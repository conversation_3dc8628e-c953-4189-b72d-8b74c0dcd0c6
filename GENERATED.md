# book-transfer-customer-account-service

## Tokens

This application uses the following tokens in it's source code. Each token should be represented by a prompt in the cooresponding generator.

-   book-transfer-customer-account-service - Name of your project
-   TypeScript Basic Lambda - The offering plan used to generator this project
-   swa-typescript-lambda-pattern - The offering plan key used to generator this project
-   <EMAIL> - Support email address of your project
-   development - Support email address of your project
-   72EE4F67-CB69-4924-AC28-DC2AF0EFB61D - Support email address of your project

## Project Structure

**[/lib/service/runtime/handler.ts](lib/service/runtime/handler.ts)** - Handler used by Lambda<br>
**[/test/event](event/)** - Sample API Gateway Events that can be used to simulate invoking your lambda through api gateway locally<br>
**[/bin/config.ts](bin/config.ts)** - Modifiable configuration parameters for your deployed lambda<br>
**[/lib/service/infrastructure.ts](lib/service/infrastructure.ts)** - The stack definition for your lambda & all of its associated resources.<br>
**[/bin/app.ts](bin/book-transfer-customer-account-service.ts)** - The executable file that CDK will build your stack(s) from.<br>
**[/bin/deployment.ts](bin/deployment.ts)** - The file that bundles your CDK infrastructure into a stack.<br>
**[/bamboo-specs/\*\*](bamboo-specs/)** - Definiton of your build/deploy plans for the bamboo pipeline.<br>

# Lambda_Observability

## Enabling Datadog

Out-of-the-box, this lambda is instrumented with Datadog constructs for monitoring lambdas through their platform.

By default, the [APM](https://www.datadoghq.com/product/apm/) (Traces/Logs) feature is disabled but can be enabled by simply setting:

_lib/stack-configuration.ts_

```
...
ddEnable: true
...
```

<a name="what-is-observability"></a>

## What is Observability?

_"Observability is the ability to measure the internal states of a system by examining its outputs. A system is considered "observable" if the current state can be estimated by only using information from outputs, namely sensor data."_

Observability involves gathering different types of data about all components within a system which helps to establish the **"Why?"** rather than just the **"What went wrong?"**.

The acronym **_M.E.L.T_** is used to define four essential data types: **Metrics**, **Events**, **Logs** & **Traces** which are used as the key pillars for observability data.

<a name="0bservability-vs. Monitoring"></a>

## Observability vs. Monitoring

What is the difference between observability and monitoring?

Simple, monitoring is the task of collecting and displaying observability data (M.E.L.Ts).

Observability is achieved when the current state of the application/function can be determined from the data collected from monitoring.

## Why is Observability important?

To run CI/CD effectively, teams need feedback from their deployed applications/functions since there is a need to know whether a recent release improved the application's performance, reliability, quality, etc. or if there was a negative impact from the release.

## Key Observability Pillars (M.E.L.T)

-   **M**etrics - An aggregated set of measurements grouped or collected at regular intervals related to deployed application (ex. CPU %)
-   **E**vents - Discrete actions happening at a moment in time. Often confused with logs, events contain a higher level of abstraction than the level of detail provided by logs. Logs record everything, whereas events are records of selected significant things (ex. completed order)
-   **L**ogs - Lines of text produced by applications. Logs provide high-fidelity data and detailed context around an event
-   **T**races - Samples of chains events between different components in a microservices ecosystem

## Key AWS Lambda Metrics To Monitor

### Function Utilization and Performance Metrics

AWS Lambda automatically tracks utilization and performance metrics for your functions. Monitoring this data can help you optimize your functions and manage costs.

| Question                                                        | Metric                                  | Description                                                                                                                                                             |
| --------------------------------------------------------------- | --------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| How long are my functions running?                              | **aws.lambda.duration**                 | Measures the average elapsed wall clock time from when the function code starts executing as a result of an invocation to when it stops executing. Shown as millisecond |
| What is the the billable time of my functions?                  | **aws.lambda.enhanced.billed_duration** | Measures the billed amount of time the function ran for (100ms increments)                                                                                              |
| How much memory is allocated by my functions?                   | **aws.lambda.memorysize**               | Measures the amount of allocated memory available to the function during execution                                                                                      |
| What is the maximum amount of memory that my functions consume? | **aws.lambda.enhanced.max_memory_used** | Measures the maximum amount of memory (mb) used by the function                                                                                                         |
| What errors are my functions throwing?                          | **aws.lambda.errors**                   | Measures the number of invocations that failed due to errors in the function (response code 4XX)                                                                        |

### Invocation Metrics

Monitoring invocation can help to determine utilization of your functions along with gaining insight into other invocation-related issues

| Question                                                                                                             | Metric                            | Description                                                                                                        |
| -------------------------------------------------------------------------------------------------------------------- | --------------------------------- | ------------------------------------------------------------------------------------------------------------------ |
| How many times are my functions being invoked?                                                                       | **aws.lambda.invocations**        | The number of times a function was invoked by either an API call or an event response from another AWS service     |
| If my function interacts with a stream-based resource, when was the last record received?                            | **aws.lambda.iterator_age**       | Measures the age of the last record for each batch of records processed                                            |
| For my async or event source mapped functions, how many failures are produced from sending to the dead letter queue? | **aws.lambda.dead_letter_errors** | Measures the sum of times Lambda is unable to write the failed event payload to your configured Dead Letter Queues |

### Concurrency Metrics

Monitoring concurrency can help you manage overprovisioned functions and scale your functions to support the flow of application traffic. By default, Lambda provides a pool of 1,000 concurrent executions per region, which are shared by all of your functions in that region.

| Question                                                      | Metric                                          | Description                                                                                                                                                        |
| ------------------------------------------------------------- | ----------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| What is the throttling rate of my function?                   | **aws.lambda.throttles**                        | Measures the number of Lambda function invocation attempts that were throttled due to invocation rates exceeding the customer's concurrent limits (error code 429) |
| How many concurrent executions are my functions experiencing? | **aws.lambda.concurrent_executions**            | Measures the average of concurrent executions for a given function at a given point in time                                                                        |
| How much concurrency reserves are left for my functions?      | **aws.lambda.unreserved_concurrent_executions** | Measures the sum of the concurrency of the functions that don't have a custom concurrency limit specified                                                          |

### Additional Key Metric Insight

See [Key metrics for monitoring AWS Lambda](https://www.datadoghq.com/blog/key-metrics-for-monitoring-aws-lambda/)

## How does SWA embed observability into AWS Lambdas

In order to increase the observability of the AWS Lambdas provisioned via the Software Accelerator platform, the SWA Team has instrumented certain lambda patterns into the DataDog observability platform via lambda layers and CDK constructs.

### Datadog (APM Platform) Serverless Integration Overview

DataDog Site: [http://jump.lmig.com/datadog](http://jump.lmig.com/datadog)

![DD_Diagram](https://datadog-docs.imgix.net/images/serverless/serverless_monitoring_installation_instructions.7575f746ca248d623aee1ee646325460.png?auto=format)

### Instrumenting NodeJs & Python Lambdas with Datadog CDK Constructs

Instrumenting serverless NodeJs/Python functions is fairly straight-forward thanks to the `datadog-cdk-construct` library which automatically adds the Datadog Lambda Library to your functions using Lambda Layers, and configures your functions to send metrics, traces, and logs to Datadog through the Datadog Lambda Extension.

> **NOTE: The Datadog constructs currently on support Python and NodeJs functions**

<br><br>

**Step 1: Install Datadog dependencies in the root package.json file (/package.json)**

```json
"datadog-cdk-constructs": "0.4.0",
```

[Latest Version and More Info on Datadog CDK Constructs](https://www.npmjs.com/package/datadog-cdk-constructs)

<br>

**Step 2: Add the Datadog construct to your stack (lib/service/infrastructure.ts):**

```typescript
import * as cdk from "@aws-cdk/core";
import { Datadog } from "datadog-cdk-constructs";

class CdkStack extends cdk.Stack {
  constructor(scope: cdk.Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    const datadog = new Datadog(this, "Datadog", {
        nodeLayerVersion: 62,
        extensionLayerVersion: 9,
        apiKey: <DATADOG_API_KEY>  //bamboo_secret_datadog_api_key
    });
    datadog.addLambdaFunctions([<LAMBDA_FUNCTIONS>]);
  }
}
```

-   [Latest Python Version](https://github.com/DataDog/datadog-lambda-python/releases)
-   [Latest Node Version](https://github.com/DataDog/datadog-lambda-js/releases)
-   [Latest Extensions Layer Version](https://docs.datadoghq.com/serverless/installation/nodejs/?tab=custom#install-the-datadog-lambda-extension)

<br>

**Step 3: Enable Lambda Tracing:**

Next, you would enable tracing on each lambda since Datadog fetches those traces from XRay for visibility in its platform:

![DD_Tracing](https://datadog-docs.imgix.net/images/serverless/serverless_tracing_installation_instructions.56283d0e016ea16534dd1e11a3157e77.png?auto=format)

Example:

```typescript
import { Tracing } from '@aws-cdk/aws-lambda';


new NodejsFunction(this, 'Function', {
			...
			tracing: Tracing.ACTIVE,
			...
		});
```

<br>

**Step 4: Configure Lambda environment variables and tags:**

Metadata tags are used for filtering in Datadog and the `/bin/config.ts` environment variables are used for the agent/extension configurations.

_Step 5.1_

<br>
File: `/bin/config.ts`

Add:

```
    DD_ENV: environmentKey, //This sets the env tag in Datadog. Use it to separate out your staging, development, and production environments.
    DD_SERVICE: appName, //This sets the service tag in Datadog. Use it to group related Lambda functions into a service.
    DD_VERSION: version, //This sets the version tag in Datadog. Use it to enable Deployment Tracking.
```

_Step 5.2_

<br>
File: `/bin/deployment.ts`

Add:

```typescript
const serviceResources = new ServiceResources(cdkStack, 'Service', {
    tags: {
        //Datadog Specifc Tags
        service: StackConfiguration.name,
        env: StackConfiguration.environmentKey,
        version: StackConfiguration.version,
        swa: StackConfiguration.swa,
    },
});
```

**Note:** Also ensure that you are including the `StackConfiguration` environment variables in your function:

```typescript
new NodejsFunction(this, 'HandlerFunction', {
			...
			environment,
			...
		});
```

<br>

**Step 5: View realtime metrics in the `SWA AWS Lambdas` dashboard:**

After deploying and invoking the lambda several times, you should start to see (3-5 min.) the enhanced metrics and traces appear for your lambda in the SWA dashboard (below)

<br>

### "SWA AWS Lambdas" Dashboard

An out-of-the-box dashboard view of your lambda key metrics is provided via a dynmamically generated URL in the deployment logs.

> Example: `swaexamplejavalambdadevelopmentDashboard = "https://app.datadoghq.com/dashboard/9cb-et9-v7c?tpl_var_Service=<<Stackconfiguration.name>>"`

With the Datadog integration you also get a set of other _enhanced metrics_ that you can use to query and view data within the Datadog dashboards. For a full list of the enhanced metrics, see [here](https://docs.datadoghq.com/serverless/enhanced_lambda_metrics/)

<br> 

## Power-Tune lambdas

"AWS Lambda Power Tuning is a state machine powered by AWS Step Functions that helps you optimize your Lambda functions for cost and/or performance in a data-driven way.

The state machine is designed to be easy to deploy and fast to execute. Also, it's language agnostic so you can optimize any Lambda functions in your account.

Basically, you can provide a Lambda function ARN as input and the state machine will invoke that function with multiple power configurations (from 128MB to 10GB, you decide which values). Then it will analyze all the execution logs and suggest you the best power configuration to minimize cost and/or maximize performance."

[Lambda Power Tuner](https://catalyst-catalog.lmig.com/#/lambda-power-tuner)

## VPC Based Lambda(s)

By Default, you're given a VPC based Lambda from the generator. This allows you to connect to other VPC based resources in AWS, however, that may not be necessary for all use cases.

If you choose to keep your Lambda Functions in the VPC, your function will create a **new security group** in it's definition. If you're creating multiple functions, you should reuse any existing Security Groups in order to reduce the number of HyperPlane ENIs created in our AWS accounts. By using the [SecurityGroupCustomResource](https://git.forge.lmig.com/projects/SF1/repos/swa-cdk-core/browse/src/custom-resource/resource-lookup/security-group-custom-resource.ts), you can lookup security groups by name within the same account to help with reusing Security Groups between Lambda Functions.

You can choose to remove your Lambda from the VPC by removing the following properties from the Function props in [lib/stack.ts](lib/stack.ts):

```ts
    vpc,
    vpcSubnets: {
        subnets: subnets
    },
    securityGroups: [
        new SecurityGroup(this, "SG", {
            vpc,
            allowAllOutbound: true,
            description: StackConfiguration.name + "-sg",
            securityGroupName: StackConfiguration.name + "-sg",
        })
    ],
```

You can find more information about Serverless Architecture Guidelines, HyperPlane ENIs, VPC based Lambdas, etc. [here](https://forge.lmig.com/wiki/display/CSS/AWS+Lambda+Serverless+Architecture+Network+Guidelines)

## Adding this lambda to an API Gateway

1. You can import the lambda function into your API Gateway Stack

    ```ts
    const myFunction = Function.fromFunctionArn(this, 'LambdaFunction', `arn:aws:lambda:${Aws.REGION}:${Aws.ACCOUNT_ID}:function:book-transfer-customer-account-service-${StackConfiguration.environmentKey}`);
    ```

2. You can create a new path that you want to the Lambda Function:
    ```ts
    apiGateway.root.resourceForPath('greetings/greeting').addMethod('GET', new LambdaIntegration(myFunction));
    ```
