{"name": "book-transfer-customer-account-service", "version": "0.1.0", "author": "<PERSON>.<EMAIL>", "bin": {"cdk-app": "bin/app.ts"}, "engines": {"npm": "^9.5.1", "node": "22"}, "scripts": {"build": "tsc", "watch": "tsc -w", "synth": "cdk synth --no-staging > template.yml", "start": "sam local start-api --template ./template.yml --debug -d 5800  --region us-east-1", "local": "npm run clean && npm run build && npm run synth && npm run start", "test": "jest --coverage --passWithNoTests", "testlocal": "cross-env IS_LOCAL='true' npm run test", "clean": "tsc --build --clean", "deploy": "cdk deploy '*' --require-approval 'never'"}, "devDependencies": {"@aws-cdk/aws-lambda-python-alpha": "^2.63.0-alpha.0", "@aws-sdk/client-kms": "^3.592.0", "@lmig/bt-cdk-common": "^1.0.1", "@lmig/swa-cdk-authorizers": "^4.3.0", "@lmig/swa-cdk-core": "^4.10.0", "@types/aws-lambda": "8.10.40", "@types/jest": "29.5.0", "@types/lodash": "4.14.179", "@types/node": "^22.10.7", "aws-cdk": "^2.176.0", "aws-cdk-lib": "^2.176.0", "axios-mock-adapter": "^2.0.0", "constructs": "10.0.75", "cross-env": "^7.0.3", "cross-spawn": "^7.0.6", "datadog-cdk-constructs-v2": "^1.8.1", "dotenv": "^16.4.7", "esbuild": "^0.25.4", "husky": "7.0.4", "jest": "29.5.0", "jest-extended": "3.2.4", "jest-junit": "15.0.0", "mongodb": "^6.7.0", "mongodb-memory-server": "^9.3.0", "source-map-support": "0.5.21", "ts-jest": "29.1.0", "ts-loader": "6.2.1", "ts-node": "10.9.1", "typescript": "5.0.3"}, "dependencies": {"@aws-lambda-powertools/logger": "^2.13.1", "@aws-lambda-powertools/metrics": "^2.13.1", "@aws-lambda-powertools/tracer": "^2.13.1", "@lmig/bt-cdk-common": "1.0.3", "@lmig/audit-log-node-starter": "1.0.5", "@middy/core": "^6.1.5", "aws-xray-sdk": "^3.10.3", "axios": "^1.7.7", "axios-retry": "^4.5.0", "lodash": "^4.17.21", "middy": "^0.24.0", "pino": "^9.4.0", "winston": "^3.14.2"}}