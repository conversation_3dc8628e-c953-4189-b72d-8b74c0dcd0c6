module.exports = {
  preset: 'ts-jest',
  reporters: ['default', ['jest-junit', { outputDirectory: 'test-reports/' }]],
  testEnvironment: 'node',
  projects: ['<rootDir>'],
  coveragePathIgnorePatterns: ['test/util', 'cdk.out'],
  testPathIgnorePatterns: ['test/util', 'cdk.out'],
  testMatch: ['<rootDir>/test/**/*.spec.ts'],
  coverageDirectory: 'test-reports/',
  setupFiles: ['./setEnvVars.js'],
};
