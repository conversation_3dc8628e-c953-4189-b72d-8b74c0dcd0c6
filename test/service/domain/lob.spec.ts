import { LineOfBusiness } from '../../../model/lob';
import { EventSource } from '../../../types/enums';

describe('LineOfBusiness.fromString', () => {
  
  test('should return empty if value is empty string', () => {
    const result = LineOfBusiness.fromString('', EventSource.AQE);
    expect(result).toBe('');
  });

  test('should return the correct LineOfBusiness for a valid value and event source', () => {
    const result = LineOfBusiness.fromString('AUTO', EventSource.AQE);
    expect(result).toBe(LineOfBusiness.AUTOP.displayName);
  });

  test('should return the correct LineOfBusiness for a valid value in different case', () => {
    const result = LineOfBusiness.fromString('auto', EventSource.AQE);
    expect(result).toBe(LineOfBusiness.AUTOP.displayName);
  });

  test('should return the passed value for an invalid value', () => {
    const result = LineOfBusiness.fromString('INVALID', EventSource.AQE);
    expect(result).toBe('INVALID');
  });

  test('should return the correct LineOfBusiness for a valid value and different event source', () => {
    const result = LineOfBusiness.fromString('Workers Comp', EventSource.SALESFORCE);
    expect(result).toBe(LineOfBusiness.WORK.displayName);
  });

  test('should return the correct LineOfBusiness for farm salesforce value', () => {
    const result = LineOfBusiness.fromString('Farm', EventSource.SALESFORCE);
    expect(result).toBe(LineOfBusiness.FARM.displayName);
  });

  test('should return the correct LineOfBusiness for Marine salesforce value', () => {
    const result = LineOfBusiness.fromString('Marine', EventSource.SALESFORCE);
    expect(result).toBe(LineOfBusiness.INMRC.displayName);
  });

  test('should return the correct LineOfBusiness for bond bat value', () => {
    const result = LineOfBusiness.fromString('Bond', EventSource.BAT);
    expect(result).toBe(LineOfBusiness.BOND.displayName);
  });

  test('should return the correct LineOfBusiness for a valid value and BAT event source', () => {
    const result = LineOfBusiness.fromString('Worker\'s Comp', EventSource.BAT);
    expect(result).toBe(LineOfBusiness.WORK.displayName);
  });
});