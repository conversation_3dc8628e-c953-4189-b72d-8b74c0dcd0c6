import { Collection, DeleteResult, MongoClient, ObjectId, WithId } from 'mongodb';
import CustomerAccountModel, { CustomerPolicyModel } from '../../../model/customerAccountModel';

export const getAllCustomerAccountsFromMongo = (mongoClient: MongoClient): Promise<WithId<CustomerAccountModel>[]> => {
  const db = mongoClient.db('BT');
  const collection: Collection<CustomerAccountModel> = db.collection('customerAccounts');
  return collection.find({}).toArray();
};

export const getAllCustomerPoliciesFromMongo = (mongoClient: MongoClient): Promise<WithId<CustomerPolicyModel>[]> => {
  const db = mongoClient.db('BT');
  const collection: Collection<CustomerPolicyModel> = db.collection('customerPolicies');
  return collection.find({}).toArray();
};

export const insertCustomerAccountIntoMongoDB = (mongoClient: MongoClient, document: CustomerAccountModel) => {
  const db = mongoClient.db('BT');
  const collection: Collection<CustomerAccountModel> = db.collection('customerAccounts');
  return collection.insertOne(document);
};

export const insertCustomerPolicyIntoMongoDB = (mongoClient: MongoClient, document: CustomerPolicyModel) => {
  const db = mongoClient.db('BT');
  const collection: Collection<CustomerPolicyModel> = db.collection('customerPolicies');
  return collection.insertOne(document);
};

export const deleteAllMongoDBData = (mongoClient: MongoClient): Promise<DeleteResult[]> => {
  const db = mongoClient.db('BT');
  return Promise.all([
    db.collection('customerAccounts').deleteMany({}), 
    db.collection('customerPolicies').deleteMany({})
  ]);
};
