import { Context } from 'aws-lambda';
import * as Hand<PERSON> from '../../../lib/service/runtime/getCustomerAccountHandler';
import * as TestUtil from './util/test-util';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { MongoClient, ObjectId } from 'mongodb';
import { decryptSecret } from '../../../lib/service/utility/decryptSecret';
import { throwError } from '../../../lib/service/utility/throwError';
import CustomerAccountModel, { CustomerMetadata, CustomerMetadataMap, CustomerMetadataModel } from '../../../model/customerAccountModel';
import {
  deleteAllMongoDBData,
  getAllCustomerAccountsFromMongo,
  getAllCustomerPoliciesFromMongo,
  insertCustomerAccountIntoMongoDB,
} from './mongo-test-util';

jest.mock('../../../lib/service/utility/decryptSecret');
jest.mock('../../../lib/service/utility/throwError');

let context: Context;
let mongoServer: MongoMemoryServer;
let mongoClient: MongoClient;

const envBackup: { [key: string]: string | undefined } = process.env;

beforeAll(async () => {
  jest.resetModules();

  // creating MongoDB in memory
  mongoServer = await MongoMemoryServer.create();

  // backing up original env variables
  process.env = { ...envBackup };

  // generating connection string to the mongodb memory server and assigning to env variable
  process.env.MONGODB_CONNECTION = mongoServer.getUri();

  // creating a MongoClient instance for use in our test cases
  mongoClient = new MongoClient(process.env.MONGODB_CONNECTION!);

  // mocking the decryptSecret function here so that when called in the Lambda, it will use this connection string instead.
  (decryptSecret as jest.Mock).mockReturnValue(Promise.resolve(process.env.MONGODB_CONNECTION));

  // mocking error response
  (throwError as jest.Mock).mockReturnValue(new Error('mock error'));
  // (error as jest.Mock).mockImplementation(() => { new Error('mock error') });
});

afterAll(async () => {
  await mongoClient.close();
  await mongoServer.stop();

  // restoring original env variables
  process.env = envBackup;
});

beforeEach(() => {
  context = TestUtil.getHandlerContext();
});

afterEach(async () => {
  console.log('customerAccounts:', await getAllCustomerAccountsFromMongo(mongoClient));
  console.log('customerPolicies:', await getAllCustomerPoliciesFromMongo(mongoClient));
  await deleteAllMongoDBData(mongoClient);
});

test('customerAccount found', async () => {
  const createdDate = new Date('2023-09-11T14:10:30.000+0000');
  let customerMetadataMap: CustomerMetadataMap<string, CustomerMetadataModel> = {
    'AQE': {
      customerMetadata: {
        btCode: 2,
        customerName: 'Test Customer',
        address1: '1234 Test Lane',
        address2: '',
        city: 'Gotham',
        state: 'NY',
        zip: '*********',
        addressLastUpdatedDate: createdDate,
      } as CustomerMetadata,
      lastUpdatedDate: createdDate,
      createdDate: createdDate,
    } as CustomerMetadataModel
  };
  const customerAccount: CustomerAccountModel = {
    customerMetadataMap: customerMetadataMap,
    ecliqAccounts: [
      {
        ecliqAccountNumber: '12345',
        ecliqAccountCreationDate: new Date(),
      },
    ],
    lastUpdatedDate: createdDate,
    createdDate: createdDate,
  };
  const insertedCustomerAccount = await insertCustomerAccountIntoMongoDB(mongoClient, customerAccount);

  const response = await Handler.getCustomerAccountLambda(
    {
      pathParameters: {
        accountId: insertedCustomerAccount.insertedId.toString(),
      },
    },
    context,
  );

  expect(response.statusCode).toEqual(200);
  expect(response.body).toEqual(JSON.stringify({ _id: insertedCustomerAccount.insertedId, ...customerAccount }));
});

test('customerAccount not found', async () => {
  const response = await Handler.getCustomerAccountLambda(
    {
      pathParameters: {
        accountId: new ObjectId().toString(),
      },
    },
    context,
  );

  expect(response.statusCode).toEqual(404);
});

test('accountId not provided', async () => {
  const response = await Handler.getCustomerAccountLambda(
    {
      pathParameters: {},
    },
    context,
  );

  expect(response.statusCode).toEqual(400);
  const parsedResponse: { data: any[]; error: string } = JSON.parse(response.body);
  expect(parsedResponse.error).toEqual('Missing accountId in pathParameters');
});

test('error during fetch', async () => {
  const response = await Handler.getCustomerAccountLambda(
    {
      pathParameters: {
        accountId: 'this ObjectId string is invalid',
      },
    },
    context,
  );

  expect(response.statusCode).toEqual(500);
  const parsedResponse: { data: any[]; error: string } = JSON.parse(response.body);
  expect(parsedResponse.error).toContain('There was an error');
});
