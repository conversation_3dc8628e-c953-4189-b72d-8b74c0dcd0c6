import { APIGatewayProxyResult, Context } from 'aws-lambda';
import * as Handler from '../../../lib/service/runtime/addCustomerPolicyHandler';
import * as TestUtil from './util/test-util';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { MongoClient, WithId, ObjectId } from 'mongodb';
import { decryptSecret } from '../../../lib/service/utility/decryptSecret';
import { throwError } from '../../../lib/service/utility/throwError';
import { AuditLogHelper } from '../../../lib/service/utility/auditLogHelper';
import Request, { CustomerMetadata, CustomerPolicy } from '../../../types/request';
import {
  deleteAllMongoDBData,
  getAllCustomerAccountsFromMongo,
  getAllCustomerPoliciesFromMongo,
  insertCustomerAccountIntoMongoDB,
  insertCustomerPolicyIntoMongoDB,
} from './mongo-test-util';
import CustomerAccountModel, { CustomerMetadataMap, CustomerMetadataModel, CustomerPolicyModel } from '../../../model/customerAccountModel';
import Response from '../../../types/response';
import { TransactionResponse } from '@lmig/audit-log-node-starter';
import { create } from 'lodash';

jest.mock('../../../lib/service/utility/decryptSecret');
jest.mock('../../../lib/service/utility/throwError');
jest.mock('../../../lib/service/utility/authenticationTokenHelper');

jest.mock('@lmig/audit-log-node-starter', () => {
    return {
        ...jest.requireActual('@lmig/audit-log-node-starter'),
        AuditLogService: {
            initialize: jest.fn().mockResolvedValue({
                startTransaction: jest.fn(),
                addTransactionEvent: jest.fn(),
                endTransaction: jest.fn(),
                createTransaction: jest.fn(),
            }),
        },
    };
});

let context: Context;
let mongoServer: MongoMemoryServer;
let mongoClient: MongoClient;

const envBackup: { [key: string]: string | undefined } = process.env;

beforeAll(async () => {
  jest.resetModules();

  // creating MongoDB in memory
  mongoServer = await MongoMemoryServer.create();

  // backing up original env variables
  process.env = { ...envBackup };

  // generating connection string to the mongodb memory server and assigning to env variable
  process.env.MONGODB_CONNECTION = mongoServer.getUri();

  // creating a MongoClient instance for use in our test cases
  mongoClient = new MongoClient(process.env.MONGODB_CONNECTION!);

  // mocking the decryptSecret function here so that when called in the Lambda, it will use this connection string instead.
  (decryptSecret as jest.Mock).mockReturnValue(Promise.resolve(process.env.MONGODB_CONNECTION));

  // mocking error response
  (throwError as jest.Mock).mockReturnValue(new Error('mock error'));
  // (error as jest.Mock).mockImplementation(() => { new Error('mock error') });
});

afterAll(async () => {
  if (mongoClient) {
    await mongoClient.close();
  }
  if (mongoServer) {
    await mongoServer.stop();
  }

  // restoring original env variables
  process.env = envBackup;
});

async function initializeAuditLogMock(
  methodName: "startTransaction" | "addTransactionEvent" | "endTransaction" | "createTransaction"
) {
  const auditLogService = (await AuditLogHelper.init()).auditLogService;

  if (!auditLogService) {
    throw new Error('auditLogService is null');
  }

  return jest.spyOn(auditLogService, methodName);
}

beforeEach(() => {
  context = TestUtil.getHandlerContext();
  jest.clearAllMocks();
});

afterEach(async () => {
  await deleteAllMongoDBData(mongoClient);
}, 20000);

// =====================================================================
// Initial Validation
// =====================================================================

describe('initial validation', () => {
  test('given a request with no eventSource, when processed, then expect a 400', async () => {
    const event = {} as Request
    event.transactionId = 'transaction-id';
    const startTransactionSpy = await initializeAuditLogMock('startTransaction');
    startTransactionSpy.mockResolvedValueOnce({ id: 'transaction-id' } as TransactionResponse);
    const createTransactionSpy = await initializeAuditLogMock('createTransaction');
    const addCustomerResponse = await Handler.addCustomerPolicyLambda(event, context);
    sleep(1000); // wait for the transaction to complete
    // verify the response
    expect(addCustomerResponse.statusCode).toEqual(200);
    // verify the error message
    const response: Response = JSON.parse(addCustomerResponse.body).data;
    expect(response.statusCode).toEqual(400);
    expect(response.error).toEqual('Missing event source');
    // verify the data in MongoDB
    const mongoData = await getAllCustomerAccountsFromMongo(mongoClient);
    expect(mongoData.length).toEqual(0);
    //expect(startTransactionSpy).toHaveBeenCalledTimes(1);
    expect(createTransactionSpy).toHaveBeenCalledTimes(1);
  });

  test('given a request with no searchCriteria, when processed, then expect a 400', async () => {
    const event = {
        eventSource: 'BAT',
    } as Request;

    const addCustomerResponse = await Handler.addCustomerPolicyLambda(event, context);

    // verify the response
    expect(addCustomerResponse.statusCode).toEqual(200);
    // verify the error message
    const response: Response = JSON.parse(addCustomerResponse.body).data;
    expect(response.statusCode).toEqual(400);
    expect(response.error).toEqual('Missing search criteria');
    // verify the data in MongoDB
    const mongoData = await getAllCustomerAccountsFromMongo(mongoClient);
    expect(mongoData.length).toEqual(0);
  });

});

// =====================================================================
// Account Creation
// =====================================================================

describe('account creation', () => {
  test('given a request with no customerMetadata, when processed, then expect a 400', async () => {
    const request: Request = {
      searchCriteria: {
        customerAccountId: '667c41a04162ebf292811cc2',
      },
      // no customerMetadata
      eventSource: 'BAT',
      customerPolicies: [],
      ecliqAccounts: [],
    };

    const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

    // verify the response
    expect(addCustomerResponse.statusCode).toEqual(200);
    // verify the error message
    const response: Response = JSON.parse(addCustomerResponse.body).data;
    expect(response.statusCode).toEqual(400);
    expect(response.error).toEqual('Missing customerMetadata');
    // verify the data in MongoDB
    const mongoData = await getAllCustomerAccountsFromMongo(mongoClient);
    expect(mongoData.length).toEqual(0);
  });

  test(
    'given 1 customerMetadata with a policy number and a lob, ' +
      'when processed successfully, ' +
      'then expect MongoDB to have 1 record with policy number and lob and success response',
    async () => {

      const eventSource = 'AQE';

      const customerPolicy: CustomerPolicy = {
        priorPolicy: {
          lineOfBusiness: 'AUTOB',
          policyNumber: '1',
        },
        aqe: {
          opportunityId: 123456,
          status: "2",
          businessType: "AUTOB",
          nNumber: "n999999"
        }
      };
      const request: Request = {
        transactionId: 'transaction-id',
        searchCriteria: {
          customerAccountId: '667c41a04162ebf292811cc2',
        },
        customerMetadata: {
          btCode: 1,
          customerName: 'Test Customer',
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
        },
        eventSource: eventSource,
        customerPolicies: [customerPolicy],
        ecliqAccounts: [],
      };

      const createTransactionSpy = await initializeAuditLogMock('createTransaction');

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

      // verify the response
      expect(addCustomerResponse.statusCode).toEqual(200);
      const response: Response = JSON.parse(addCustomerResponse.body).data;
      expect(response.success).toEqual('Completed processing');
      expect(typeof response.id).toEqual('string');
      // verify the data in MongoDB
      const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
      const mongoCustomerPolicies = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerAccounts.length).toEqual(1);
      expect(typeof mongoCustomerAccounts[0]._id).toEqual('object');
      expect(typeof mongoCustomerAccounts[0].createdDate).toEqual('object');
      expect(typeof mongoCustomerAccounts[0].lastUpdatedDate).toEqual('object');
      expect(mongoCustomerPolicies.length).toEqual(1);
      expect(mongoCustomerPolicies[0].priorPolicy?.policyNumber).toEqual('1');
      expect(mongoCustomerPolicies[0].priorPolicy?.lineOfBusiness).toEqual('AUTOB');
      expect(mongoCustomerPolicies[0].aqe?.opportunityId).toEqual(123456);
      expect(mongoCustomerPolicies[0].aqe?.status).toEqual('2');
      expect(mongoCustomerPolicies[0].aqe?.businessType).toEqual('AUTOB');
      expect(mongoCustomerPolicies[0].aqe?.nNumber).toEqual('n999999');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.btCode).toEqual(1);
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.customerName).toEqual('Test Customer');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.address1).toEqual('1234 Test Lane');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.address2).toEqual('');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.city).toEqual('Gotham');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.state).toEqual('NY');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.zip).toEqual('*********');
      expect(typeof mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.addressLastUpdatedDate).toEqual('object');
      expect(createTransactionSpy).toHaveBeenCalledTimes(2);
    },
  );

  test(
    'given 2 customer policy records, ' +
      'when processed successfully, ' +
      'then expect MongoDB to have one account with both customer policies',
    async () => {
      const eventSource = 'AQE';
      const customerPolicy1: CustomerPolicy = {
        priorPolicy: {
          lineOfBusiness: 'AUTOB',
          policyNumber: '1',
        },
      };
      const customerPolicy2: CustomerPolicy = {
        priorPolicy: {
          lineOfBusiness: 'AUTOB',
          policyNumber: '2',
        },
      };
      const request: Request = {
        searchCriteria: {
          customerAccountId: '667c41a04162ebf292811cc2',
          customerMetadata: {
            btCode: 1,
            customerName: 'Test Customer',
            address1: '1234 Test Lane',
            address2: '',
            city: 'Gotham',
            state: 'NY',
            zip: '*********',
          },
        },
        customerMetadata: {
          btCode: 1,
          customerName: 'Test Customer',
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
        },
        eventSource: eventSource,
        customerPolicies: [customerPolicy1, customerPolicy2],
        ecliqAccounts: [],
      };

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

      // verify the response
      expect(addCustomerResponse.statusCode).toEqual(200);
      const response: Response = JSON.parse(addCustomerResponse.body).data;
      expect(response.success).toEqual('Completed processing');
      expect(typeof response.id).toEqual('string');
      // verify the data in MongoDB
      const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
      const mongoCustomerPolicies = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerAccounts.length).toEqual(1);
      expect(typeof mongoCustomerAccounts[0]._id).toEqual('object');
      expect(typeof mongoCustomerAccounts[0].createdDate).toEqual('object');
      expect(typeof mongoCustomerAccounts[0].lastUpdatedDate).toEqual('object');
      expect(mongoCustomerPolicies.length).toEqual(2);
      expect(mongoCustomerPolicies[0].priorPolicy?.policyNumber).toEqual('1');
      expect(mongoCustomerPolicies[0].priorPolicy?.lineOfBusiness).toEqual('AUTOB');
      expect(mongoCustomerPolicies[1].priorPolicy?.policyNumber).toEqual('2');
      expect(mongoCustomerPolicies[1].priorPolicy?.lineOfBusiness).toEqual('AUTOB');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.btCode).toEqual(1);
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.customerName).toEqual('Test Customer');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.address1).toEqual('1234 Test Lane');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.address2).toEqual('');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.city).toEqual('Gotham');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.state).toEqual('NY');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.zip).toEqual('*********');
      expect(mongoCustomerPolicies[0].btCode).toEqual(1);
      expect(mongoCustomerPolicies[1].btCode).toEqual(1);
      expect(typeof mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.addressLastUpdatedDate).toEqual('object');
    },
  );

  test(
    'given 2 customer policy records, ' +
      'when processed successfully, ' +
      'then expect MongoDB to have one account with both customer policies',
    async () => {
      const eventSource = 'AQE';
      const customerPolicy1: CustomerPolicy = {
        priorPolicy: {
          lineOfBusiness: 'AUTOB',
          policyNumber: '1',
        },
      };
      const customerPolicy2: CustomerPolicy = {
        priorPolicy: {
          lineOfBusiness: 'AUTOB',
          policyNumber: '2',
        },
      };
      const request: Request = {
        searchCriteria: {
          customerAccountId: '667c41a04162ebf292811cc2',
          customerMetadata: {
            btCode: '123456',
            customerName: 'Test Customer',
            address1: '1234 Test Lane',
            address2: '',
            city: 'Gotham',
            state: 'NY',
            zip: '*********',
          },
        },
        customerMetadata: {
          btCode: '123456',
          customerName: 'Test Customer',
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
        },
        eventSource: eventSource,
        customerPolicies: [customerPolicy1, customerPolicy2],
        ecliqAccounts: [],
      };

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

      // verify the response
      expect(addCustomerResponse.statusCode).toEqual(200);
      const response: Response = JSON.parse(addCustomerResponse.body).data;
      expect(response.success).toEqual('Completed processing');
      expect(typeof response.id).toEqual('string');
      // verify the data in MongoDB
      const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
      const mongoCustomerPolicies = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerAccounts.length).toEqual(1);
      expect(typeof mongoCustomerAccounts[0]._id).toEqual('object');
      expect(typeof mongoCustomerAccounts[0].createdDate).toEqual('object');
      expect(typeof mongoCustomerAccounts[0].lastUpdatedDate).toEqual('object');
      expect(mongoCustomerPolicies.length).toEqual(2);
      expect(mongoCustomerPolicies[0].priorPolicy?.policyNumber).toEqual('1');
      expect(mongoCustomerPolicies[0].priorPolicy?.lineOfBusiness).toEqual('AUTOB');
      expect(mongoCustomerPolicies[1].priorPolicy?.policyNumber).toEqual('2');
      expect(mongoCustomerPolicies[1].priorPolicy?.lineOfBusiness).toEqual('AUTOB');

      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.btCode).toEqual(123456);
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.customerName).toEqual('Test Customer');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.address1).toEqual('1234 Test Lane');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.address2).toEqual('');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.city).toEqual('Gotham');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.state).toEqual('NY');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.zip).toEqual('*********');
      expect(typeof mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.addressLastUpdatedDate).toEqual('object');
    },
  );

  test(
    'Request has 1 customer account data and SearchCriteria BT code and CustomerMetadata BT code values are different, ' +  
    'when processed successfully, then expect MongoDB to have one account created successfully with BT code value associated to customer metadata',
    async () => {

      const eventSource = 'AQE';
      const customerPolicy1: CustomerPolicy = {
        btCode: '123456',
        priorPolicy: {
          lineOfBusiness: 'AUTOB',
          policyNumber: '1',
        },
      };
      const request: Request = {
        searchCriteria: {
          customerAccountId: '667c41a04162ebf292811cc2',
          btCode: '654321',
          customerMetadata: {
            btCode: '123456',
            customerName: 'Test Customer',
            address1: '1234 Test Lane',
            address2: '',
            city: 'Gotham',
            state: 'NY',
            zip: '*********',
          },
        },
        customerMetadata: {
          btCode: '123456',
          customerName: 'Test Customer',
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
        },
        eventSource: eventSource,
        customerPolicies: [customerPolicy1],
        ecliqAccounts: [],
      };

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

      // verify the response
      expect(addCustomerResponse.statusCode).toEqual(200);
      const response: Response = JSON.parse(addCustomerResponse.body).data;
      expect(response.success).toEqual('Completed processing');
      expect(typeof response.id).toEqual('string');

      // verify the data in MongoDB
      const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
      const mongoCustomerPolicies = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerAccounts.length).toEqual(1);
      expect(typeof mongoCustomerAccounts[0]._id).toEqual('object');
      expect(typeof mongoCustomerAccounts[0].createdDate).toEqual('object');
      expect(typeof mongoCustomerAccounts[0].lastUpdatedDate).toEqual('object');
      expect(mongoCustomerPolicies.length).toEqual(1);
      expect(mongoCustomerPolicies[0].priorPolicy?.policyNumber).toEqual('1');
      expect(mongoCustomerPolicies[0].priorPolicy?.lineOfBusiness).toEqual('AUTOB');
      expect(mongoCustomerPolicies[0].btCode).toEqual(123456);
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.btCode).toEqual(123456);
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.customerName).toEqual('Test Customer');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.address1).toEqual('1234 Test Lane');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.address2).toEqual('');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.city).toEqual('Gotham');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.state).toEqual('NY');
      expect(mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.zip).toEqual('*********');
      expect(typeof mongoCustomerAccounts[0].customerMetadataMap[eventSource]?.customerMetadata.addressLastUpdatedDate).toEqual('object');
    });


});

// =====================================================================
// Account updation
// =====================================================================
describe('account updation', () => {
  test('give a customeraccount exists for the requested customerMetadata but the request is from a different source,'
    + ' when processed, then expect the customerMetadata to be added to the map', async () => {
    const createdDate = new Date('2023-09-11T14:10:30.000+0000');
    
    await insertCustomerAccount(createdDate);

    const customerPolicy1: CustomerPolicy = {
      priorPolicy: {
        lineOfBusiness: 'AUTOB',
        policyNumber: '1',
      },
    };

    const request: Request = {
      searchCriteria: {
        customerMetadata: {
          btCode: 2,
          customerName: 'Test Customer'.toUpperCase(),
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
        },
      },
      customerMetadata: {
        btCode: 2,
        customerName: 'Test Customer',
        address1: '1234 Test Lane',
        address2: '',
        city: 'Gotham',
        state: 'NY',
        zip: '*********',
      },
      eventSource: 'BAT',
      customerPolicies: [customerPolicy1],
      ecliqAccounts: [],
    };

    // Before the update, the customerMetadataMap should have the AQE source
    const mongoCustomerAccountsBeforeUpdate = await getAllCustomerAccountsFromMongo(mongoClient);
    expect(mongoCustomerAccountsBeforeUpdate[0].customerMetadataMap['AQE']).toBeDefined();
    expect(mongoCustomerAccountsBeforeUpdate[0].customerMetadataMap['AQE'].customerMetadata.btCode).toEqual(2);
    expect(mongoCustomerAccountsBeforeUpdate[0].customerMetadataMap['BAT']).toBeUndefined();
    expect(mongoCustomerAccountsBeforeUpdate[0].ecliqAccounts.length).toEqual(1);

    const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

    // verify the response
    expect(addCustomerResponse.statusCode).toEqual(200);
    const response: Response = JSON.parse(addCustomerResponse.body).data;
    expect(response.success).toEqual('Completed processing');
    expect(typeof response.id).toEqual('string');

    const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);

    // After the update, the customerMetadataMap should have the AQE and BAT sources
    expect(mongoCustomerAccounts.length).toEqual(1);
    expect(mongoCustomerAccountsBeforeUpdate[0].ecliqAccounts.length).toEqual(1);
    expect(mongoCustomerAccounts[0].customerMetadataMap['AQE']).toBeDefined();
    expect(mongoCustomerAccounts[0].customerMetadataMap['AQE'].customerMetadata.btCode).toEqual(2);
    expect(mongoCustomerAccounts[0].customerMetadataMap['BAT']).toBeDefined();
    expect(mongoCustomerAccounts[0].customerMetadataMap['BAT']?.customerMetadata.btCode).toEqual(2);
  }, 10000);

  test('give a customeraccount exists for the requested customerMetadata and the request is from the same source,'
    + ' when processed, then expect the existing customerMetadata to be updated', async () => {
      const createdDate = new Date('2023-09-11T14:10:30.000+0000');
    await insertCustomerAccount(createdDate);

    const customerPolicy1: CustomerPolicy = {
      priorPolicy: {
        lineOfBusiness: 'AUTOB',
        policyNumber: '1',
      },
    };

    const request: Request = {
      searchCriteria: {
        customerMetadata: {
          btCode: 2,
          customerName: 'Test Customer'.toUpperCase(),
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
        },
      },
      customerMetadata: {
        btCode: 5,
        customerName: 'Test Customer',
        address1: '1234 Test Lane',
        address2: '',
        city: 'Gotham',
        state: 'NY',
        zip: '*********',
      },
      eventSource: 'AQE',
      customerPolicies: [customerPolicy1],
      ecliqAccounts: [
        {
        ecliqAccountNumber: '12346',
        ecliqAccountCreationDate: new Date(),
        },
      ],
    };

    // Before the update, the customerMetadataMap should have the AQE source and the btCode should be 2
    const mongoCustomerAccountsBeforeUpdate = await getAllCustomerAccountsFromMongo(mongoClient);
    expect(mongoCustomerAccountsBeforeUpdate[0].customerMetadataMap['AQE']).toBeDefined();
    expect(mongoCustomerAccountsBeforeUpdate[0].customerMetadataMap['AQE'].customerMetadata.btCode).toEqual(2);
    expect(mongoCustomerAccountsBeforeUpdate[0].customerMetadataMap['BAT']).toBeUndefined
    expect(mongoCustomerAccountsBeforeUpdate[0].ecliqAccounts.length).toEqual(1);
    const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

    // verify the response
    expect(addCustomerResponse.statusCode).toEqual(200);
    const response: Response = JSON.parse(addCustomerResponse.body).data;
    expect(response.success).toEqual('Completed processing');
    expect(typeof response.id).toEqual('string');

    const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);

    // After the update, the customerMetadataMap should have the AQE source and the btCode should be 5
    expect(mongoCustomerAccounts.length).toEqual(1);
    expect(mongoCustomerAccounts[0].customerMetadataMap['AQE']).toBeDefined();
    expect(mongoCustomerAccounts[0].customerMetadataMap['AQE'].customerMetadata.btCode).toEqual(5);
    expect(mongoCustomerAccounts[0].customerMetadataMap['BAT']).toBeUndefined
    expect(mongoCustomerAccounts[0].ecliqAccounts.length).toEqual(2);
  });
});

// =====================================================================
// Policy Creation
// =====================================================================
describe('Policy Creation', () => {
  test('Given a customer policy created from salesforce with no prior policy number,'
    + 'When a request came from bat and prior lob, prior expiration date and prior premium not matched with salesforce,'    
    + 'Then a new policy should be created', async () => {
      const createdDate = new Date('2023-09-11T14:10:30.000+0000');
      const insertedCustomerAccount = await insertCustomerAccount(createdDate);

      const customerPolicy = createCustomerPolicyWithPriorData(createdDate, new Date('2024-12-04'), 100, 'salesforce', insertedCustomerAccount.insertedId);
      await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);

      const customerPolicy1 = buildCustomerPolicy(new Date('2024-07-04'), 500, 'bookAssessment');
      const request = buildRequest([customerPolicy1], 'BAT');

      const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);
      await sleep(1000);
      const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(2);
      expect(mongoCustomerPoliciesAfterUpdate[1].bookAssessment?.assessmentId).toEqual(1235);
      expect(mongoCustomerPoliciesAfterUpdate[1].bookAssessment?.priorPremium).toEqual(500);

  }, 10000)

  test('Given a customer policy created from salesforce with no prior policy number,'
    + 'When a request came from aqe and prior lob, prior expiration date and prior premium not matched with salesforce,'    
    + 'Then a new policy should be created', async () => {
      const createdDate = new Date('2023-09-11T14:10:30.000+0000');
      const insertedCustomerAccount = await insertCustomerAccount(createdDate);

      const customerPolicy = createCustomerPolicyWithPriorData(createdDate, new Date('2024-12-04'), 100, 'salesforce', insertedCustomerAccount.insertedId);
      await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);

      const customerPolicy1 = buildCustomerPolicy(new Date('2024-07-04'), 500, 'aqe');
      const request = buildRequest([customerPolicy1], 'AQE');

      const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);
      await sleep(1000);
      const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(2);
      expect(mongoCustomerPoliciesAfterUpdate[1].aqe?.opportunityId).toEqual(1235);
      expect(mongoCustomerPoliciesAfterUpdate[1].aqe?.priorPremium).toEqual(500);
  }, 10000)

  test('Given a customer policy created from aqe with no prior policy number,'
    + 'When a request came from bat and prior lob, prior expiration date and prior premium not matched with aqe,'    
    + 'Then a new policy should be created', async () => {
      const createdDate = new Date('2023-09-11T14:10:30.000+0000');
      const insertedCustomerAccount = await insertCustomerAccount(createdDate);

      const customerPolicy = createCustomerPolicyWithPriorData(createdDate, new Date('2024-12-04'), 100, 'aqe', insertedCustomerAccount.insertedId);
      await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);

      const customerPolicy1 = buildCustomerPolicy(new Date('2024-07-04'), 500, 'bookAssessment');
      const request = buildRequest([customerPolicy1], 'BAT');

      const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);
      await sleep(1000);
      const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(2);
      expect(mongoCustomerPoliciesAfterUpdate[1].bookAssessment?.assessmentId).toEqual(1235);
      expect(mongoCustomerPoliciesAfterUpdate[1].bookAssessment?.priorPremium).toEqual(500);
  }, 10000)

  test('Given a customer policy created from aqe with no prior policy number,'
    + 'When a request came from salesforce and prior lob, prior expiration date and prior premium not matched with aqe,'    
    + 'Then a new policy should be created', async () => {
      const createdDate = new Date('2023-09-11T14:10:30.000+0000');
      const insertedCustomerAccount = await insertCustomerAccount(createdDate);

      const customerPolicy = createCustomerPolicyWithPriorData(createdDate, new Date('2024-12-04'), 100, 'aqe', insertedCustomerAccount.insertedId);
      await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);

      const customerPolicy1 = buildCustomerPolicy(new Date('2024-07-04'), 500, 'salesforce');
      const request = buildRequest([customerPolicy1], 'SALESFORCE');

      const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);
      await sleep(1000);
      const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(2);
      expect(mongoCustomerPoliciesAfterUpdate[1].salesforce?.policyDetailsId).toEqual('1235');
      expect(mongoCustomerPoliciesAfterUpdate[1].salesforce?.priorPremium).toEqual(500);
  }, 10000)

   test('Given a customer policy created from bat with no prior policy number,'
      + 'When a request came from salesforce and prior lob, prior expiration date and prior premium not matched with bat,'    
      + 'Then a new policy should be created', async () => {
        const createdDate = new Date('2023-09-11T14:10:30.000+0000');
        const insertedCustomerAccount = await insertCustomerAccount(createdDate);
  
        const customerPolicy = createCustomerPolicyWithPriorData(createdDate, new Date('2024-12-04'), 100, 'bookAssessment', insertedCustomerAccount.insertedId);
        await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);
  
        const customerPolicy1 = buildCustomerPolicy(new Date('2024-07-04'), 500, 'salesforce');
        const request = buildRequest([customerPolicy1], 'SALESFORCE');
  
        const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);
  
        const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);
        await sleep(1000);
        const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(2);
    }, 10000)
  
    test('Given a customer policy created from aqe with no prior policy number,'
      + 'When a request came from salesforce and prior lob, prior expiration date and prior premium not matched with aqe,'    
      + 'Then a new policy should be created', async () => {
        const createdDate = new Date('2023-09-11T14:10:30.000+0000');
        const insertedCustomerAccount = await insertCustomerAccount(createdDate);
  
        const customerPolicy = createCustomerPolicyWithPriorData(createdDate, new Date('2024-12-04'), 100, 'aqe', insertedCustomerAccount.insertedId);
        await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);
  
        const customerPolicy1 = buildCustomerPolicy(new Date('2024-07-04'), 500, 'salesforce');
        const request = buildRequest([customerPolicy1], 'SALESFORCE');
  
        const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);
  
        const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);
        await sleep(1000);
        const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(2);
        expect(mongoCustomerPoliciesAfterUpdate[1].salesforce?.policyDetailsId).toEqual('1235');
        expect(mongoCustomerPoliciesAfterUpdate[1].salesforce?.priorPremium).toEqual(500);
    }, 10000)

});

// =====================================================================
// Policy updation
// =====================================================================
describe('Policy updation', () => {
  test('Given customer account and policy exists but the source data not present, '
    + 'When processed, Then source data should be updated in the existing policy', async () => {
      const createdDate = new Date('2023-09-11T14:10:30.000+0000');
      const insertedCustomerAccount = await insertCustomerAccount(createdDate);

      const customerPolicy: CustomerPolicyModel = {
        customerAccountId: insertedCustomerAccount.insertedId,
        btCode: 2,
        lastUpdatedDate: createdDate,
        createdDate: createdDate,
        priorPolicy: {
          policyNumber: '987654',
          lineOfBusiness: 'AUTOB'
        }
      };
      const insertedCustomerPolicy = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);


      const customerPolicy1: CustomerPolicy = {
        priorPolicy: {
          lineOfBusiness: 'AUTOB',
          policyNumber: '987654',
        },
        libertyPolicy: {
          effectiveDate: new Date('2024-12-04'),
          ecliqId : '8c34e5ab-04dc-4eaf-afea-25fdb3d7d001'
        },
        aqe: {
          opportunityId: 1235
        }
      };

      const request: Request = {
        transactionId: 'transaction-id',
        searchCriteria: {
          customerMetadata: {
            btCode: 2,
            customerName: 'Test Customer'.toUpperCase(),
            address1: '1234 Test Lane',
            address2: '',
            city: 'Gotham',
            state: 'NY',
            zip: '*********',
          },
        },
        customerMetadata: {
          btCode: 2,
          customerName: 'Test Customer',
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
        },
        eventSource: 'AQE',
        customerPolicies: [customerPolicy1],
        ecliqAccounts: [],
      };
      const createTransactionSpy = await initializeAuditLogMock('createTransaction');

      const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);
      expect(mongoCustomerPoliciesBeforeUpdate[0].aqe?.opportunityId).toBeUndefined();

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

      // verify the response
      expect(addCustomerResponse.statusCode).toEqual(200);
      const response: Response = JSON.parse(addCustomerResponse.body).data;
      expect(response.success).toEqual('Completed processing');
      expect(typeof response.id).toEqual('string');
      // Introduce a delay before fetching the updated data
      await sleep(1000); // Delay for 1 seconds
      const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
      expect(mongoCustomerPoliciesAfterUpdate[0].aqe?.opportunityId).toEqual(1235);
      expect(mongoCustomerPoliciesAfterUpdate[0].btCode).toEqual(2);
      expect(createTransactionSpy).toHaveBeenCalledTimes(2);
  }, 10000)

  test('Given customer account exists but policy not exists, '
    + 'When processed, Then source data should be updated in the existing policy', async () => {
      const createdDate = new Date('2023-09-11T14:10:30.000+0000');
      const insertedCustomerAccount = await insertCustomerAccount(createdDate);

      const customerPolicy: CustomerPolicyModel = {
        customerAccountId: insertedCustomerAccount.insertedId,
        btCode: 2,
        lastUpdatedDate: createdDate,
        createdDate: createdDate,
        priorPolicy: {
          policyNumber: '987654',
          lineOfBusiness: 'AUTOB'
        },
        aqe: {
          opportunityId: 1235
        }
      };
      const insertedCustomerPolicy = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);


      const customerPolicy1: CustomerPolicy = {
        priorPolicy: {
          lineOfBusiness: 'CPKGE',
          policyNumber: '987657',
        },
        libertyPolicy: {
          effectiveDate: new Date('2024-12-04'),
            ecliqId:'8c34e5ab-04dc-4eaf-afea-25fdb3d7d00e'
        },
        aqe: {
          opportunityId: 1236
        }
      };

      const request: Request = {
        searchCriteria: {
          customerMetadata: {
            btCode: 2,
            customerName: 'Test Customer',
            address1: '1234 Test Lane',
            address2: '',
            city: 'Gotham',
            state: 'NY',
            zip: '*********',
          },
        },
        customerMetadata: {
          btCode: 2,
          customerName: 'Test Customer',
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
        },
        eventSource: 'AQE',
        customerPolicies: [customerPolicy1],
        ecliqAccounts: [],
      };

      const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);
      expect(mongoCustomerPoliciesBeforeUpdate[0].aqe?.opportunityId).toEqual(1235);

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

      // verify the response
      expect(addCustomerResponse.statusCode).toEqual(200);
      const response: Response = JSON.parse(addCustomerResponse.body).data;
      expect(response.success).toEqual('Completed processing');
      expect(typeof response.id).toEqual('string');

      const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(2);
      expect(mongoCustomerPoliciesAfterUpdate[0].aqe?.opportunityId).toEqual(1235);
      expect(mongoCustomerPoliciesAfterUpdate[1].aqe?.opportunityId).toEqual(1236);
      expect(mongoCustomerPoliciesAfterUpdate[0].btCode).toEqual(2);
  }, 10000);

  test('Given a customer account matches with original BT code and receives request for same customer with new BT code, '
    + 'When processed, Then new customer account is created and customer policy is updated with new customer account id', async () => {

    const createdDate = new Date('2023-09-11T14:10:30.000+0000');
    const insertedCustomerAccount = await insertCustomerAccount(createdDate);

    const customerPolicy: CustomerPolicyModel = {
      customerAccountId: insertedCustomerAccount.insertedId,
      lastUpdatedDate: createdDate,
      createdDate: createdDate,
      btCode: 2,
      priorPolicy: {
        policyNumber: '987654',
        lineOfBusiness: 'AUTOB'
      },
      aqe: {
        opportunityId: 1235
      }
    };
    const insertedCustomerPolicy = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);

    const customerPolicy1: CustomerPolicy = {
      btCode: 4,
      priorPolicy: {
        policyNumber: '987654',
        lineOfBusiness: 'AUTOB',
      },
      aqe: {
        opportunityId: 1235
      }
    };

    const request: Request = {
      searchCriteria: {
        customerAccountId: insertedCustomerAccount.insertedId.toString(),
        btCode: 2,
        customerMetadata: {
          btCode: 4,
          customerName: 'Test Customer'.toUpperCase(),
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
        },
      },
      customerMetadata: {
        btCode: 4,
        customerName: 'Test Customer',
        address1: '1234 Test Lane',
        address2: '',
        city: 'Gotham',
        state: 'NY',
        zip: '*********',
      },
      eventSource: 'AQE',
      customerPolicies: [customerPolicy1],
      ecliqAccounts: [
        {
        ecliqAccountNumber: '12346',
        ecliqAccountCreationDate: new Date(),
        },
      ],
    };

    const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

    expect(addCustomerResponse.statusCode).toEqual(200);
    const response: Response = JSON.parse(addCustomerResponse.body).data;
    expect(response.success).toEqual('Completed processing');
    expect(typeof response.id).toEqual('string');

    const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
    expect(mongoCustomerAccounts[0].customerMetadataMap['AQE'].customerMetadata.btCode).toEqual(4);
    expect(mongoCustomerAccounts[0].customerMetadataMap['AQE'].customerMetadata.btCode).not.toEqual(insertedCustomerAccount.insertedId)

    const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
    expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
    expect(mongoCustomerPoliciesAfterUpdate[0].aqe?.opportunityId).toEqual(1235);
    expect(mongoCustomerPoliciesAfterUpdate[0].customerAccountId).toEqual(mongoCustomerAccounts[0]._id);
    expect(mongoCustomerPoliciesAfterUpdate[0].btCode).toEqual(4);
  }, 10000);

  test('Given a customer account matches with new BT code and receives request for same customer for another policy, '
    + 'When processed, Then the customer policy is updated with customer account id matching new BT code', async () => {
      const createdDate = new Date('2023-09-11T14:10:30.000+0000');
    const insertedCustomerAccount1 = await insertCustomerAccount(createdDate);

    const customerPolicy1: CustomerPolicyModel = {
      customerAccountId: insertedCustomerAccount1.insertedId,
      lastUpdatedDate: createdDate,
      createdDate: createdDate,
      btCode: 2,
      priorPolicy: {
        policyNumber: '987654',
        lineOfBusiness: 'AUTOB'
      },
      aqe: {
        opportunityId: 1235
      }
    };
    const insertedCustomerPolicy1 = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy1);

    let customerMetadataMap: CustomerMetadataMap<string, CustomerMetadataModel> = {
      'AQE': {
        customerMetadata: {
          btCode: 4,
          customerName: 'Test Customer'.toUpperCase(),
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
          addressLastUpdatedDate: createdDate,
        } as CustomerMetadata,
        lastUpdatedDate: createdDate,
        createdDate: createdDate,
      } as CustomerMetadataModel
    };
    const customerAccount: CustomerAccountModel = {
      customerMetadataMap: customerMetadataMap,
      ecliqAccounts: [
        {
          ecliqAccountNumber: '12345',
          ecliqAccountCreationDate: new Date(),
        },
      ],
      lastUpdatedDate: createdDate,
      createdDate: createdDate,
    };
    const insertedCustomerAccount2 =  await insertCustomerAccountIntoMongoDB(mongoClient, customerAccount);

    const customerPolicy2: CustomerPolicyModel = {
      customerAccountId: insertedCustomerAccount2.insertedId,
      lastUpdatedDate: createdDate,
      createdDate: createdDate,
      btCode: 4,
      priorPolicy: {
        policyNumber: '987677',
        lineOfBusiness: 'BOP'
      },
      aqe: {
        opportunityId: 1236
      }
    };

    const insertedCustomerPolicy2 = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy2);

    const customerPolicy3: CustomerPolicy = {
      btCode: 2,
      priorPolicy: {
        policyNumber: '987677',
        lineOfBusiness: 'BOP',
      },
      aqe: {
        opportunityId: 1236
      }
    };

    const request: Request = {
      searchCriteria: {
        customerAccountId: insertedCustomerAccount2.insertedId.toString(),
        btCode: 4,
        customerMetadata: {
          btCode: 2,
          customerName: 'Test Customer'.toUpperCase(),
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
        },
      },
      customerMetadata: {
        btCode: 2,
        customerName: 'Test Customer',
        address1: '1234 Test Lane',
        address2: '',
        city: 'Gotham',
        state: 'NY',
        zip: '*********',
      },
      eventSource: 'AQE',
      customerPolicies: [customerPolicy3],
      ecliqAccounts: [
        {
        ecliqAccountNumber: '12346',
        ecliqAccountCreationDate: new Date(),
        },
      ],
    };

    const mongoCustomerAccountsBeforeUpdate = await getAllCustomerAccountsFromMongo(mongoClient);
    expect(mongoCustomerAccountsBeforeUpdate.length).toEqual(2);

    const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

    expect(addCustomerResponse.statusCode).toEqual(200);
    const response: Response = JSON.parse(addCustomerResponse.body).data;
    expect(response.success).toEqual('Completed processing');
    expect(typeof response.id).toEqual('string');
    
    const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
    expect(mongoCustomerAccounts.length).toEqual(1);
    expect(mongoCustomerAccounts[0].customerMetadataMap['AQE'].customerMetadata.btCode).toEqual(2);
    expect(mongoCustomerAccounts[0].customerMetadataMap['AQE'].customerMetadata.btCode).not.toEqual(insertedCustomerAccount1.insertedId)

    const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
    expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(2);
    expect(mongoCustomerPoliciesAfterUpdate[0].aqe?.opportunityId).toEqual(1235);
    expect(mongoCustomerPoliciesAfterUpdate[1].aqe?.opportunityId).toEqual(1236);
    expect(mongoCustomerPoliciesAfterUpdate[0].customerAccountId).toEqual(mongoCustomerAccounts[0]._id);
    expect(mongoCustomerPoliciesAfterUpdate[1].customerAccountId).toEqual(mongoCustomerAccounts[0]._id);
    expect(mongoCustomerPoliciesAfterUpdate[0].btCode).toEqual(2);
    expect(mongoCustomerPoliciesAfterUpdate[1].btCode).toEqual(2);
  }, 10000);

  test('Given a customer policy created from salesforce with no prior policy number,'
      + 'When a request came from bat and prior lob, prior expiration date and prior premium matched with salesforce,'    
      + 'Then the existing policy should be updated', async () => {
        const createdDate = new Date('2023-09-11T14:10:30.000+0000');
        const insertedCustomerAccount = await insertCustomerAccount(createdDate);
  
        const customerPolicy = createCustomerPolicyWithPriorData(createdDate, new Date('2024-12-04'), 100, 'salesforce', insertedCustomerAccount.insertedId);
        await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);
  
        const customerPolicy1 = buildCustomerPolicy(new Date('2024-12-04'), 100, 'bookAssessment');
        const request = buildRequest([customerPolicy1], 'BAT');
  
        const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);
  
        const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);
        await sleep(1000);
        const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
        expect(mongoCustomerPoliciesAfterUpdate[0].bookAssessment?.assessmentId).toEqual(1235);
        expect(mongoCustomerPoliciesAfterUpdate[0].bookAssessment?.priorPremium).toEqual(100);
  
    }, 10000)
  
    test('Given a customer policy created from salesforce with no prior policy number,'
      + 'When a request came from aqe and prior lob, prior expiration date and prior premium matched with salesforce,'    
      + 'Then the existing policy should be updated', async () => {
        const createdDate = new Date('2023-09-11T14:10:30.000+0000');
        const insertedCustomerAccount = await insertCustomerAccount(createdDate);
  
        const customerPolicy = createCustomerPolicyWithPriorData(createdDate, new Date('2024-12-04'), 100, 'salesforce', insertedCustomerAccount.insertedId);
        await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);
  
        const customerPolicy1 = buildCustomerPolicy(new Date('2024-12-04'), 100, 'aqe');
        const request = buildRequest([customerPolicy1], 'AQE');
  
        const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);
  
        const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);
        await sleep(1000);
        const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
        expect(mongoCustomerPoliciesAfterUpdate[0].aqe?.opportunityId).toEqual(1235);
        expect(mongoCustomerPoliciesAfterUpdate[0].aqe?.priorPremium).toEqual(100);
    }, 10000)

    test('Given a customer policy created from aqe with no prior policy number,'
      + 'When a request came from bat and prior lob, prior expiration date and prior premium matched with aqe,'    
      + 'Then the existing policy should be updated', async () => {
        const createdDate = new Date('2023-09-11T14:10:30.000+0000');
        const insertedCustomerAccount = await insertCustomerAccount(createdDate);
  
        const customerPolicy = createCustomerPolicyWithPriorData(createdDate, new Date('2024-12-04'), 100, 'aqe', insertedCustomerAccount.insertedId);
        await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);
  
        const customerPolicy1 = buildCustomerPolicy(new Date('2024-12-04'), 100, 'bookAssessment');
        const request = buildRequest([customerPolicy1], 'BAT');
  
        const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);
  
        const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);
        await sleep(1000);
        const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
        expect(mongoCustomerPoliciesAfterUpdate[0].bookAssessment?.assessmentId).toEqual(1235);
        expect(mongoCustomerPoliciesAfterUpdate[0].bookAssessment?.priorPremium).toEqual(100);
  
    }, 10000)
  
    test('Given a customer policy created from aqe with no prior policy number,'
      + 'When a request came from salesforce and prior lob, prior expiration date and prior premium matched with aqe,'    
      + 'Then the existing policy should be updated', async () => {
        const createdDate = new Date('2023-09-11T14:10:30.000+0000');
        const insertedCustomerAccount = await insertCustomerAccount(createdDate);
  
        const customerPolicy = createCustomerPolicyWithPriorData(createdDate, new Date('2024-12-04'), 100, 'aqe', insertedCustomerAccount.insertedId);
        await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);
  
        const customerPolicy1 = buildCustomerPolicy(new Date('2024-12-04'), 100,'salesforce');
        const request = buildRequest([customerPolicy1], 'SALESFORCE');
  
        const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);
  
        const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);
        await sleep(1000);
        const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
        expect(mongoCustomerPoliciesAfterUpdate[0].salesforce?.policyDetailsId).toEqual('1235');
        expect(mongoCustomerPoliciesAfterUpdate[0].salesforce?.priorPremium).toEqual(100);
    }, 10000)

    test('Given a customer policy created from bat with no prior policy number,'
      + 'When a request came from salesforce and prior lob, prior expiration date and prior premium matched with bat,'    
      + 'Then the existing policy should be updated', async () => {
        const createdDate = new Date('2023-09-11T14:10:30.000+0000');
        const insertedCustomerAccount = await insertCustomerAccount(createdDate);
  
        const customerPolicy = createCustomerPolicyWithPriorData(createdDate, new Date('2024-12-04'), 100, 'bookAssessment', insertedCustomerAccount.insertedId);
        await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);
  
        const customerPolicy1 = buildCustomerPolicy(new Date('2024-12-04'), 100, 'salesforce');
        const request = buildRequest([customerPolicy1], 'SALESFORCE');
  
        const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);
  
        const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);
        await sleep(1000);
        const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
        expect(mongoCustomerPoliciesAfterUpdate[0].salesforce?.policyDetailsId).toEqual('1235');
        expect(mongoCustomerPoliciesAfterUpdate[0].salesforce?.priorPremium).toEqual(100);
  
    }, 10000)
  
    test('Given a customer policy created from aqe with no prior policy number,'
      + 'When a request came from salesforce and prior lob, prior expiration date and prior premium matched with aqe,'    
      + 'Then the existing policy should be updated', async () => {
        const createdDate = new Date('2023-09-11T14:10:30.000+0000');
        const insertedCustomerAccount = await insertCustomerAccount(createdDate);
  
        const customerPolicy = createCustomerPolicyWithPriorData(createdDate, new Date('2024-12-04'), 100, 'aqe', insertedCustomerAccount.insertedId);
        await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);
  
        const customerPolicy1 = buildCustomerPolicy(new Date('2024-12-04'), 100, 'salesforce');
        const request = buildRequest([customerPolicy1], 'SALESFORCE');
  
        const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);
  
        const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);
        await sleep(1000);
        const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
        expect(mongoCustomerPoliciesAfterUpdate[0].salesforce?.policyDetailsId).toEqual('1235');
        expect(mongoCustomerPoliciesAfterUpdate[0].salesforce?.priorPremium).toEqual(100);
    }, 10000)

});

// =====================================================================
// BAT events
// =====================================================================
describe('BAT events', () => {
  test(
    'given no prior data exists, ' +
      'when the eventSource is BAT, ' +
      'and processed successfully, ' +
      'then expect MongoDB to have one account with the customer policy',
    async () => {
      const { priorCarrierPolicyNumber, btCode, customerMetaData, eventSource, customerPolicy }: { priorCarrierPolicyNumber: string; btCode: string|number; customerMetaData: CustomerMetadata; eventSource: string; customerPolicy: CustomerPolicy; } = setupInitialBatData();

      // Requests coming from BAT should include searchCriteria, customerMetadata, eventSource = BAT, and customerPolicies
      const request: Request = {
        // searchCriteria from BAT should include priorCarrierPolicyNumber, btCode, customerMetaData
        searchCriteria: {
          priorCarrierPolicyNumber: priorCarrierPolicyNumber,
          btCode: btCode,
          customerMetadata: customerMetaData,
        },
        customerMetadata: customerMetaData,
        eventSource: eventSource,
        customerPolicies: [customerPolicy],
        ecliqAccounts: [],
      };

      const addCustomerPolicyResponse: APIGatewayProxyResult = await Handler.addCustomerPolicyLambda(request, context);
      const mongoCustomerAccounts: WithId<CustomerAccountModel>[] = await getAllCustomerAccountsFromMongo(mongoClient);
      const mongoCustomerPolicies: WithId<CustomerPolicyModel>[] = await getAllCustomerPoliciesFromMongo(mongoClient);

      validateInitialBatInsertion(addCustomerPolicyResponse, mongoCustomerAccounts, eventSource, customerMetaData, mongoCustomerPolicies, customerPolicy);
    },
  );


  test('given an existing customeraccount, ' +
    'when the eventSource is BAT, ' + 
    'and the customerAccountId is the same, ' +
    'then expect the existing BAT customerMetadata to be updated, ' +
    'and expect the existing policy to be updated',
    async () => {
      const { priorCarrierPolicyNumber, btCode, customerMetaData, eventSource, customerPolicy }: { priorCarrierPolicyNumber: string; btCode: string|number; customerMetaData: CustomerMetadata; eventSource: string; customerPolicy: CustomerPolicy; } = setupInitialBatData();

      const request: Request = {
        searchCriteria: { 
          priorCarrierPolicyNumber: priorCarrierPolicyNumber,
          btCode: btCode,
          customerMetadata: customerMetaData
        },
        customerMetadata: customerMetaData,
        eventSource: eventSource,
        customerPolicies: [customerPolicy],
        ecliqAccounts: [],
      };

      const addCustomerPolicyResponse = await Handler.addCustomerPolicyLambda(request, context);

      // verify customer account and policy prior to update
      const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
      const mongoCustomerPolicies = await getAllCustomerPoliciesFromMongo(mongoClient);
      validateInitialBatInsertion(addCustomerPolicyResponse, mongoCustomerAccounts, eventSource, customerMetaData, mongoCustomerPolicies, customerPolicy);

      // set up another request with updated customer metadata
      // it will match by the searchCriteria customer account id
      const customerAccountId = mongoCustomerAccounts[0]._id;

      const changedCustomerMetaData: CustomerMetadata = {
        address1: 'changed address 1',
        address2: 'changed address 2',
        city: 'changed city',
        state: 'changed state',
        zip: '90210',
        customerName: 'changed customer name',
        btCode: 1,
      }

      const request2: Request = {
        searchCriteria: {
          customerAccountId: String(customerAccountId),
          priorCarrierPolicyNumber: priorCarrierPolicyNumber,
          btCode: btCode,
          customerMetadata: changedCustomerMetaData
        },
        customerMetadata: changedCustomerMetaData,
        eventSource: eventSource,
        customerPolicies: [customerPolicy],
        ecliqAccounts: [],
      }

      const secondAddCustomerPolicyResponse = await Handler.addCustomerPolicyLambda(request2, context);

      // verify the updates
      const mongoCustomerAccountsAfterUpdate = await getAllCustomerAccountsFromMongo(mongoClient);

      expect(mongoCustomerAccountsAfterUpdate.length).toEqual(1); // no new insertion
      expect(mongoCustomerAccountsAfterUpdate[0].customerMetadataMap[eventSource]?.customerMetadata.btCode).toEqual(1);

      // expect it to be equal to the changed values
      assertCustomerMetadataEquality(mongoCustomerAccountsAfterUpdate[0], eventSource, changedCustomerMetaData);

      // verify the customer policy is updated
      const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1); // no new insertion

      // updated timestamp
      expect(mongoCustomerPolicies[0].lastUpdatedDate).not.toEqual(mongoCustomerPoliciesAfterUpdate[0].bookAssessment?.lastUpdatedDate);

      assertCustomerPolicyEqualityAndIfUpdateThenIgnoreLobPolicyNumber(mongoCustomerPoliciesAfterUpdate[0], customerPolicy);
  });

  test('given an existing customeraccount with eventSource AQE, ' +
        'when the eventSource is BAT, and the customerAccountId is the same, ' +
        'then expect the existing BAT customerMetadata to be updated, ' +
        'and expect the existing policy to be updated',
        async () => {
            const { priorCarrierPolicyNumber, btCode, customerMetaData, eventSource, customerPolicy } = {
                priorCarrierPolicyNumber: "********",
                btCode: 211815,
                customerMetaData: commonCustomerMetadata,
                eventSource: "AQE",
                customerPolicy: commonCustomerPolicy,
            };

            const request: Request = {
                searchCriteria: { priorCarrierPolicyNumber, btCode, customerMetadata: commonCustomerMetadata },
                customerMetadata: commonCustomerMetadata,
                customerPolicies: [commonCustomerPolicy],
                ecliqAccounts: [],
                eventSource: eventSource,
                transactionId: "683a113cb578b025b01dd81a",
            };

            const addCustomerPolicyResponseAQE = await Handler.addCustomerPolicyLambda(request, context);

            const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
            expect(mongoCustomerAccounts.length).toEqual(1);
            const mongoCustomerPolicies = await getAllCustomerPoliciesFromMongo(mongoClient);

            const requestForBAT: Request = {
                searchCriteria: { priorCarrierPolicyNumber, btCode, customerMetadata: commonCustomerMetadata },
                customerMetadata: commonCustomerMetadata,
                customerPolicies: [
                    {
                        ...commonCustomerPolicy,
                        bookAssessment: {
                            priorExpirationDate: new Date("2025-12-31"),
                            priorPremium: 469,
                            assessmentId: 749650,
                            finalAppetiteDecision: "Approved",
                        },
                    },
                ],
                ecliqAccounts: [],
                eventSource: "BAT",
            };

            const addCustomerPolicyResponseForBAT = await Handler.addCustomerPolicyLambda(requestForBAT, context);

            const mongoCustomerAccountsAfterUpdate = await getAllCustomerAccountsFromMongo(mongoClient);
            expect(mongoCustomerAccountsAfterUpdate.length).toEqual(1);
            expect(mongoCustomerAccountsAfterUpdate[0].customerMetadataMap[eventSource]?.customerMetadata.btCode).toEqual(211815);
            expect(mongoCustomerAccountsAfterUpdate[0].aqeCustomerMetadataKey).toEqual('211815-TEST FORCE LLC-3368 Highway 280 Ste 205-Alex City-AL-35010');
            expect(mongoCustomerAccountsAfterUpdate[0].batCustomerMetadataKey).toEqual('211815-TEST FORCE LLC-3368 Highway 280 Ste 205-Alex City-AL-35010');

            if (requestForBAT.customerMetadata) {
                assertCustomerMetadataEquality(
                    mongoCustomerAccountsAfterUpdate[0],
                    eventSource,
                    requestForBAT.customerMetadata
                );
            }

            const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
            expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
            expect(mongoCustomerPolicies[0].lastUpdatedDate).not.toEqual(mongoCustomerPoliciesAfterUpdate[0].bookAssessment?.lastUpdatedDate);

            assertCustomerPolicyEqualityAndIfUpdateThenIgnoreLobPolicyNumber(mongoCustomerPoliciesAfterUpdate[0], customerPolicy);
        });

    const commonCustomerMetadata = {
        btCode: 211815,
        customerName: "TEST FORCE LLC",
        address1: "3368 Highway 280 Ste 205",
        address2: "Ste 205",
        city: "Alex City",
        state: "AL",
        zip: "35010",
    };

    const commonCustomerPolicy = {
        btCode: 211815,
        priorPolicy: {
            lineOfBusiness: "WORK",
            policyNumber: "********",

        }
    };

  test('Given customer account exists but policy not exists, '
    + 'When processed, Then source data should be updated in the existing policy', async () => {
      const createdDate = new Date('2023-09-11T14:10:30.000+0000');
      const insertedCustomerAccount = await insertCustomerAccount(createdDate);

      const customerPolicy: CustomerPolicyModel = {
        customerAccountId: insertedCustomerAccount.insertedId,
        btCode: 2,
        lastUpdatedDate: createdDate,
        createdDate: createdDate,
        priorPolicy: {
          policyNumber: '987654',
          lineOfBusiness: 'AUTOB'
        }
      };
      const insertedCustomerPolicy = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);


      const customerPolicy1: CustomerPolicy = {
        priorPolicy: {
          lineOfBusiness: 'AUTOB',
          policyNumber: '987654',
        },
        libertyPolicy: {
          effectiveDate: new Date('2024-12-04'),
          ecliqId : '8c34e5ab-04dc-4eaf-afea-25fdb3d7d001'
        },
        aqe: {
          opportunityId: 1235
        }
      };

      const request: Request = {
        searchCriteria: {
          customerMetadata: {
            btCode: 2,
            customerName: 'Test Customer'.toUpperCase(),
            address1: '1234 Test Lane',
            address2: '',
            city: 'Gotham',
            state: 'NY',
            zip: '*********',
          },
        },
        customerMetadata: {
          btCode: 2,
          customerName: 'Test Customer',
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
        },
        eventSource: 'AQE',
        customerPolicies: [customerPolicy1],
        ecliqAccounts: [],
      };

      const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);
      expect(mongoCustomerPoliciesBeforeUpdate[0].aqe?.opportunityId).toBeUndefined();

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

      // verify the response
      expect(addCustomerResponse.statusCode).toEqual(200);
      const response: Response = JSON.parse(addCustomerResponse.body).data;
      expect(response.success).toEqual('Completed processing');
      expect(typeof response.id).toEqual('string');
      // Introduce a delay before fetching the updated data
      await sleep(1000); // Delay for 1 seconds
      const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
      expect(mongoCustomerPoliciesAfterUpdate[0].aqe?.opportunityId).toEqual(1235);
      expect(mongoCustomerPoliciesAfterUpdate[0].btCode).toEqual(2);
  }, 10000)

  test('Given customer account exists but policy not exists, '
    + 'When processed, Then source data should be updated in the existing policy', async () => {
      const createdDate = new Date('2023-09-11T14:10:30.000+0000');
      const insertedCustomerAccount = await insertCustomerAccount(createdDate);

      const customerPolicy: CustomerPolicyModel = {
        customerAccountId: insertedCustomerAccount.insertedId,
        btCode: 2,
        lastUpdatedDate: createdDate,
        createdDate: createdDate,
        priorPolicy: {
          policyNumber: '987654',
          lineOfBusiness: 'AUTOB'
        },
        aqe: {
          opportunityId: 1235
        }
      };
      const insertedCustomerPolicy = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);


      const customerPolicy1: CustomerPolicy = {
        priorPolicy: {
          lineOfBusiness: 'CPKGE',
          policyNumber: '987657',
        },
        libertyPolicy: {
          effectiveDate: new Date('2024-12-04'),
            ecliqId:'8c34e5ab-04dc-4eaf-afea-25fdb3d7d00e'
        },
        aqe: {
          opportunityId: 1236
        }
      };

      const request: Request = {
        searchCriteria: {
          customerMetadata: {
            btCode: 2,
            customerName: 'Test Customer',
            address1: '1234 Test Lane',
            address2: '',
            city: 'Gotham',
            state: 'NY',
            zip: '*********',
          },
        },
        customerMetadata: {
          btCode: 2,
          customerName: 'Test Customer',
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
        },
        eventSource: 'AQE',
        customerPolicies: [customerPolicy1],
        ecliqAccounts: [],
      };

      const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);
      expect(mongoCustomerPoliciesBeforeUpdate[0].aqe?.opportunityId).toEqual(1235);

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

      // verify the response
      expect(addCustomerResponse.statusCode).toEqual(200);
      const response: Response = JSON.parse(addCustomerResponse.body).data;
      expect(response.success).toEqual('Completed processing');
      expect(typeof response.id).toEqual('string');

      const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(2);
      expect(mongoCustomerPoliciesAfterUpdate[0].aqe?.opportunityId).toEqual(1235);
      expect(mongoCustomerPoliciesAfterUpdate[1].aqe?.opportunityId).toEqual(1236);
      expect(mongoCustomerPoliciesAfterUpdate[0].btCode).toEqual(2);
  }, 10000);

  test('Given a customer account matches with original BT code and receives request for same customer with new BT code, '
    + 'When processed, Then new customer account is created and customer policy is updated with new customer account id', async () => {

    const createdDate = new Date('2023-09-11T14:10:30.000+0000');
    const insertedCustomerAccount = await insertCustomerAccount(createdDate);

    const customerPolicy: CustomerPolicyModel = {
      customerAccountId: insertedCustomerAccount.insertedId,
      lastUpdatedDate: createdDate,
      createdDate: createdDate,
      btCode: 2,
      priorPolicy: {
        policyNumber: '987654',
        lineOfBusiness: 'AUTOB'
      },
      aqe: {
        opportunityId: 1235
      }
    };
    const insertedCustomerPolicy = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);

    const customerPolicy1: CustomerPolicy = {
      btCode: 4,
      priorPolicy: {
        policyNumber: '987654',
        lineOfBusiness: 'AUTOB',
      },
      aqe: {
        opportunityId: 1235
      }
    };

    const request: Request = {
      searchCriteria: {
        customerAccountId: insertedCustomerAccount.insertedId.toString(),
        btCode: 2,
        customerMetadata: {
          btCode: 4,
          customerName: 'Test Customer'.toUpperCase(),
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
        },
      },
      customerMetadata: {
        btCode: 4,
        customerName: 'Test Customer',
        address1: '1234 Test Lane',
        address2: '',
        city: 'Gotham',
        state: 'NY',
        zip: '*********',
      },
      eventSource: 'AQE',
      customerPolicies: [customerPolicy1],
      ecliqAccounts: [
        {
        ecliqAccountNumber: '12346',
        ecliqAccountCreationDate: new Date(),
        },
      ],
    };

    const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

    expect(addCustomerResponse.statusCode).toEqual(200);
    const response: Response = JSON.parse(addCustomerResponse.body).data;
    expect(response.success).toEqual('Completed processing');
    expect(typeof response.id).toEqual('string');

    const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
    expect(mongoCustomerAccounts[0].customerMetadataMap['AQE'].customerMetadata.btCode).toEqual(4);
    expect(mongoCustomerAccounts[0].customerMetadataMap['AQE'].customerMetadata.btCode).not.toEqual(insertedCustomerAccount.insertedId)

    const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
    expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
    expect(mongoCustomerPoliciesAfterUpdate[0].aqe?.opportunityId).toEqual(1235);
    expect(mongoCustomerPoliciesAfterUpdate[0].customerAccountId).toEqual(mongoCustomerAccounts[0]._id);
    expect(mongoCustomerPoliciesAfterUpdate[0].btCode).toEqual(4);
  }, 10000);

  test('Given a customer account matches with new BT code and receives request for same customer for another policy, '
    + 'When processed, Then the customer policy is updated with customer account id matching new BT code', async () => {
      const createdDate = new Date('2023-09-11T14:10:30.000+0000');
    const insertedCustomerAccount1 = await insertCustomerAccount(createdDate);

    const customerPolicy1: CustomerPolicyModel = {
      customerAccountId: insertedCustomerAccount1.insertedId,
      lastUpdatedDate: createdDate,
      createdDate: createdDate,
      btCode: 2,
      priorPolicy: {
        policyNumber: '987654',
        lineOfBusiness: 'AUTOB'
      },
      aqe: {
        opportunityId: 1235
      }
    };
    const insertedCustomerPolicy1 = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy1);

    let customerMetadataMap: CustomerMetadataMap<string, CustomerMetadataModel> = {
      'AQE': {
        customerMetadata: {
          btCode: 4,
          customerName: 'Test Customer'.toUpperCase(),
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
          addressLastUpdatedDate: createdDate,
        } as CustomerMetadata,
        lastUpdatedDate: createdDate,
        createdDate: createdDate,
      } as CustomerMetadataModel
    };
    const customerAccount: CustomerAccountModel = {
      customerMetadataMap: customerMetadataMap,
      ecliqAccounts: [
        {
          ecliqAccountNumber: '12345',
          ecliqAccountCreationDate: new Date(),
        },
      ],
      lastUpdatedDate: createdDate,
      createdDate: createdDate,
    };
    const insertedCustomerAccount2 =  await insertCustomerAccountIntoMongoDB(mongoClient, customerAccount);

    const customerPolicy2: CustomerPolicyModel = {
      customerAccountId: insertedCustomerAccount2.insertedId,
      lastUpdatedDate: createdDate,
      createdDate: createdDate,
      btCode: 4,
      priorPolicy: {
        policyNumber: '987677',
        lineOfBusiness: 'BOP'
      },
      aqe: {
        opportunityId: 1236
      }
    };

    const insertedCustomerPolicy2 = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy2);

    const customerPolicy3: CustomerPolicy = {
      btCode: 2,
      priorPolicy: {
        policyNumber: '987677',
        lineOfBusiness: 'BOP',
      },
      aqe: {
        opportunityId: 1236
      }
    };

    const request: Request = {
      searchCriteria: {
        customerAccountId: insertedCustomerAccount2.insertedId.toString(),
        btCode: 4,
        customerMetadata: {
          btCode: 2,
          customerName: 'Test Customer'.toUpperCase(),
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
        },
      },
      customerMetadata: {
        btCode: 2,
        customerName: 'Test Customer',
        address1: '1234 Test Lane',
        address2: '',
        city: 'Gotham',
        state: 'NY',
        zip: '*********',
      },
      eventSource: 'AQE',
      customerPolicies: [customerPolicy3],
      ecliqAccounts: [
        {
        ecliqAccountNumber: '12346',
        ecliqAccountCreationDate: new Date(),
        },
      ],
    };

    const mongoCustomerAccountsBeforeUpdate = await getAllCustomerAccountsFromMongo(mongoClient);
    expect(mongoCustomerAccountsBeforeUpdate.length).toEqual(2);

    const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

    expect(addCustomerResponse.statusCode).toEqual(200);
    const response: Response = JSON.parse(addCustomerResponse.body).data;
    expect(response.success).toEqual('Completed processing');
    expect(typeof response.id).toEqual('string');
    
    const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
    expect(mongoCustomerAccounts.length).toEqual(1);
    expect(mongoCustomerAccounts[0].customerMetadataMap['AQE'].customerMetadata.btCode).toEqual(2);
    expect(mongoCustomerAccounts[0].customerMetadataMap['AQE'].customerMetadata.btCode).not.toEqual(insertedCustomerAccount1.insertedId)

    const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
    expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(2);
    expect(mongoCustomerPoliciesAfterUpdate[0].aqe?.opportunityId).toEqual(1235);
    expect(mongoCustomerPoliciesAfterUpdate[1].aqe?.opportunityId).toEqual(1236);
    expect(mongoCustomerPoliciesAfterUpdate[0].customerAccountId).toEqual(mongoCustomerAccounts[0]._id);
    expect(mongoCustomerPoliciesAfterUpdate[1].customerAccountId).toEqual(mongoCustomerAccounts[0]._id);
    expect(mongoCustomerPoliciesAfterUpdate[0].btCode).toEqual(2);
    expect(mongoCustomerPoliciesAfterUpdate[1].btCode).toEqual(2);
  }, 10000);

});

// Simplified repetitive logic by extracting common operations into helper functions

function verifyCustomerAccountInsertion(mongoCustomerAccounts: any[], eventSource: string, customerMetaData: CustomerMetadata) {
  expect(mongoCustomerAccounts.length).toEqual(1);
  expect(typeof mongoCustomerAccounts[0]._id).toEqual('object');
  expect(typeof mongoCustomerAccounts[0].createdDate).toEqual('object');
  expect(typeof mongoCustomerAccounts[0].lastUpdatedDate).toEqual('object');
  assertCustomerMetadataEquality(mongoCustomerAccounts[0], eventSource, customerMetaData);
}

function verifyPolicyInsertion(mongoCustomerPolicies: WithId<CustomerPolicyModel>[], customerPolicy: CustomerPolicy) {
  expect(mongoCustomerPolicies.length).toEqual(1);
  assertCustomerPolicyEqualityAndIfUpdateThenIgnoreLobPolicyNumber(mongoCustomerPolicies[0], customerPolicy);
  expect(mongoCustomerPolicies[0].bookAssessment?.lastUpdatedDate).toBeInstanceOf(Date);
  expect(mongoCustomerPolicies[0].bookAssessment?.createdDate).toBeInstanceOf(Date);
  expect(mongoCustomerPolicies[0].isUpdatedByEcliq).toEqual(false);
  expect(mongoCustomerPolicies[0].createdDate).toBeInstanceOf(Date);
  expect(mongoCustomerPolicies[0].lastUpdatedDate).toBeInstanceOf(Date);
}

function validateInitialBatInsertion(addCustomerPolicyResponse: any, mongoCustomerAccounts: any[], eventSource: string, customerMetaData: CustomerMetadata, mongoCustomerPolicies: WithId<CustomerPolicyModel>[], customerPolicy: CustomerPolicy) {
  expect(addCustomerPolicyResponse.statusCode).toEqual(200);
  const response = JSON.parse(addCustomerPolicyResponse.body).data;
  expect(response.success).toEqual('Completed processing');
  expect(typeof response.id).toEqual('string');

  verifyCustomerAccountInsertion(mongoCustomerAccounts, eventSource, customerMetaData);
  verifyPolicyInsertion(mongoCustomerPolicies, customerPolicy);
}

function setupInitialBatData() {
  const eventSource = 'BAT';

  const priorCarrierPolicyNumber = '999';
  const btCode = Number(1);
  const lob = 'TESTLOB';
  const carrier = 'TEST CARRIER';

  const customerPolicy: CustomerPolicy = {
    btCode: btCode,
    priorPolicy: {
      lineOfBusiness: lob,
      policyNumber: priorCarrierPolicyNumber,
      carrier: carrier,
    },
    bookAssessment: {
      priorExpirationDate: new Date('2024-02-02'),
      priorPremium: 5000,
      assessmentId: 123456,
      finalAppetiteDecision: 'MAYBE',
    },
  };

  const customerMetaData = {
    btCode: btCode,
    customerName: 'Test Customer Name'.toUpperCase(),
    address1: '123 Fake Street',
    address2: 'Test address 2',
    city: 'Seattle',
    state: 'WA',
    zip: '*********',
  };
  return { priorCarrierPolicyNumber, btCode, customerMetaData, eventSource, customerPolicy };
}


function setupInitialAQEData() {
    const eventSource = 'BAT';

    const priorCarrierPolicyNumber = '999';
    const btCode = Number(1);
    const lob = 'TESTLOB';
    const carrier = 'TEST CARRIER';

    const customerPolicy: CustomerPolicy = {
        btCode: btCode,
        priorPolicy: {
            lineOfBusiness: lob,
            policyNumber: priorCarrierPolicyNumber,
            carrier: carrier,
        },
        bookAssessment: {
            priorExpirationDate: new Date('2024-02-02'),
            priorPremium: 5000,
            assessmentId: 123456,
            finalAppetiteDecision: 'MAYBE',
        },
    };

    const customerMetaData = {
        btCode: btCode,
        customerName: 'Test Customer Name'.toUpperCase(),
        address1: '123 Fake Street',
        address2: 'Test address 2',
        city: 'Seattle',
        state: 'WA',
        zip: '*********',
    };
    return { priorCarrierPolicyNumber, btCode, customerMetaData, eventSource, customerPolicy };
}



function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function insertCustomerAccount(createdDate: Date) {
  let customerMetadataMap: CustomerMetadataMap<string, CustomerMetadataModel> = {
    'AQE': {
      customerMetadata: {
        btCode: 2,
        customerName: 'Test Customer'.toUpperCase(),
        address1: '1234 Test Lane',
        address2: '',
        city: 'Gotham',
        state: 'NY',
        zip: '*********',
        addressLastUpdatedDate: createdDate,
      } as CustomerMetadata,
      lastUpdatedDate: createdDate,
      createdDate: createdDate,
    } as CustomerMetadataModel
  };
  const customerAccount: CustomerAccountModel = {
    customerMetadataMap: customerMetadataMap,
    ecliqAccounts: [
      {
        ecliqAccountNumber: '12345',
        ecliqAccountCreationDate: new Date(),
      },
    ],
    lastUpdatedDate: createdDate,
    createdDate: createdDate,
  };
  return await insertCustomerAccountIntoMongoDB(mongoClient, customerAccount);
}
function assertCustomerPolicyEqualityAndIfUpdateThenIgnoreLobPolicyNumber(
  actualPolicy: WithId<CustomerPolicyModel>,
  expectedPolicy: CustomerPolicy
) {
  // Check priorPolicy fields except for lineOfBusiness and policyNumber
  if (expectedPolicy.priorPolicy) {
    if (expectedPolicy.priorPolicy.carrier) {
      expect(actualPolicy.priorPolicy?.carrier).toEqual(expectedPolicy.priorPolicy.carrier);
    }
  }

  // Check bookAssessment fields
  if (expectedPolicy.bookAssessment) {
    expect(actualPolicy.bookAssessment?.priorExpirationDate).toEqual(expectedPolicy.bookAssessment.priorExpirationDate);
    expect(actualPolicy.bookAssessment?.priorPremium).toEqual(expectedPolicy.bookAssessment.priorPremium);
    expect(actualPolicy.bookAssessment?.assessmentId).toEqual(expectedPolicy.bookAssessment.assessmentId);
    expect(actualPolicy.bookAssessment?.finalAppetiteDecision).toEqual(expectedPolicy.bookAssessment.finalAppetiteDecision);
  }

  // Check libertyPolicy fields
  if (expectedPolicy.libertyPolicy) {
    expect(actualPolicy.libertyPolicy?.effectiveDate).toEqual(expectedPolicy.libertyPolicy.effectiveDate);
    expect(actualPolicy.libertyPolicy?.ecliqId).toEqual(expectedPolicy.libertyPolicy.ecliqId);
  }

  // Check aqe fields
  if (expectedPolicy.aqe) {
    expect(actualPolicy.aqe?.opportunityId).toEqual(expectedPolicy.aqe.opportunityId);
  }

  // Check btCode
  if (expectedPolicy.btCode) {
    expect(actualPolicy.btCode).toEqual(expectedPolicy.btCode);
  }
}
function assertCustomerMetadataEquality(
  customerAccount: CustomerAccountModel,
  eventSource: string,
  customerMetaData: CustomerMetadata
) {
  const metadata = customerAccount.customerMetadataMap[eventSource]?.customerMetadata;
  expect(metadata).toBeDefined();
  expect(metadata?.btCode).toEqual(customerMetaData.btCode);
  expect(metadata?.customerName).toEqual(customerMetaData.customerName);
  expect(metadata?.address1).toEqual(customerMetaData.address1);
  expect(metadata?.address2).toEqual(customerMetaData.address2);
  expect(metadata?.city).toEqual(customerMetaData.city);
  expect(metadata?.state).toEqual(customerMetaData.state);
  expect(metadata?.zip).toEqual(customerMetaData.zip);
}

function buildCustomerPolicy(expirationDate: Date, premium: number, priorPolicyType: string) {
  const customerPolicy ={
    priorPolicy: {
      lineOfBusiness: 'AUTOB',
      policyNumber: '987654',
    }
  } as CustomerPolicy;
  buildSourceData(priorPolicyType, customerPolicy, expirationDate, premium);
  return customerPolicy;
}

function buildRequest(policies: CustomerPolicy[], eventSource: string) {
  return {
    searchCriteria: {
      customerMetadata: {
        btCode: 2,
        customerName: 'Test Customer'.toUpperCase(),
        address1: '1234 Test Lane',
        address2: '',
        city: 'Gotham',
        state: 'NY',
        zip: '*********',
      },
    },
    customerMetadata: {
      btCode: 2,
      customerName: 'Test Customer'.toUpperCase(),
      address1: '1234 Test Lane',
      address2: '',
      city: 'Gotham',
      state: 'NY',
      zip: '*********',
    },
    eventSource: eventSource,
    customerPolicies: policies,
    ecliqAccounts: [],
  } as Request;
}
function createCustomerPolicyWithPriorData(createdDate: Date, expirationDate: Date, premium: number, priorPolicyType: string, customerAccountId: ObjectId) {
  const customerPolicy = {
    customerAccountId: customerAccountId,
    lastUpdatedDate: createdDate,
    createdDate: createdDate,
    priorPolicy: {
      lineOfBusiness: 'AUTOB',
    },
  } as CustomerPolicyModel;

  buildSourceData(priorPolicyType, customerPolicy, expirationDate, premium);

  return customerPolicy;
}
function buildSourceData(priorPolicyType: string, customerPolicy: CustomerPolicyModel | CustomerPolicy, expirationDate: Date, premium: number) {
  if (priorPolicyType === 'bookAssessment') {
    customerPolicy.bookAssessment = {
      assessmentId: 1235,
      priorExpirationDate: expirationDate,
      priorPremium: premium,
    };
  } else if (priorPolicyType === 'aqe') {
    customerPolicy.aqe = {
      opportunityId: 1235,
      priorExpirationDate: expirationDate,
      priorPremium: premium,
    };
  } else if (priorPolicyType === 'salesforce') {
    customerPolicy.salesforce = {
      policyDetailsId: '1235',
      priorExpirationDate: expirationDate,
      priorPremium: premium,
    };
  }
}

