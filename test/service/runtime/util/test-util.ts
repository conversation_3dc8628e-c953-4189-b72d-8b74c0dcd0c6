import { Context, APIGatewayProxyResult } from "aws-lambda";

export const getHandlerContext = (): Context => { 
    return {
        callbackWaitsForEmptyEventLoop: true,
        functionName: "fake-function-name",
        functionVersion: "1.0.0",
        invokedFunctionArn: "arn:aws:lambda:us-east-1:123456789012:function:my-function",
        memoryLimitInMB: "200",
        awsRequestId: "requestId",
        logGroupName: "log-group",
        logStreamName: "log-stream",
        getRemainingTimeInMillis: () => 200,
        done: () => null,
        fail: () => null,
        succeed: () => null
    }
}

export const getHandlerResponse = (statusCode: number, body: any, headers?: { [header: string]: string | number | boolean }, isBase64Encoded?: boolean, multiValueHeaders?: { [header: string]: (string | number | boolean)[] }): APIGatewayProxyResult => {
    return {
        body: JSON.stringify(body), 
        statusCode,
        headers,
        isBase64Encoded,
        multiValueHeaders
    } 
}

