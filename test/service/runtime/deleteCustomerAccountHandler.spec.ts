import { Context } from 'aws-lambda';
import { deleteCustomerAccountLambda } from '../../../lib/service/runtime/deleteCustomerAccountHandler';
import { getHandlerResponse } from '../../../lib/service/utility/getHandlerResponse';
import { deleteCustomerAccountsAndPolicies, initializeMongo } from '../../../lib/service/runtime/mongo';

jest.mock('../../../lib/service/runtime/mongo');
jest.mock('../../../lib/service/utility/getHandlerResponse');

describe('deleteCustomerAccountLambda', () => {
    const context: Context = {} as any;

    beforeEach(() => {
        jest.clearAllMocks();
        jest.spyOn(console, 'log').mockImplementation(() => {});
    });

    afterEach(() => {
        (console.log as jest.Mock).mockRestore();
    });

    it('should return 400 when JSON is invalid', async () => {
        const event = { body: '{invalidJson}', headers: { 'Content-Type': 'application/json' } };
        (getHandlerResponse as jest.Mock).mockReturnValue({ statusCode: 400, body: JSON.stringify({ error: 'Invalid JSON' }) });

        const result = await deleteCustomerAccountLambda(event, context);

        expect(result).toEqual({ statusCode: 400, body: JSON.stringify({ error: 'Invalid JSON' }) });
        expect(console.log).toHaveBeenCalled();
    });

    it('should return 400 when customerAccountIds array is empty', async () => {
        const event = { body: JSON.stringify({ customerIds: [] }), headers: { 'Content-Type': 'application/json' } };
        (getHandlerResponse as jest.Mock).mockReturnValue({ statusCode: 400, body: JSON.stringify({ error: 'Missing customer account ids in request body' }) });

        const result = await deleteCustomerAccountLambda(event, context);

        expect(result).toEqual({ statusCode: 400, body: JSON.stringify({ error: 'Missing customer account ids in request body' }) });
        expect(console.log).toHaveBeenCalled();
    });

    it('should return 404 when no customer accounts are found', async () => {
        const event = { body: JSON.stringify(['nonexistentId']), headers: { 'Content-Type': 'application/json' } };
        (deleteCustomerAccountsAndPolicies as jest.Mock).mockResolvedValue(0);
        (getHandlerResponse as jest.Mock).mockReturnValue({ statusCode: 404, body: JSON.stringify({ error: 'No customer accounts were found with provided ids' }) });

        const result = await deleteCustomerAccountLambda(event, context);

        expect(result).toEqual({ statusCode: 404, body: JSON.stringify({ error: 'No customer accounts were found with provided ids' }) });
        expect(console.log).toHaveBeenCalled();
    });

    it('should return 200 when customer accounts are deleted successfully', async () => {
        const event = { body: JSON.stringify(['67e1d0e5257ed137f5e886bc', '67e1d0e5257ed137f5e886bc']), headers: { 'Content-Type': 'application/json' } };
        (deleteCustomerAccountsAndPolicies as jest.Mock).mockResolvedValue(2);
        (getHandlerResponse as jest.Mock).mockReturnValue({ statusCode: 200, body: JSON.stringify({ success: 'Customer account(s) 2 deleted successfully' }) });

        const result = await deleteCustomerAccountLambda(event, context);

        expect(result).toEqual({ statusCode: 200, body: JSON.stringify({ success: 'Customer account(s) 2 deleted successfully' }) });
        expect(console.log).toHaveBeenCalled();
    });

    it('should return 400 when Content-Type is not application/json', async () => {
        const event = {
            body: JSON.stringify({ customerIds: ['67e1d0e5257ed137f5e886bc'] }),
            headers: { 'Content-Type': 'text/plain' }
        };
        (getHandlerResponse as jest.Mock).mockReturnValue({
            statusCode: 400,
            body: JSON.stringify({ error: 'Invalid Content-Type, expected application/json' })
        });

        const result = await deleteCustomerAccountLambda(event, context);

        expect(result).toEqual({
            statusCode: 400,
            body: JSON.stringify({ error: 'Invalid Content-Type, expected application/json' })
        });
    });
});