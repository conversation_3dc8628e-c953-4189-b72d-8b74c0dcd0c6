import { Context } from 'aws-lambda';
import * as Hand<PERSON> from '../../../lib/service/runtime/saveEcliqAccountNumberHandler';
import * as TestUtil from './util/test-util';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { MongoClient, ObjectId } from 'mongodb';
import { decryptSecret } from '../../../lib/service/utility/decryptSecret';
import { throwError } from '../../../lib/service/utility/throwError';
import Response from '../../../types/response';
import CustomerAccountModel, {CustomerPolicyModel} from '../../../model/customerAccountModel';
import {
    deleteAllMongoDBData,
    getAllCustomerAccountsFromMongo, getAllCustomerPoliciesFromMongo,
    insertCustomerAccountIntoMongoDB,
    insertCustomerPolicyIntoMongoDB
} from './mongo-test-util';
import {EventSource, LmStatus} from '../../../types/enums';

jest.mock('../../../lib/service/utility/decryptSecret');
jest.mock('../../../lib/service/utility/throwError');

let context: Context;
let mongoServer: MongoMemoryServer;
let mongoClient: MongoClient;
const envBackup: { [key: string]: string | undefined } = process.env;

beforeAll(async () => {
  jest.resetModules();

  // creating MongoDB in memory
  mongoServer = await MongoMemoryServer.create();

  // backing up original env variables
  process.env = { ...envBackup };

  // generating connection string to the mongodb memory server and assigning to env variable
  process.env.MONGODB_CONNECTION = mongoServer.getUri();

  // creating a MongoClient instance for use in our test cases
  mongoClient = new MongoClient(process.env.MONGODB_CONNECTION!);

  // mocking the decryptSecret function here so that when called in the Lambda, it will use this connection string instead.
  (decryptSecret as jest.Mock).mockReturnValue(Promise.resolve(process.env.MONGODB_CONNECTION));

  // mocking error response
  (throwError as jest.Mock).mockReturnValue(new Error('mock error'));
  // (error as jest.Mock).mockImplementation(() => { new Error('mock error') });
});

afterAll(async () => {
  await mongoClient.close();
  await mongoServer.stop();
  // restoring original env variables
  process.env = envBackup;
});

beforeEach(() => {
  context = TestUtil.getHandlerContext();
});

afterEach(async () => {
  await deleteAllMongoDBData(mongoClient);
});

const createInputRequest = (id?: string, policyId?: string, ecliqAccountNumber?: string) => ({
  queryStringParameters: { id, policyId },
  body: JSON.stringify({
    ecliqAccounts: ecliqAccountNumber ? [{ ecliqAccountNumber, ecliqAccountCreationDate: new Date(Date.now()) }] : [],
  }),
});

test('Given the request has mongo id and account number and customerAccounts data is not present, when processed, nothing is saved', async () => {
  const event = createInputRequest('5f9b1b3b1f3b9b0001b3b9b0', '66eaf680d722cacd2170b3c7', 'ABCD00000001');
  const saveEcliqAccountNumberResponse = await Handler.saveEcliqAccountNumberLambda(event, context);

  expect(saveEcliqAccountNumberResponse.statusCode).toEqual(200);
  expect(JSON.parse(saveEcliqAccountNumberResponse.body).success).toEqual('No matching customer account was found');
  expect(await getAllCustomerAccountsFromMongo(mongoClient)).toHaveLength(0);
});

test('Given the request does not have mongo id and account number is present, when processed, 400 is thrown', async () => {
  const event = createInputRequest(undefined, '66eaf680d722cacd2170b3c7', 'ABCD00000001');
  const saveEcliqAccountNumberResponse = await Handler.saveEcliqAccountNumberLambda(event, context);

  expect(saveEcliqAccountNumberResponse.statusCode).toEqual(400);
  expect(JSON.parse(saveEcliqAccountNumberResponse.body).error).toEqual('Customer account id was not provided');
  expect(await getAllCustomerAccountsFromMongo(mongoClient)).toHaveLength(0);
});

test('Given the request does not have mongo policy id and account number is present, when processed, 400 is thrown', async () => {
  const event = createInputRequest('5f9b1b3b1f3b9b0001b3b9b0', undefined, 'ABCD00000001');
  const response = await Handler.saveEcliqAccountNumberLambda(event, context);

  expect(response.statusCode).toEqual(400);
  expect(JSON.parse(response.body).error).toEqual('Policy id was not provided');
  expect(await getAllCustomerAccountsFromMongo(mongoClient)).toHaveLength(0);
});

test('Given the request has mongo id, policyId and account number is not present, when processed, 400 is thrown', async () => {
  const event = createInputRequest('667c41a04162ebf292811cc2', '66eaf680d722cacd2170b3c7');
  const saveEcliqAccountNumberResponse = await Handler.saveEcliqAccountNumberLambda(event, context);

  expect(saveEcliqAccountNumberResponse.statusCode).toEqual(400);
  expect(JSON.parse(saveEcliqAccountNumberResponse.body).error).toEqual('ecliqAccounts was not provided or is empty');
  expect(await getAllCustomerAccountsFromMongo(mongoClient)).toHaveLength(0);
});

test('Given the request has mongo id, policyId and customer account number and customerAccounts data is present, when processed, ecliq account number is added', async () => {
  const customerAccountId = new ObjectId('667c41a04162ebf292811cc2');
  const createdDate = new Date('2023-09-11T14:10:30.000+0000');
  const lastUpdatedDate = new Date('2023-09-12T14:10:30.000+0000');
  const document: CustomerAccountModel = {
    _id: customerAccountId,
    customerMetadataMap: {
      'AQE': {
        customerMetadata: {
          btCode: 1,
          customerName: 'Test Customer',
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
          addressLastUpdatedDate: createdDate,
        },
        lastUpdatedDate: createdDate,
        createdDate: createdDate,
      },
    },
    ecliqAccounts: [],
    lastUpdatedDate,
    createdDate,
  };

  const customerPolicyDocument: CustomerPolicyModel = {
    _id: new ObjectId('66eaf680d722cacd2170b3c7'),
    customerAccountId,
    btCode: 1,
    emailAddress: '<EMAIL>',
    phoneNumber: '************',
    ratingState: 'NY',
    priorPolicy: {
      lineOfBusiness: 'Auto',
      carrier: 'Test Carrier',
      policyNumber: 'POL1234567',
      lastUpdatedSource: EventSource.ECLIQ,
    },
    libertyPolicy: {
      effectiveDate: new Date('2023-09-11T14:10:30.000+0000'),
      lineOfBusiness: 'Auto',
      policyNumber: 'POL1234567',
      premium: 1000,
      producer: 'Test Producer',
      status: LmStatus.ISSUED,
      lineType: 'Personal',
      ecliqId: 'ECLIQ1234567',
      priorExpirationDate: new Date('2023-09-10T14:10:30.000+0000'),
      priorPremium: 900,
      lastUpdatedDate: new Date('2023-09-12T14:10:30.000+0000'),
      createdDate: new Date('2023-09-11T14:10:30.000+0000'),
    },
    isUpdatedByEcliq: true,
    lastUpdatedDate: new Date('2023-09-12T14:10:30.000+0000'),
    createdDate: new Date('2023-09-11T14:10:30.000+0000'),
  };

  await insertCustomerAccountIntoMongoDB(mongoClient, document);
  await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicyDocument);
  const event = createInputRequest('667c41a04162ebf292811cc2', '66eaf680d722cacd2170b3c7', 'ABCD00000001');
  const saveEcliqAccountNumberResponse = await Handler.saveEcliqAccountNumberLambda(event, context);

  expect(saveEcliqAccountNumberResponse.statusCode).toEqual(200);
  expect(JSON.parse(saveEcliqAccountNumberResponse.body).success).toEqual('Completed processing');
  expect(await getAllCustomerAccountsFromMongo(mongoClient)).toHaveLength(1);
  const mongoData = await getAllCustomerPoliciesFromMongo(mongoClient);
  expect(mongoData).toHaveLength(1);
  expect((mongoData).map(i => i.ecliqAccountNumber)).toEqual(['ABCD00000001']);
});

test('Given the request has mongo id, policyId and customer account number and customerAccounts data is present but policy data not present, when processed, ecliq account number is added to account but customer policy is not updated', async () => {
  const customerAccountId = new ObjectId('667c41a04162ebf292811cc2');
  const createdDate = new Date('2023-09-11T14:10:30.000+0000');
  const lastUpdatedDate = new Date('2023-09-12T14:10:30.000+0000');
  const document: CustomerAccountModel = {
    _id: customerAccountId,
    customerMetadataMap: {
      'AQE': {
        customerMetadata: {
          btCode: 1,
          customerName: 'Test Customer',
          address1: '1234 Test Lane',
          address2: '',
          city: 'Gotham',
          state: 'NY',
          zip: '*********',
          addressLastUpdatedDate: createdDate,
        },
        lastUpdatedDate: createdDate,
        createdDate: createdDate,
      },
    },
    ecliqAccounts: [],
    lastUpdatedDate,
    createdDate,
  };

  await insertCustomerAccountIntoMongoDB(mongoClient, document);
  const event = createInputRequest('667c41a04162ebf292811cc2', '66eaf680d722cacd2170b3c7', 'ABCD00000001');
  const saveEcliqAccountNumberResponse = await Handler.saveEcliqAccountNumberLambda(event, context);

  expect(saveEcliqAccountNumberResponse.statusCode).toEqual(200);
  expect(JSON.parse(saveEcliqAccountNumberResponse.body).success).toEqual('Completed processing');
  expect(await getAllCustomerAccountsFromMongo(mongoClient)).toHaveLength(1);
  expect(await getAllCustomerPoliciesFromMongo(mongoClient)).toHaveLength(0);
});