import { ObjectId } from "mongodb";
import { movePresentFields } from "../../../lib/service/runtime/mongo";
import { CustomerPolicyModel } from "../../../model/customerAccountModel";
import { CustomerPolicy } from "../../../types/request";

test('movePresentFields', () => {
    const _id = new ObjectId();
    const customerAccountId = new ObjectId();
    const sourceCustomerPolicy: Partial<CustomerPolicy> = {
        btCode: 1,
        aqe: {
            opportunityId: 2
        },
        priorPolicy: {
            carrier: 'carrier'
        }
    };

    const targetCustomerPolicy: Partial<CustomerPolicyModel> = {
        _id: _id,
        customerAccountId: customerAccountId,
        emailAddress: 'email',
        aqe: {
            businessType: 'business'
        }
    };

    movePresentFields(sourceCustomerPolicy, targetCustomerPolicy);

    expect(targetCustomerPolicy._id).toEqual(_id);
    expect(targetCustomerPolicy.customerAccountId).toEqual(customerAccountId);
    expect(targetCustomerPolicy.btCode).toEqual(1);
    expect(targetCustomerPolicy.emailAddress).toEqual('email');
    expect(targetCustomerPolicy.aqe?.opportunityId).toEqual(2);
    expect(targetCustomerPolicy.aqe?.businessType).toEqual('business');
    expect(targetCustomerPolicy.priorPolicy?.carrier).toEqual('carrier');
});
