import {Context} from 'aws-lambda';
import * as Handler from '../../../lib/service/runtime/addCustomerPolicyHandler';
import * as TestUtil from './util/test-util';
import {MongoMemoryServer} from 'mongodb-memory-server';
import {MongoClient} from 'mongodb';
import {decryptSecret} from '../../../lib/service/utility/decryptSecret';
import { AuditLogHelper } from '../../../lib/service/utility/auditLogHelper';
import {throwError} from '../../../lib/service/utility/throwError';
import Request, {CustomerMetadata, CustomerPolicy} from '../../../types/request';
import Response from '../../../types/response';
import {
    deleteAllMongoDBData,
    getAllCustomerAccountsFromMongo,
    getAllCustomerPoliciesFromMongo,
    insertCustomerAccountIntoMongoDB,
    insertCustomerPolicyIntoMongoDB,
} from './mongo-test-util';
import CustomerAccountModel, {
    CustomerMetadataMap,
    CustomerMetadataModel,
    CustomerPolicyModel
} from '../../../model/customerAccountModel';

jest.mock('../../../lib/service/utility/decryptSecret');
jest.mock('../../../lib/service/utility/throwError');
jest.mock('../../../lib/service/utility/authenticationTokenHelper');
jest.mock('@lmig/audit-log-node-starter', () => {
    return {
        ...jest.requireActual('@lmig/audit-log-node-starter'),
        AuditLogService: {
            initialize: jest.fn().mockResolvedValue({
                startTransaction: jest.fn(),
                addTransactionEvent: jest.fn(),
                endTransaction: jest.fn(),
            }),
        },
    };
});

let context: Context;
let mongoServer: MongoMemoryServer;
let mongoClient: MongoClient;

const envBackup: { [key: string]: string | undefined } = process.env;

beforeAll(async () => {
    jest.resetModules();

    // creating MongoDB in memory
    mongoServer = await MongoMemoryServer.create();

    // backing up original env variables
    process.env = {...envBackup};

    // generating connection string to the mongodb memory server and assigning to env variable
    process.env.MONGODB_CONNECTION = mongoServer.getUri();

    // creating a MongoClient instance for use in our test cases
    mongoClient = new MongoClient(process.env.MONGODB_CONNECTION!);

    // mocking the decryptSecret function here so that when called in the Lambda, it will use this connection string instead.
    (decryptSecret as jest.Mock).mockReturnValue(Promise.resolve(process.env.MONGODB_CONNECTION));

    // mocking error response
    (throwError as jest.Mock).mockReturnValue(new Error('mock error'));
    // (error as jest.Mock).mockImplementation(() => { new Error('mock error') });
});

afterAll(async () => {
    await mongoClient.close();
    await mongoServer.stop();

    // restoring original env variables
    process.env = envBackup;
});

async function initializeAuditLogMock() {
        const auditLogService = (await AuditLogHelper.init()).auditLogService;

        if (!auditLogService) {
            throw new Error('auditLogService is null');
        }

        return jest.spyOn(auditLogService, 'endTransaction');
}

beforeEach(() => {
    initializeAuditLogMock();
    context = TestUtil.getHandlerContext();
});

afterEach(async () => {
    await deleteAllMongoDBData(mongoClient);
}, 20000);


// =====================================================================
// Account Creation
// =====================================================================

describe('account creation', () => {
    test(
        'given a request with no customer policies, and customer account does not exists' +
        'when processed successfully, ' +
        'then only create the customer account for Ecliq ',
        async () => {

            const eventSource = 'ECLIQ';

            const request: Request = buildRequest([]);

            const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

            // verify the response
            expect(addCustomerResponse.statusCode).toEqual(200);
            const response: Response = JSON.parse(addCustomerResponse.body).data;
            
            expect(response.success).toEqual('Completed processing');
            expect(typeof response.id).toEqual('string');
            // verify the data in MongoDB
            const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
            const mongoCustomerPolicies = await getAllCustomerPoliciesFromMongo(mongoClient);
            expect(mongoCustomerAccounts.length).toEqual(1);
            expect(typeof mongoCustomerAccounts[0]._id).toEqual('object');
            expect(typeof mongoCustomerAccounts[0].createdDate).toEqual('object');
            expect(typeof mongoCustomerAccounts[0].lastUpdatedDate).toEqual('object');
            expect(mongoCustomerPolicies.length).toEqual(0);
        },
    );
});

// =====================================================================
// Account updation
// =====================================================================
describe('Account updation', () => {
    test('given a request with no customer policies, and customer account does not exists' +
        'when processed successfully, ' +
        'then update the customer account and not create customer policy for Ecliq', async () => {

        const request: Request = buildRequest([]);
        
        const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

        // verify the response
        expect(addCustomerResponse.statusCode).toEqual(200);
        const response: Response = JSON.parse(addCustomerResponse.body).data;
        expect(response.success).toEqual('Completed processing');
        expect(typeof response.id).toEqual('string');
        // Introduce a delay before fetching the updated data
        await sleep(1000); // Delay for 1 seconds
        const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(0);
    }, 10000);
});


describe('Account updation', () => {
    test('given a request with a customer account contains same ecliq account number ' +
        'when processed successfully, ' +
        'then do not update the customer account with ecliqNumber', async () => {

        const request: Request = buildRequest([]);
        
        let addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

        // verify the response
        expect(addCustomerResponse.statusCode).toEqual(200);

        // Introduce a delay before fetching the updated data
        await sleep(1000); // Delay for 1 second

        //adding same request again to update the account without ecliq account number
        addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

        expect(addCustomerResponse.statusCode).toEqual(200);
        const response: Response = JSON.parse(addCustomerResponse.body).data;
        expect(response.success).toEqual('Completed processing');
        expect(typeof response.id).toEqual('string');
        // verify the data in MongoDB
        const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
        expect(mongoCustomerAccounts.length).toEqual(1);
        expect(mongoCustomerAccounts[0].ecliqAccounts.length).toEqual(1);
        const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(0);
    }, 10000);
});

// =====================================================================
// Account and Policy creation
// =====================================================================
describe('Account and Policy creation', () => {
    test('Given customer account and policy does not exists, '
        + 'When processed, Then account and policy should be created for Ecliq', async () => {
        const customerPolicy1: CustomerPolicy = buildCustomerPolicy();

        const request: Request = buildRequest([customerPolicy1]);
        
        const mongoCustomerAccountsBefore = await getAllCustomerAccountsFromMongo(mongoClient);
        expect(mongoCustomerAccountsBefore.length).toEqual(0);
        const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(0);

        const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

        // verify the response
        expect(addCustomerResponse.statusCode).toEqual(200);
        const response: Response = JSON.parse(addCustomerResponse.body).data;
        expect(response.success).toEqual('Completed processing');
        expect(typeof response.id).toEqual('string');
        // Introduce a delay before fetching the updated data
        await sleep(1000); // Delay for 1 seconds
        const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
        expect(mongoCustomerAccounts.length).toEqual(1);
        expect(typeof mongoCustomerAccounts[0]._id).toEqual('object');
        expect(typeof mongoCustomerAccounts[0].createdDate).toEqual('object');
        expect(typeof mongoCustomerAccounts[0].lastUpdatedDate).toEqual('object');

        const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
        expect(mongoCustomerPoliciesAfterUpdate[0].ecliqAccountNumber).toEqual('********');
        expect(mongoCustomerPoliciesAfterUpdate[0].libertyPolicy?.ecliqId).toEqual('c9131275-ef69-4397-8b5f-4eda24713063');
    }, 10000)
});

// =====================================================================
// Policy creation
// =====================================================================
describe('Policy creation', () => {
    test('Given customer account but policy does not exists, '
        + 'When processed, Then policy should be created for Ecliq', async () => {
        const createdDate = new Date('2025-01-14T14:10:30.000+0000');
        const insertedCustomerAccount = await insertCustomerAccount(createdDate);


        const customerPolicy1: CustomerPolicy = buildCustomerPolicy();

        const request: Request = buildRequest([customerPolicy1]);

        const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(0);

        const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

        // verify the response
        expect(addCustomerResponse.statusCode).toEqual(200);
        const response: Response = JSON.parse(addCustomerResponse.body).data;
        expect(response.success).toEqual('Completed processing');
        expect(typeof response.id).toEqual('string');
        // Introduce a delay before fetching the updated data
        await sleep(1000); // Delay for 1 seconds
        const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
        expect(mongoCustomerPoliciesAfterUpdate[0].ecliqAccountNumber).toEqual('********');
    }, 10000)
});

// =====================================================================
// Policy updation
// =====================================================================
describe('Policy updation', () => {
    test('Given customer account and policy exists, '
        + 'When processed, Then source data should be updated in the existing policy', async () => {
        const createdDate = new Date('2025-01-14T14:10:30.000+0000');
        const insertedCustomerAccount = await insertCustomerAccount(createdDate);

        const customerPolicy: CustomerPolicyModel = {
            customerAccountId: insertedCustomerAccount.insertedId,
            lastUpdatedDate: createdDate,
            createdDate: createdDate,
            priorPolicy: {
                policyNumber: '987654',
                lineOfBusiness: 'AUTOB'
            },
            libertyPolicy: {
                effectiveDate: new Date('2024-12-04'),
                ecliqId: 'c9131275-ef69-4397-8b5f-4eda24713063',
            }
        };
        const insertedCustomerPolicy = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);
        const customerPolicy1: CustomerPolicy = buildCustomerPolicy();
        const request: Request = buildRequest([customerPolicy1])

        const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);
        expect(mongoCustomerPoliciesBeforeUpdate[0].ecliqAccountNumber).toBeUndefined();

        const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

        // verify the response
        expect(addCustomerResponse.statusCode).toEqual(200);
        const response: Response = JSON.parse(addCustomerResponse.body).data;
        expect(response.success).toEqual('Completed processing');
        expect(typeof response.id).toEqual('string');
        // Introduce a delay before fetching the updated data
        await sleep(1000); // Delay for 1 seconds
        const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
        expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
        expect(mongoCustomerPoliciesAfterUpdate[0].libertyPolicy?.ecliqId).toEqual('c9131275-ef69-4397-8b5f-4eda24713063');

    }, 10000)

    test('Given customer account and policy exists, and request has EBLIA or LL line of business, '
      + 'When processed, Then new policy should be created', async () => {
      const createdDate = new Date('2025-01-14T14:10:30.000+0000');
      const insertedCustomerAccount = await insertCustomerAccount(createdDate);

      const customerPolicy: CustomerPolicyModel = {
          customerAccountId: insertedCustomerAccount.insertedId,
          lastUpdatedDate: createdDate,
          createdDate: createdDate,
          priorPolicy: {
            policyNumber: '987654',
            lineOfBusiness: 'EBLIA'
        }
      };
      const insertedCustomerPolicy = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);
      const customerPolicy1: CustomerPolicy = buildCustomerPolicy('LL');
      
      const request: Request = buildRequest([customerPolicy1])

      const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);
      expect(mongoCustomerPoliciesBeforeUpdate[0].ecliqAccountNumber).toBeUndefined();

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

      // verify the response
      expect(addCustomerResponse.statusCode).toEqual(200);
      const response: Response = JSON.parse(addCustomerResponse.body).data;
      expect(response.success).toEqual('Completed processing');
      expect(typeof response.id).toEqual('string');
      // Introduce a delay before fetching the updated data
      await sleep(1000); // Delay for 1 seconds
      const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(2);
      expect(mongoCustomerPoliciesAfterUpdate[1].libertyPolicy?.lineOfBusiness).toEqual('LL');

  }, 10000)

  test('Given customer account and policy exists, and request has package line of business, '
    + 'When processed, Then source data should be updated in the existing policy', async () => {
    const createdDate = new Date('2025-01-14T14:10:30.000+0000');
    const insertedCustomerAccount = await insertCustomerAccount(createdDate);

    const customerPolicy: CustomerPolicyModel = {
        customerAccountId: insertedCustomerAccount.insertedId,
        ecliqAccountNumber: '********',
        lastUpdatedDate: createdDate,
        createdDate: createdDate,
        priorPolicy: {
          policyNumber: '987654',
          lineOfBusiness: 'BOP'
      }
    };
    const insertedCustomerPolicy = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);
    const customerPolicy1: CustomerPolicy = buildCustomerPolicy('CPSP');
    
    const request: Request = buildRequest([customerPolicy1])

    const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
    expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);

    const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

    // verify the response
    expect(addCustomerResponse.statusCode).toEqual(200);
    const response: Response = JSON.parse(addCustomerResponse.body).data;
    expect(response.success).toEqual('Completed processing');
    expect(typeof response.id).toEqual('string');
    // Introduce a delay before fetching the updated data
    await sleep(1000); // Delay for 1 seconds
    const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
    expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
    expect(mongoCustomerPoliciesAfterUpdate[0].libertyPolicy?.lineOfBusiness).toEqual('CPSP');

}, 10000)

test('Given customer account and policy exists, and request has not mapped line of business, '
  + 'When processed, Then source data should be updated in the existing package policy', async () => {
  const createdDate = new Date('2025-01-14T14:10:30.000+0000');
  const insertedCustomerAccount = await insertCustomerAccount(createdDate);

  const customerPolicy: CustomerPolicyModel = {
      customerAccountId: insertedCustomerAccount.insertedId,
      ecliqAccountNumber: '********',
      lastUpdatedDate: createdDate,
      createdDate: createdDate,
      priorPolicy: {
        policyNumber: '987654',
        lineOfBusiness: 'CPKGE'
    }
  };
  const insertedCustomerPolicy = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);
  const customerPolicy1: CustomerPolicy = buildCustomerPolicy('GOPI');
  
  const request: Request = buildRequest([customerPolicy1])

  const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
  expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);

  const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

  // verify the response
  expect(addCustomerResponse.statusCode).toEqual(200);
  const response: Response = JSON.parse(addCustomerResponse.body).data;
  expect(response.success).toEqual('Completed processing');
  expect(typeof response.id).toEqual('string');
  // Introduce a delay before fetching the updated data
  await sleep(1000); // Delay for 1 seconds
  const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
  expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
  expect(mongoCustomerPoliciesAfterUpdate[0].libertyPolicy?.lineOfBusiness).toEqual('GOPI');

}, 10000)

test('Given customer policy with EBLIA lob has ecliqId, and the request policy has EBLIA lob and mathcing ecliqId, '
  + 'When processed, Then source data should be updated in the existing policy', async () => {
  const createdDate = new Date('2025-01-14T14:10:30.000+0000');
  const insertedCustomerAccount = await insertCustomerAccount(createdDate);

  const customerPolicy: CustomerPolicyModel = {
      customerAccountId: insertedCustomerAccount.insertedId,
      ecliqAccountNumber: '********',
      lastUpdatedDate: createdDate,
      createdDate: createdDate,
      libertyPolicy: {
        effectiveDate: new Date('2024-12-04'),
        ecliqId: 'c9131275-ef69-4397-8b5f-4eda24713063',
        lineOfBusiness: 'EBLIA'
      },
  };
  const insertedCustomerPolicy = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);
  const customerPolicy1: CustomerPolicy = buildCustomerPolicy('EBLIA');
  
  const request: Request = buildRequest([customerPolicy1])

  const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
  expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);

  const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

  // verify the response
  expect(addCustomerResponse.statusCode).toEqual(200);
  const response: Response = JSON.parse(addCustomerResponse.body).data;
  expect(response.success).toEqual('Completed processing');
  expect(typeof response.id).toEqual('string');
  // Introduce a delay before fetching the updated data
  await sleep(1000); // Delay for 1 seconds
  const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
  expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
  expect(mongoCustomerPoliciesAfterUpdate[0].libertyPolicy?.lineOfBusiness).toEqual('EBLIA');

}, 10000)

test('Given customer policy with EBLIA lob has ecliqId, and the request policy has EBLIA lob and different ecliqId, '
  + 'When processed, Then new policy should be created', async () => {
  const createdDate = new Date('2025-01-14T14:10:30.000+0000');
  const insertedCustomerAccount = await insertCustomerAccount(createdDate);

  const customerPolicy: CustomerPolicyModel = {
      customerAccountId: insertedCustomerAccount.insertedId,
      ecliqAccountNumber: '********',
      lastUpdatedDate: createdDate,
      createdDate: createdDate,
      libertyPolicy: {
        effectiveDate: new Date('2024-12-04'),
        ecliqId: 'test-ecliq-id',
        lineOfBusiness: 'EBLIA'
      },
  };
  const insertedCustomerPolicy = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);
  const customerPolicy1: CustomerPolicy = buildCustomerPolicy('EBLIA');
  
  
  const request: Request = buildRequest([customerPolicy1])

  const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
  expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);
  expect(mongoCustomerPoliciesBeforeUpdate[0].libertyPolicy?.lineOfBusiness).toEqual('EBLIA');
  expect(mongoCustomerPoliciesBeforeUpdate[0].libertyPolicy?.ecliqId).toEqual('test-ecliq-id');

  const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

  // verify the response
  expect(addCustomerResponse.statusCode).toEqual(200);
  const response: Response = JSON.parse(addCustomerResponse.body).data;
  expect(response.success).toEqual('Completed processing');
  expect(typeof response.id).toEqual('string');
  // Introduce a delay before fetching the updated data
  await sleep(1000); // Delay for 1 seconds
  const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
  expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(2);
  expect(mongoCustomerPoliciesAfterUpdate[1].libertyPolicy?.lineOfBusiness).toEqual('EBLIA');
  expect(mongoCustomerPoliciesAfterUpdate[1].libertyPolicy?.ecliqId).toEqual('c9131275-ef69-4397-8b5f-4eda24713063');

}, 10000)
});

function buildCustomerPolicy(lob: string = 'AUTOB') {
    return {
        ecliqAccountNumber: '********',
        libertyPolicy: {
            effectiveDate: new Date('2024-12-04'),
            ecliqId: 'c9131275-ef69-4397-8b5f-4eda24713063',
            lineOfBusiness: lob
        },
        salesforce: {
            policyDetailsId: '1235'
        }
    } as CustomerPolicy;
}

function buildRequest(policies: CustomerPolicy[]) {
    return {
        searchCriteria: {
            ecliqAccountNumber: '********',
            customerMetadata: {
                btCode: 2,
                customerName: 'Test Customer'.toUpperCase(),
                address1: '1234 Test Lane',
                address2: '',
                city: 'Fremont',
                state: 'CA',
                zip: '94538',
            },
        },
        customerMetadata: {
            btCode: 2,
            customerName: 'Test Customer',
            address1: '1234 Test Lane',
            address2: '',
            city: 'Fremont',
            state: 'CA',
            zip: '94538',
        },
        eventSource: 'ECLIQ',
        customerPolicies: policies,
        ecliqAccounts: [
            {
                ecliqAccountNumber: '********'
            }

        ],
    } as Request;
}

function sleep(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function insertCustomerAccount(createdDate: Date) {
    let customerMetadataMap: CustomerMetadataMap<string, CustomerMetadataModel> = {
        'AQE': {
            customerMetadata: {
                btCode: 2,
                customerName: 'Test Customer'.toUpperCase(),
                address1: '1234 Test Lane',
                address2: '',
                city: 'Fremont',
                state: 'CA',
                zip: '94538',
                addressLastUpdatedDate: createdDate,
            } as CustomerMetadata,
            lastUpdatedDate: createdDate,
            createdDate: createdDate,
        } as CustomerMetadataModel
    };
    const customerAccount: CustomerAccountModel = {
        customerMetadataMap: customerMetadataMap,
        ecliqAccounts: [
            {
                ecliqAccountNumber: '12345',
                ecliqAccountCreationDate: new Date(),
            },
        ],
        lastUpdatedDate: createdDate,
        createdDate: createdDate,
    };
    return await insertCustomerAccountIntoMongoDB(mongoClient, customerAccount);
}
