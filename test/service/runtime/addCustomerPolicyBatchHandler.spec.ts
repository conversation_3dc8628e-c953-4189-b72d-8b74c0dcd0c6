import { Context } from 'aws-lambda';
import * as Hand<PERSON> from '../../../lib/service/runtime/addCustomerPolicyBatchHandler';
import Request from '../../../types/request';
import * as AWS from 'aws-sdk';
import { <PERSON>ail<PERSON>elper } from '../../../lib/service/utility/emailHelper';
import MockAdapter from 'axios-mock-adapter';
import axios from 'axios';
import {AddressDetails} from '../../../model/addressModels';
import * as TestUtil from './util/test-util';
import { FeatureService } from '../../../lib/service/utility/featureService';
import { AddressHelper } from '../../../lib/service/utility/addressHelper';
import { AuditLogHelper } from '../../../lib/service/utility/auditLogHelper';
// Create a mock adapter for axios
const mock = new MockAdapter(axios);

jest.mock('../../../lib/service/utility/addressHelper');
jest.mock('../../../lib/service/utility/emailHelper');
jest.mock('../../../lib/service/utility/featureService');

// Mock the AddressHelper to use the mocked axios instance
const ADDRESS_DETAILS: AddressDetails = {
    addressLine: '1234 Test Lane',
    city: 'Freemont',
    state: 'CA',
    postalCodeInfo: {
        postalCodeClassification: 'zip5',
        postalCode: '55555',
        postalCodeExtension: '4444',
        postalCodePlus4: '5555',
        postalCodeHyphenPlus4: '94538',
    },
    unit: {
        unitNumber: '1234',
        unitType: 'APT',
    },
    houseNumber: '',
};
jest.mock('../../../lib/service/utility/addressHelper', () => ({
    ...jest.requireActual('../../../lib/service/utility/addressHelper'),
    AddressHelper: {
        bulkCleanseAddresses: jest.fn().mockImplementation((addresses) => {
            const cleansedAddresses: { [key: string]: AddressDetails } = {};
            for (const key in addresses) {
                cleansedAddresses[key] = structuredClone(ADDRESS_DETAILS);
            }
            return Promise.resolve(cleansedAddresses);
        }),
        assignAddressDetailsToCustomerMetadata: jest.fn().mockImplementation((customerMetadata, addressDetails) => {

        })
    }
}));

jest.mock('../../../lib/service/utility/featureService', () => ({
  ...jest.requireActual('../../../lib/service/utility/featureService'),
  FeatureService: {
    checkFeatureLaunch: jest.fn().mockImplementation((featureName, identifier) => {
        return Promise.resolve(true);
      })
  }
}));

jest.mock('@lmig/audit-log-node-starter', () => {
    return {
        ...jest.requireActual('@lmig/audit-log-node-starter'),
        AuditLogService: {
            initialize: jest.fn().mockResolvedValue({
                startTransaction: jest.fn(),
                addTransactionEvent: jest.fn(),
                endTransaction: jest.fn(),
            }),
        },
    };
});

jest.mock('../../../lib/service/utility/emailHelper', () => ({
    ...jest.requireActual('../../../lib/service/utility/emailHelper'),
    EmailHelper: {
        sendEmail: jest.fn().mockImplementation((url, emailRequestBody) => {
            return Promise.resolve({
                status: 200,
                statusText: 'OK',
                headers: {},
                config: {},
                data: {}
            })
        }),
        buildEmailRequest: jest.fn().mockImplementation((requests, subject) => {
        })
    }
}));

let context: Context;


beforeEach(() => {
    context = TestUtil.getHandlerContext();
});

jest.mock('aws-sdk', () => {
  const mLambda = {
      invoke: jest.fn().mockReturnThis(),
      promise: jest.fn(),
  };
  return { Lambda: jest.fn(() => mLambda) };
});

jest.mock('aws-xray-sdk', () => {
  const mockXRay = {
      captureAWS: jest.fn((AWS) => AWS), // Return the AWS SDK without modification
      getSegment: jest.fn(() => ({
          addAnnotation: jest.fn(),
          addError: jest.fn(),
          close: jest.fn(),
      })),
  };
  return mockXRay;
});

describe('batch processing', () => {
  let mockLambda: jest.Mocked<AWS.Lambda>;

  beforeEach(() => {
    mockLambda = new AWS.Lambda() as jest.Mocked<AWS.Lambda>;
    mock.reset();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should validate and process 1 request successfully', async () => {
      const endTransactionEventSpy = await initializeAuditLogMock();
      const event = {
      body: JSON.stringify([{ 
        searchCriteria: {btCode: '123'},
          transactionId: '1234',
        customerMetadata: { btCode: '123', customerName: 'John Doe', address1: '123 Main St', city: 'Anytown', state: 'NY', zip: '12345' } }]),
    };

    (mockLambda.invoke as jest.Mock).mockReturnValue({
      promise: jest.fn().mockResolvedValue({
          Payload: JSON.stringify({ body: JSON.stringify({ success: 'Completed processing' }) }),
      }),
    });

    const response = await Handler.addCustomerPolicyBatchLambda(event, context);

    expect(mockLambda.invoke).toHaveBeenCalledTimes(1);
    expect(FeatureService.checkFeatureLaunch).toHaveBeenCalledTimes(1);
    expect(AddressHelper.bulkCleanseAddresses).toHaveBeenCalledTimes(1);
    expect(EmailHelper.sendEmail).toHaveBeenCalledTimes(0);
    expect(endTransactionEventSpy).toHaveBeenCalledTimes(1);
    expect(response.statusCode).toBe(200);
    const responseBody = JSON.parse(response.body);
    expect(responseBody.success).toBe('Completed processing');
    expect(responseBody.data).toBeDefined();
    expect(responseBody.data.length).toBe(1);
  });

  test('should validate and process 3 request successfully', async () => {
    const event = {
      body: JSON.stringify([
        { searchCriteria: {btCode: '123'},
          customerMetadata: { btCode: '123', customerName: 'John Doe', address1: '123 Main St', city: 'Anytown', state: 'NY', zip: '12345' } },
        { searchCriteria: {btCode: '124'},
          customerMetadata: { btCode: '124', customerName: 'John Doe1', address1: '124 Main St', city: 'Anytown', state: 'NY', zip: '12345' } },
        { searchCriteria: {btCode: '125'},
          customerMetadata: { btCode: '125', customerName: 'John Doe2', address1: '125 Main St', city: 'Anytown', state: 'NY', zip: '12345' } }
      ]),
    };

    (mockLambda.invoke as jest.Mock).mockReturnValue({
      promise: jest.fn().mockResolvedValue({
          Payload: JSON.stringify({ body: JSON.stringify({ success: 'Completed processing', data: { statusCode: 200, success: 'Completed processing'} }) }),
      }),
    });

    const response = await Handler.addCustomerPolicyBatchLambda(event, context);

    expect(mockLambda.invoke).toHaveBeenCalledTimes(3);
    expect(FeatureService.checkFeatureLaunch).toHaveBeenCalledTimes(1);
    expect(AddressHelper.bulkCleanseAddresses).toHaveBeenCalledTimes(1);
    expect(EmailHelper.sendEmail).toHaveBeenCalledTimes(0);

    expect(response.statusCode).toBe(200);
    const responseBody = JSON.parse(response.body);
    expect(responseBody.success).toBe('Completed processing');
    expect(responseBody.data).toBeDefined();
    expect(responseBody.data.length).toBe(3);
    console.log(responseBody.data);
    expect(responseBody.data[0].success).toBe('Completed processing');
    expect(responseBody.data[1].success).toBe('Completed processing');
    expect(responseBody.data[2].success).toBe('Completed processing');
  });

  test('should validate and process 3 request successfully, 2 valid requests and one invalid', async () => {
    const event = {
      body: JSON.stringify([{ searchCriteria: {btCode: '123'},
        customerMetadata: { btCode: '123', customerName: 'John Doe', address1: '123 Main St', city: 'Anytown', state: 'NY', zip: '12345' } },
        { searchCriteria: {btCode: '124'},
          customerMetadata: { btCode: '124', customerName: '', address1: '124 Main St', city: 'Anytown', state: 'NY', zip: '12345' } },
        { searchCriteria: {btCode: '125'},
          customerMetadata: { btCode: '125', customerName: 'John Doe2', address1: '125 Main St', city: 'Anytown', state: 'NY', zip: '12345' } }
      ]),
    };

    (mockLambda.invoke as jest.Mock).mockReturnValue({
      promise: jest.fn().mockResolvedValue({
          Payload: JSON.stringify({ body: JSON.stringify({ success: 'Completed processing', data: { statusCode: 200, success: 'Completed processing'}}) }),
      }),
    });

    const response = await Handler.addCustomerPolicyBatchLambda(event, context);

    expect(mockLambda.invoke).toHaveBeenCalledTimes(2);
    expect(FeatureService.checkFeatureLaunch).toHaveBeenCalledTimes(1);
    expect(AddressHelper.bulkCleanseAddresses).toHaveBeenCalledTimes(1);
    expect(EmailHelper.sendEmail).toHaveBeenCalledTimes(1);

    expect(response.statusCode).toBe(200);
    const responseBody = JSON.parse(response.body);
    expect(responseBody.success).toBe('Completed processing');
    expect(responseBody.data).toBeDefined();
    expect(responseBody.data.length).toBe(3);
    expect(responseBody.data[0].success).toBe('Completed processing');
    expect(responseBody.data[1].error).toBe('Invalid customerMetadata');
    expect(responseBody.data[2].success).toBe('Completed processing');
  });

  test('should send email for invalid customer metadata', async () => {
    const event = {
      body: JSON.stringify([{ customerMetadata: { btCode: '', customerName: '', address1: '', city: '', state: '', zip: '' } }]),
    };

    const response = await Handler.addCustomerPolicyBatchLambda(event, context);

    expect(mockLambda.invoke).toHaveBeenCalledTimes(0);
    expect(FeatureService.checkFeatureLaunch).toHaveBeenCalledTimes(1);
    expect(AddressHelper.bulkCleanseAddresses).toHaveBeenCalledTimes(0);
    expect(EmailHelper.sendEmail).toHaveBeenCalledTimes(1);

    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body).success).toBe('Completed processing');
  });

  test('should not call address cleanse when feature not enabled', async () => {
    const event = {
      body: JSON.stringify([{ searchCriteria: {btCode: '124'},
        customerMetadata: { btCode: '124', customerName: 'John Doe1', address1: '124 Main St', city: 'Anytown', state: 'NY', zip: '12345' } }]),
    };

    (FeatureService.checkFeatureLaunch as jest.Mock).mockReturnValue(false);

    const response = await Handler.addCustomerPolicyBatchLambda(event, context);

    expect(mockLambda.invoke).toHaveBeenCalledTimes(1);
    expect(FeatureService.checkFeatureLaunch).toHaveBeenCalledTimes(1);
    expect(AddressHelper.bulkCleanseAddresses).toHaveBeenCalledTimes(0);
    expect(EmailHelper.sendEmail).toHaveBeenCalledTimes(0);

    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body).success).toBe('Completed processing');
  });

  test('should return 400 for empty requests', async () => {
    const event = {
      body: JSON.stringify([]),
    };

    const response = await Handler.addCustomerPolicyBatchLambda(event, context);

    expect(mockLambda.invoke).toHaveBeenCalledTimes(0);

    expect(response.statusCode).toBe(400);
  });

  test('should return 400 for invalid requests', async () => {
    const event = {
      body: 'invalid',
    };

    const response = await Handler.addCustomerPolicyBatchLambda(event, context);

    expect(mockLambda.invoke).toHaveBeenCalledTimes(0);

    expect(response.statusCode).toBe(400);
  });

  test('should return 400 for missing searchCriteria in the request', async () => {
    const event = {
      body: JSON.stringify([{ customerMetadata: { btCode: '123', customerName: 'John Doe', address1: '123 Main St', city: 'Anytown', state: 'NY', zip: '12345' } }]),
    };

    const response = await Handler.addCustomerPolicyBatchLambda(event, context);

    expect(mockLambda.invoke).toHaveBeenCalledTimes(0);

    expect(response.statusCode).toBe(200);

    const data = JSON.parse(response.body).data;
    const status = data[0].statusCode;
    const error = data[0].error;

    expect(response.statusCode).toBe(200);
    expect(status).toEqual(400);
    expect(error).toEqual('Invalid request');
  });
    async function initializeAuditLogMock() {
        const auditLogService = (await AuditLogHelper.init()).auditLogService;

        if (!auditLogService) {
            throw new Error('auditLogService is null');
        }

        return jest.spyOn(auditLogService, 'endTransaction');
    }
});
