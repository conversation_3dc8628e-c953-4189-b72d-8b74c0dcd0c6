import { Context } from 'aws-lambda';
import * as Hand<PERSON> from '../../../lib/service/runtime/getEcliqAccountNumberHandler';
import * as TestUtil from './util/test-util';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { MongoClient, ObjectId } from 'mongodb';
import { decryptSecret } from '../../../lib/service/utility/decryptSecret';
import { throwError } from '../../../lib/service/utility/throwError';
import CustomerAccountModel, { CustomerPolicyModel } from '../../../model/customerAccountModel';
import {
  deleteAllMongoDBData,
  getAllCustomerAccountsFromMongo,
  getAllCustomerPoliciesFromMongo,
  insertCustomerAccountIntoMongoDB,
  insertCustomerPolicyIntoMongoDB,
} from './mongo-test-util';
import { EventSource } from '../../../types/enums';

jest.mock('../../../lib/service/utility/decryptSecret');
jest.mock('../../../lib/service/utility/throwError');

let context: Context;
let mongoServer: MongoMemoryServer;
let mongoClient: MongoClient;

const envBackup: { [key: string]: string | undefined } = process.env;

beforeAll(async () => {
  jest.resetModules();

  // creating MongoDB in memory
  mongoServer = await MongoMemoryServer.create();

  // backing up original env variables
  process.env = { ...envBackup };

  // generating connection string to the mongodb memory server and assigning to env variable
  process.env.MONGODB_CONNECTION = mongoServer.getUri();

  // creating a MongoClient instance for use in our test cases
  mongoClient = new MongoClient(process.env.MONGODB_CONNECTION!);

  // mocking the decryptSecret function here so that when called in the Lambda, it will use this connection string instead.
  (decryptSecret as jest.Mock).mockReturnValue(Promise.resolve(process.env.MONGODB_CONNECTION));

  // mocking error response
  (throwError as jest.Mock).mockReturnValue(new Error('mock error'));
  // (error as jest.Mock).mockImplementation(() => { new Error('mock error') });
});

afterAll(async () => {
  await mongoClient.close();
  await mongoServer.stop();

  // restoring original env variables
  process.env = envBackup;
});

beforeEach(() => {
  context = TestUtil.getHandlerContext();
});

afterEach(async () => {
  console.log('customerAccounts:', await getAllCustomerAccountsFromMongo(mongoClient));
  console.log('customerPolicies:', await getAllCustomerPoliciesFromMongo(mongoClient));
  await deleteAllMongoDBData(mongoClient);
});

test(
  'Given priorPolicyNumber and btCode and customerAccounts data is not present, ' +
    'when processed, ' +
    'empty string is returned',
  async () => {
    const event = {
      queryStringParameters: {
        priorPolicyNumber: 'L45678',
        btCode: 1,
      },
    };

    const getEcliqAccountNumberResponse = await Handler.getEcliqAccountNumberLambda(event, context);

    expect(getEcliqAccountNumberResponse.statusCode).toEqual(200);
    expect(getEcliqAccountNumberResponse.body).toEqual('');
  },
);

test(
  'Given the query parameters does not have priorPolicyNumber and btCode is present, ' +
    'when processed, ' +
    '400 is thrown',
  async () => {
    const event = {
      queryStringParameters: {
        btCode: 1,
      },
    };

    const getEcliqAccountNumberResponse = await Handler.getEcliqAccountNumberLambda(event, context);

    expect(getEcliqAccountNumberResponse.statusCode).toEqual(400);
    const response: { error: string } = JSON.parse(getEcliqAccountNumberResponse.body);
    expect(response.error).toEqual('Missing priorPolicyNumber in queryStringParameters');
  },
);

test(
  'Given the query parameters has priorPolicyNumber and btCode is not present, ' + 'when processed, ' + '400 is thrown',
  async () => {
    const event = {
      queryStringParameters: {
        priorPolicyNumber: '667c41a04162ebf292811cc2',
      },
    };

    const getEcliqAccountNumberResponse = await Handler.getEcliqAccountNumberLambda(event, context);

    expect(getEcliqAccountNumberResponse.statusCode).toEqual(400);
    const response: { error: string } = JSON.parse(getEcliqAccountNumberResponse.body);
    expect(response.error).toEqual('Missing btCode in queryStringParameters');
  },
);

test(
  'Given the query parameters has priorPolicyNumber and btCode and customerAccounts data with ecliqAccounts is present, ' +
    'when processed, ' +
    'ecliq account number is returned',
  async () => {
    const customerAccountId = new ObjectId('667c41a04162ebf292811cc2');
    const createdDate = new Date('2023-09-11T14:10:30.000+0000');
    const lastUpdatedDate = new Date('2023-09-12T14:10:30.000+0000');
    const expectedResponse = 'E123457';
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const document: CustomerAccountModel = {
      _id: customerAccountId,
      customerMetadataMap:  {
        'AQE': { 
          customerMetadata: {
            btCode: 2,
            customerName: 'Test Customer',
            address1: '1234 Test Lane',
            address2: '',
            city: 'Gotham',
            state: 'NY',
            zip: '*********',
            addressLastUpdatedDate: createdDate,
          },
          lastUpdatedDate: createdDate,
          createdDate: createdDate,
        },
      },
      ecliqAccounts: [
        {
          ecliqAccountNumber: 'not expected, should sort by date',
          ecliqAccountCreationDate: yesterday,
        },
        {
          ecliqAccountNumber: expectedResponse,
          ecliqAccountCreationDate: new Date(),
        },
      ],
      lastUpdatedDate: lastUpdatedDate,
      createdDate: createdDate,
    };
    const insertedCustomerAccount = await insertCustomerAccountIntoMongoDB(mongoClient, document);
    const customerPolicy: CustomerPolicyModel = {
      customerAccountId: insertedCustomerAccount.insertedId,
      btCode: 1,
      priorPolicy: {
        policyNumber: 'ABCD00000002',
      },
      aqe: {
      },
      lastUpdatedDate: createdDate,
      createdDate: createdDate,
      lastPolicyEvent: {
        policyEventDate: createdDate,
        policyEventType: EventSource.SALESFORCE,
      },
    };
    await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);

    const getEcliqAccountNumberResponse = await Handler.getEcliqAccountNumberLambda(
      {
        queryStringParameters: {
          priorPolicyNumber: 'ABCD00000002',
          btCode: 1,
        },
      },
      context,
    );

    expect(getEcliqAccountNumberResponse.statusCode).toEqual(200);
    expect(getEcliqAccountNumberResponse.body).toEqual(expectedResponse);
  },
);
