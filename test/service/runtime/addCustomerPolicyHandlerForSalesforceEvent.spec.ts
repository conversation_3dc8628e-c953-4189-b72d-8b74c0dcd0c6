import { Context } from 'aws-lambda';
import * as Handler from '../../../lib/service/runtime/addCustomerPolicyHandler';
import * as TestUtil from './util/test-util';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { MongoClient } from 'mongodb';
import { decryptSecret } from '../../../lib/service/utility/decryptSecret';
import { throwError } from '../../../lib/service/utility/throwError';
import Request, { CustomerMetadata, CustomerPolicy } from '../../../types/request';
import {
  deleteAllMongoDBData,
  getAllCustomerAccountsFromMongo,
  getAllCustomerPoliciesFromMongo,
  insertCustomerAccountIntoMongoDB,
  insertCustomerPolicyIntoMongoDB,
} from './mongo-test-util';
import CustomerAccountModel, { CustomerMetadataMap, CustomerMetadataModel, CustomerPolicyModel } from '../../../model/customerAccountModel';
import Response from '../../../types/response';

jest.mock('../../../lib/service/utility/decryptSecret');
jest.mock('../../../lib/service/utility/throwError');
jest.mock('../../../lib/service/utility/authenticationTokenHelper');

let context: Context;
let mongoServer: MongoMemoryServer;
let mongoClient: MongoClient;

const envBackup: { [key: string]: string | undefined } = process.env;

beforeAll(async () => {
  jest.resetModules();

  // creating MongoDB in memory
  mongoServer = await MongoMemoryServer.create();

  // backing up original env variables
  process.env = { ...envBackup };

  // generating connection string to the mongodb memory server and assigning to env variable
  process.env.MONGODB_CONNECTION = mongoServer.getUri();

  // creating a MongoClient instance for use in our test cases
  mongoClient = new MongoClient(process.env.MONGODB_CONNECTION!);

  // mocking the decryptSecret function here so that when called in the Lambda, it will use this connection string instead.
  (decryptSecret as jest.Mock).mockReturnValue(Promise.resolve(process.env.MONGODB_CONNECTION));

  // mocking error response
  (throwError as jest.Mock).mockReturnValue(new Error('mock error'));
  // (error as jest.Mock).mockImplementation(() => { new Error('mock error') });
});

afterAll(async () => {
  await mongoClient.close();
  await mongoServer.stop();

  // restoring original env variables
  process.env = envBackup;
});

beforeEach(() => {
  context = TestUtil.getHandlerContext();
});

afterEach(async () => {
  await deleteAllMongoDBData(mongoClient);
}, 20000);


// =====================================================================
// Account Creation
// =====================================================================

describe('account creation', () => {
  test(
    'given a request with no customer policies, and customer account does not exists' +
      'when processed successfully, ' +
      'then only create the customer account ',
    async () => {

      const eventSource = 'SALESFORCE';

      let request: Request = buildRequest([]);
      request.quoteEntryId = 'Q-1234';
      request.quoterComments = 'Test comment';

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

      // verify the response
      expect(addCustomerResponse.statusCode).toEqual(200);
      const response: Response = JSON.parse(addCustomerResponse.body).data;
      expect(response.success).toEqual('Completed processing');
      expect(typeof response.id).toEqual('string');
      // verify the data in MongoDB
      const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
      const mongoCustomerPolicies = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerAccounts.length).toEqual(1);
      expect(typeof mongoCustomerAccounts[0]._id).toEqual('object');
      expect(typeof mongoCustomerAccounts[0].createdDate).toEqual('object');
      expect(typeof mongoCustomerAccounts[0].lastUpdatedDate).toEqual('object');
      expect(mongoCustomerAccounts[0].quoteEntryIds).toContain('Q-1234');
      expect(mongoCustomerAccounts[0].quoterComments).toEqual('Test comment');
      expect(mongoCustomerPolicies.length).toEqual(0);
    },
  );
});

// =====================================================================
// Account updation
// =====================================================================
describe('Account updation', () => {
  test('given a request with no customer policies, and customer account does not exists' +
      'when processed successfully, ' +
      'then update the customer account and not create customer policy', async () => {
      const createdDate = new Date('2023-09-11T14:10:30.000+0000');
      const insertedCustomerAccount = await insertCustomerAccount(createdDate);

      const request: Request = buildRequest([]);
      request.quoteEntryId = 'Q-1234';
      request.quoterComments = 'Test comment';
     
      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

      // verify the response
      expect(addCustomerResponse.statusCode).toEqual(200);
      const response: Response = JSON.parse(addCustomerResponse.body).data;
      expect(response.success).toEqual('Completed processing');
      expect(typeof response.id).toEqual('string');
      // Introduce a delay before fetching the updated data
      await sleep(1000); // Delay for 1 seconds
      const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
      expect(mongoCustomerAccounts[0].quoteEntryIds).toContain('Q-1234');
      expect(mongoCustomerAccounts[0].quoterComments).toEqual('Test comment');
      const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(0);
  }, 10000);

  test('given a request with quotentryid and quotercomments, and customer account does exists' +
    'when processed successfully, ' +
    'then update the customer account with quoterid and quotercomments', async () => {

    let request: Request = buildRequest([]);
    request.quoteEntryId = 'Q-1234';
   
    let addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

    // verify the response
    expect(addCustomerResponse.statusCode).toEqual(200);
    const response: Response = JSON.parse(addCustomerResponse.body).data;
    expect(response.success).toEqual('Completed processing');
    expect(typeof response.id).toEqual('string');
    // Introduce a delay before fetching the updated data
    await sleep(1000); // Delay for 1 seconds
    let mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
    expect(mongoCustomerAccounts[0].quoteEntryIds).toContain('Q-1234');
    expect(mongoCustomerAccounts[0].quoterComments).toEqual('');
    
    request.quoteEntryId = 'Q-1235';
    request.quoterComments = 'Test comment';
    
    addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);
    await sleep(1000); // Delay for 1 seconds
    mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
    expect(mongoCustomerAccounts[0].quoteEntryIds?.length).toEqual(2);
    expect(mongoCustomerAccounts[0].quoteEntryIds).toContain('Q-1234');
    expect(mongoCustomerAccounts[0].quoteEntryIds).toContain('Q-1235');
    expect(mongoCustomerAccounts[0].quoterComments).toEqual('Test comment');

}, 10000);
});

// =====================================================================
// Account and Policy creation
// =====================================================================
describe('Account and Policy creation', () => {
  test('Given customer account and policy does not exists, '
    + 'When processed, Then account and policy should be created', async () => {
      const customerPolicy1: CustomerPolicy = buildCustomerPolicy();

      const request: Request = buildRequest([customerPolicy1]);

      const mongoCustomerAccountsBefore = await getAllCustomerAccountsFromMongo(mongoClient);
      expect(mongoCustomerAccountsBefore.length).toEqual(0);
      const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(0);

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

      // verify the response
      expect(addCustomerResponse.statusCode).toEqual(200);
      const response: Response = JSON.parse(addCustomerResponse.body).data;
      expect(response.success).toEqual('Completed processing');
      expect(typeof response.id).toEqual('string');
      // Introduce a delay before fetching the updated data
      await sleep(1000); // Delay for 1 seconds
      const mongoCustomerAccounts = await getAllCustomerAccountsFromMongo(mongoClient);
      expect(mongoCustomerAccounts.length).toEqual(1);
      expect(typeof mongoCustomerAccounts[0]._id).toEqual('object');
      expect(typeof mongoCustomerAccounts[0].createdDate).toEqual('object');
      expect(typeof mongoCustomerAccounts[0].lastUpdatedDate).toEqual('object');

      const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
      expect(mongoCustomerPoliciesAfterUpdate[0].salesforce?.policyDetailsId).toEqual('1235');
  }, 10000)
});

// =====================================================================
// Policy creation
// =====================================================================
describe('Policy creation', () => {
  test('Given customer account but policy does not exists, '
    + 'When processed, Then policy should be created', async () => {
      const createdDate = new Date('2023-09-11T14:10:30.000+0000');
      const insertedCustomerAccount = await insertCustomerAccount(createdDate);


      const customerPolicy1: CustomerPolicy = buildCustomerPolicy();

      const request: Request = buildRequest([customerPolicy1]);

      const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(0);

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

      // verify the response
      expect(addCustomerResponse.statusCode).toEqual(200);
      const response: Response = JSON.parse(addCustomerResponse.body).data;
      expect(response.success).toEqual('Completed processing');
      expect(typeof response.id).toEqual('string');
      // Introduce a delay before fetching the updated data
      await sleep(1000); // Delay for 1 seconds
      const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
      expect(mongoCustomerPoliciesAfterUpdate[0].salesforce?.policyDetailsId).toEqual('1235');
  }, 10000)
});

// =====================================================================
// Policy updation
// =====================================================================
describe('Policy updation', () => {
  test('Given customer account and policy exists, '
    + 'When processed, Then source data should be updated in the existing policy', async () => {
      const createdDate = new Date('2023-09-11T14:10:30.000+0000');
      const insertedCustomerAccount = await insertCustomerAccount(createdDate);

      const customerPolicy: CustomerPolicyModel = {
        customerAccountId: insertedCustomerAccount.insertedId,
        lastUpdatedDate: createdDate,
        createdDate: createdDate,
        priorPolicy: {
          policyNumber: '987654',
          lineOfBusiness: 'AUTOB'
        }
      };
      const insertedCustomerPolicy = await insertCustomerPolicyIntoMongoDB(mongoClient, customerPolicy);


      const customerPolicy1: CustomerPolicy = buildCustomerPolicy();

      const request: Request = buildRequest([customerPolicy1])

      const mongoCustomerPoliciesBeforeUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesBeforeUpdate.length).toEqual(1);
      expect(mongoCustomerPoliciesBeforeUpdate[0].salesforce?.policyDetailsId).toBeUndefined();

      const addCustomerResponse = await Handler.addCustomerPolicyLambda(request, context);

      // verify the response
      expect(addCustomerResponse.statusCode).toEqual(200);
      const response: Response = JSON.parse(addCustomerResponse.body).data;
      expect(response.success).toEqual('Completed processing');
      expect(typeof response.id).toEqual('string');
      // Introduce a delay before fetching the updated data
      await sleep(1000); // Delay for 1 seconds
      const mongoCustomerPoliciesAfterUpdate = await getAllCustomerPoliciesFromMongo(mongoClient);
      expect(mongoCustomerPoliciesAfterUpdate.length).toEqual(1);
      expect(mongoCustomerPoliciesAfterUpdate[0].salesforce?.policyDetailsId).toEqual('1235');
  }, 10000)
});

function buildCustomerPolicy() {
  return {
    priorPolicy: {
      lineOfBusiness: 'AUTOB',
      policyNumber: '987654',
    },
    libertyPolicy: {
      effectiveDate: new Date('2024-12-04')
    },
    salesforce: {
      policyDetailsId: '1235'
    }
  } as CustomerPolicy;
}

function buildRequest(policies: CustomerPolicy[]) {
  return {
    searchCriteria: {
      customerMetadata: {
        btCode: 2,
        customerName: 'Test Customer'.toUpperCase(),
        address1: '1234 Test Lane',
        address2: '',
        city: 'Gotham',
        state: 'NY',
        zip: '*********',
      },
    },
    customerMetadata: {
      btCode: 2,
      customerName: 'Test Customer'.toUpperCase(),
      address1: '1234 Test Lane',
      address2: '',
      city: 'Gotham',
      state: 'NY',
      zip: '*********',
    },
    eventSource: 'SALESFORCE',
    customerPolicies: policies,
    ecliqAccounts: [],
  } as Request;
}

function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function insertCustomerAccount(createdDate: Date) {
  let customerMetadataMap: CustomerMetadataMap<string, CustomerMetadataModel> = {
    'AQE': {
      customerMetadata: {
        btCode: 2,
        customerName: 'Test Customer'.toUpperCase(),
        address1: '1234 Test Lane',
        address2: '',
        city: 'Gotham',
        state: 'NY',
        zip: '*********',
        addressLastUpdatedDate: createdDate,
      } as CustomerMetadata,
      lastUpdatedDate: createdDate,
      createdDate: createdDate,
    } as CustomerMetadataModel
  };
  const customerAccount: CustomerAccountModel = {
    customerMetadataMap: customerMetadataMap,
    ecliqAccounts: [
      {
        ecliqAccountNumber: '12345',
        ecliqAccountCreationDate: new Date(),
      },
    ],
    lastUpdatedDate: createdDate,
    createdDate: createdDate,
  };
  return await insertCustomerAccountIntoMongoDB(mongoClient, customerAccount);
}
