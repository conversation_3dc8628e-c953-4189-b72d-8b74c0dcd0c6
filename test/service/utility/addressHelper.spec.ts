import axios from 'axios';
import {
  AddressHelper,
  createAddressLastLine,
  createUnparsedAddressRequest,
} from '../../../lib/service/utility/addressHelper';
import { getCustomerAccountClientAuthToken } from '../../../lib/service/utility/authenticationTokenHelper';
import { logger } from '../../../lib/service/utility/logger';
import { CustomerMetadata } from '../../../types/request';
import { Address } from '../../../model/addressModels';

jest.mock('axios');
jest.mock('axios-retry');
jest.mock('../../../lib/service/utility/authenticationTokenHelper');
jest.mock('../../../lib/service/utility/logger');

jest.mock('../../../lib/service/utility/addressHelper', () => {
  return {
    ...jest.requireActual('../../../lib/service/utility/addressHelper'),
    createAddressLastLine: jest.fn(),
    createUnparsedAddressRequest: jest.fn(),
  };
});

describe('AddressHelper - processAddress', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should process address successfully', async () => {
    const mockToken = 'mockToken';
    const mockResponse = {
      data: {
        addresses: [
          {
            addressRequestId: '1',
            address: {
              addressLine: '123 Main St',
              city: 'Anytown',
              state: 'CA',
              postalCodeInfo: { postalCode: '12345' },
              unit: { unitType: 'Apt', unitNumber: '1' },
              houseNumber: '123',
            },
          },
        ],
      },
    };

    (getCustomerAccountClientAuthToken as jest.Mock).mockResolvedValue(mockToken);
    (createAddressLastLine as jest.Mock).mockReturnValue('Anytown, CA 12345');
    (createUnparsedAddressRequest as jest.Mock).mockReturnValue({ unparsedAddress: '123 Main St, Anytown, CA 12345' });
    (axios.request as jest.Mock).mockResolvedValue(mockResponse);

    const result = await AddressHelper.processAddress('123 Main St', '', 'Anytown', 'CA', '12345', 'US');

    expect(result).toEqual({
      address1: '123 Main St',
      address2: 'Apt 1',
      city: 'Anytown',
      state: 'CA',
      zip: '12345',
    });
    expect(logger.info).toHaveBeenCalled();
  });

  test('should handle errors during address processing', async () => {
    const mockError = new Error('Network Error');
    (getCustomerAccountClientAuthToken as jest.Mock).mockRejectedValue(mockError);

    const result = await AddressHelper.processAddress('123 Main St', '', 'Anytown', 'CA', '12345', 'US');

    expect(result).toEqual({});
    expect(logger.error).toHaveBeenCalledWith('Error in address cleanse method execution: ', mockError.message);
  });

  test('should handle 403 error response', async () => {
    const mockToken = 'mockToken';
    const mockError = {
      response: {
        status: 403,
        data: 'Forbidden',
      },
    };

    (getCustomerAccountClientAuthToken as jest.Mock).mockResolvedValue(mockToken);
    (createAddressLastLine as jest.Mock).mockReturnValue('Anytown, CA 12345');
    (createUnparsedAddressRequest as jest.Mock).mockReturnValue({ unparsedAddress: '123 Main St, Anytown, CA 12345' });
    (axios.request as jest.Mock).mockRejectedValue(mockError);

    const result = await AddressHelper.processAddress('123 Main St', '', 'Anytown', 'CA', '12345', 'US');

    expect(result).toEqual({});
    expect(logger.error).toHaveBeenCalledWith('Error response data:', 'Forbidden');
    expect(logger.error).toHaveBeenCalledWith('Error response status:', 403);
  });

  test('should handle 500 error response', async () => {
    const mockToken = 'mockToken';
    const mockError = {
      response: {
        status: 500,
        data: 'Internal Server Error',
      },
    };

    (getCustomerAccountClientAuthToken as jest.Mock).mockResolvedValue(mockToken);
    (createAddressLastLine as jest.Mock).mockReturnValue('Anytown, CA 12345');
    (createUnparsedAddressRequest as jest.Mock).mockReturnValue({ unparsedAddress: '123 Main St, Anytown, CA 12345' });
    (axios.request as jest.Mock).mockRejectedValue(mockError);

    const result = await AddressHelper.processAddress('123 Main St', '', 'Anytown', 'CA', '12345', 'US');

    expect(result).toEqual({});
    expect(logger.error).toHaveBeenCalledWith('Error response data:', 'Internal Server Error');
    expect(logger.error).toHaveBeenCalledWith('Error response status:', 500);
  });
});

describe('AddressHelper - bulkCleanseAddresses', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should process address successfully', async () => {
    const mockToken = 'mockToken';
    const addressCleanseAddress: Address = {
      addressLine: '123 Main St',
      city: 'Anytown',
      state: 'CA',
      postalCodeInfo: {
        postalCode: '12345',
        postalCodeClassification: '',
        postalCodeExtension: '',
        postalCodePlus4: '',
        postalCodeHyphenPlus4: '',
      },
      unit: { unitType: 'Apt', unitNumber: '1' },
      houseNumber: '123',
      addressRequestId: '',
      address: undefined,
      geocode: undefined,
      dataQuality: undefined,
      extendedInformation: undefined,
    };
    const mockResponse = {
      data: {
        addresses: [
          {
            addressRequestId: '1',
            address: addressCleanseAddress,
          },
          {
            addressRequestId: '2',
            address: addressCleanseAddress,
          },
        ],
      },
    };
    const customerMetadataAddress: CustomerMetadata = {
      btCode: '',
      customerName: '',
      address1: '123 Main St',
      address2: '',
      city: 'Anytown',
      state: 'CA',
      zip: '12345',
    };

    (getCustomerAccountClientAuthToken as jest.Mock).mockResolvedValue(mockToken);
    (createAddressLastLine as jest.Mock).mockReturnValue('Anytown, CA 12345');
    (createUnparsedAddressRequest as jest.Mock).mockReturnValue({ unparsedAddress: '123 Main St, Anytown, CA 12345' });
    (axios.request as jest.Mock).mockResolvedValue(mockResponse);

    const result = await AddressHelper.bulkCleanseAddresses({
      key1: customerMetadataAddress,
      key2: customerMetadataAddress,
    });

    expect(result).toEqual({
      key1: AddressHelper.addressDetailsFromResponse(addressCleanseAddress),
      key2: AddressHelper.addressDetailsFromResponse(addressCleanseAddress),
    });
    expect(logger.info).toHaveBeenCalled();
  });

  // test("should handle errors during address processing", async () => {
  //   const mockError = new Error("Network Error");
  //   (getCustomerAccountClientAuthToken as jest.Mock).mockRejectedValue(mockError);

  //   const result = await AddressHelper.processAddress("123 Main St", "", "Anytown", "CA", "12345", "US");

  //   expect(result).toEqual({});
  //   expect(logger.error).toHaveBeenCalledWith("Error in address cleanse method execution: ", mockError.message);
  // });

  // test("should handle 403 error response", async () => {
  //   const mockToken = "mockToken";
  //   const mockError = {
  //     response: {
  //       status: 403,
  //       data: "Forbidden",
  //     },
  //   };

  //   (getCustomerAccountClientAuthToken as jest.Mock).mockResolvedValue(mockToken);
  //   (createAddressLastLine as jest.Mock).mockReturnValue("Anytown, CA 12345");
  //   (createUnparsedAddressRequest as jest.Mock).mockReturnValue({ unparsedAddress: "123 Main St, Anytown, CA 12345" });
  //   (axios.request as jest.Mock).mockRejectedValue(mockError);

  //   const result = await AddressHelper.processAddress("123 Main St", "", "Anytown", "CA", "12345", "US");

  //   expect(result).toEqual({});
  //   expect(logger.error).toHaveBeenCalledWith("Error response data:", "Forbidden");
  //   expect(logger.error).toHaveBeenCalledWith("Error response status:", 403);
  // });

  // test("should handle 500 error response", async () => {
  //   const mockToken = "mockToken";
  //   const mockError = {
  //     response: {
  //       status: 500,
  //       data: "Internal Server Error",
  //     },
  //   };

  //   (getCustomerAccountClientAuthToken as jest.Mock).mockResolvedValue(mockToken);
  //   (createAddressLastLine as jest.Mock).mockReturnValue("Anytown, CA 12345");
  //   (createUnparsedAddressRequest as jest.Mock).mockReturnValue({ unparsedAddress: "123 Main St, Anytown, CA 12345" });
  //   (axios.request as jest.Mock).mockRejectedValue(mockError);

  //   const result = await AddressHelper.processAddress("123 Main St", "", "Anytown", "CA", "12345", "US");

  //   expect(result).toEqual({});
  //   expect(logger.error).toHaveBeenCalledWith("Error response data:", "Internal Server Error");
  //   expect(logger.error).toHaveBeenCalledWith("Error response status:", 500);
  // });
});
