import { removeNonNumericChars, removeSpecialChars } from "../../../lib/service/utility/regex";

describe("removeNonNumericChars", () => {
  test("should remove non-numeric characters from customer name", () => {
    expect(removeNonNumericChars("John123Doe456")).toBe("123456");
  });

  test("should return the same string if it contains only numeric characters", () => {
    expect(removeNonNumericChars("123456")).toBe("123456");
  });

  test("should return an empty string for null input", () => {
    expect(removeNonNumericChars(null)).toBe("");
  });

  test("should return an empty string for undefined input", () => {
    expect(removeNonNumericChars(undefined)).toBe("");
  });
});

describe("removeSpecialChars", () => {
  test("should remove special characters from customer name", () => {
    expect(removeSpecialChars("John!@#Doe")).toBe("JohnDoe");
  });

  test("should trim leading and trailing spaces and replace multiple spaces with a single space in customer name", () => {
    expect(removeSpecialChars("  <PERSON>   <PERSON>e  ")).toBe("John Doe");
  });

  test("should return the same string if it contains no special characters", () => {
    expect(removeSpecialChars("John Doe")).toBe("John Doe");
  });

  test("should return an empty string for null input", () => {
    expect(removeSpecialChars(null)).toBe("");
  });

  test("should return an empty string for undefined input", () => {
    expect(removeSpecialChars(undefined)).toBe("");
  });
});