import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import {EmailHelper}  from '../../../lib/service/utility/emailHelper';
import { logger } from '../../../lib/service/utility/logger';

const axiosMock = new MockAdapter(axios);
jest.mock('../../../lib/service/utility/logger');
const url = '/api/endpoint';

describe('EmailHelper - sendEmail', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    test('should build email request successfully', async () => {

        const customerPolicy1 = {
            priorPolicy: {
              lineOfBusiness: 'AUTOB',
              policyNumber: '987654',
            },
            libertyPolicy: {
              effectiveDate: new Date('2024-12-04'),
              ecliqId:'8c34e5ab-04dc-4eaf-afea-25fdb3d7d001'
            },
            aqe: {
              opportunityId: 1235
            }
          };

        const mockRequests = [
            {
                searchCriteria: {
                    customerMetadata: {
                    btCode: 2,
                    customerName: 'Test Customer',
                    address1: '1234 Test Lane',
                    address2: '',
                    city: 'Gotham',
                    state: 'NY',
                    zip: '*********',
                    },
                },
                customerMetadata: {
                    btCode: 2,
                    customerName: 'Test Customer',
                    address1: '1234 Test Lane',
                    address2: '',
                    city: 'Gotham',
                    state: 'NY',
                    zip: '*********',
                },
                eventSource: 'AQE',
                customerPolicies: [customerPolicy1],
                ecliqAccounts: [],
                }
        ]

        const requestBody = await EmailHelper.buildEmailRequest(mockRequests, "test email");

        expect(requestBody.subject).toEqual('test email')

    });

    test('should send email successfully', async () => {
        axiosMock.reset();
        const mockEmailRequest = {
            from: '<EMAIL>',
            toList: [
                '<EMAIL>'
            ],
            subject: 'Test Email',
            body: {},
        };

        const mockResponse = {
            status: 200,
            statusText: 'OK',
            headers: {},
            config: {},
            data: {}
        };
        axiosMock.onPost(url).reply(200, mockResponse);

        const response = await EmailHelper.sendEmail(url, mockEmailRequest);

        expect(response.status).toEqual(200);

    });

    test('When send email is called it should send response status 400 for a bad request', async () => {
      axiosMock.reset();
      const mockEmailRequest = {
          from: '<EMAIL>',
          toList: [],
          subject: 'Test Email',
          body: {},
      };

      const mockResponse = {
          status: 400,
          statusText: 'Bad Request',
          headers: {},
          config: {},
          data: {}
      };
      axiosMock.onPost(url).reply(400, mockResponse);

      const response = await EmailHelper.sendEmail(url, mockEmailRequest);

      expect(response.status).toEqual(400);

  });

  test('handle error gracefully whenever exception occurrs', async () => {
    axiosMock.reset();
      const mockEmailRequest = {
          from: '<EMAIL>',
          toList: [
              '<EMAIL>'
          ],
          subject: 'Test Email',
          body: {},
      };
      
      axiosMock.onPost(url).networkError();

      const response = await EmailHelper.sendEmail(url, mockEmailRequest);
      expect(logger.error).toHaveBeenCalledWith('Exception occurred while sending email', response.message);
  });
});
