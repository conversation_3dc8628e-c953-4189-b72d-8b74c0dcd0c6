import axios from "axios";
import axiosRetry from "axios-retry";
import { FeatureService } from "../../../lib/service/utility/featureService";
import { getCustomerAccountClientAuthToken } from "../../../lib/service/utility/authenticationTokenHelper";
import { logger } from "../../../lib/service/utility/logger";

jest.mock("axios");
jest.mock("axios-retry");
jest.mock("../../../lib/service/utility/authenticationTokenHelper");
jest.mock("../../../lib/service/utility/logger");

describe("FeatureService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("should return true if the feature is launched", async () => {
    const featureNames = ["addressCleanse"];
    const identifier = "customerAccountService";
    const mockToken = "mockToken";
    const mockResponse = { data: { addressCleanse: true } };

    (getCustomerAccountClientAuthToken as jest.Mock).mockResolvedValue(mockToken);
    (axios as unknown as jest.Mock).mockResolvedValue(mockResponse);

    const result = await FeatureService.checkFeatureLaunch(featureNames, identifier);

    expect(result).toBe(true);
    expect(getCustomerAccountClientAuthToken).toHaveBeenCalledWith(
      expect.any(String),
      expect.any(String),
      expect.any(String),
      expect.any(String)
    );
    expect(axios).toHaveBeenCalledWith(expect.objectContaining({
      headers: { Authorization: `Bearer ${mockToken}`, 'Content-Type': 'application/json' }
    }));
  });

  test("should return false if the feature is not launched", async () => {
    const featureNames = ["addressCleanse"];
    const identifier = "customerAccountService";
    const mockToken = "mockToken";
    const mockResponse = { data: { addressCleanse: false } };

    (getCustomerAccountClientAuthToken as jest.Mock).mockResolvedValue(mockToken);
    (axios as unknown as jest.Mock).mockResolvedValue(mockResponse);

    const result = await FeatureService.checkFeatureLaunch(featureNames, identifier);

    expect(result).toBe(false);
  });

  test("should throw an error if token retrieval fails", async () => {
    const featureNames = ["addressCleanse"];
    const identifier = "customerAccountService";
    const mockError = new Error("Token retrieval failed");

    (getCustomerAccountClientAuthToken as jest.Mock).mockRejectedValue(mockError);

    await expect(FeatureService.checkFeatureLaunch(featureNames, identifier)).rejects.toThrow("Token retrieval failed");
  });

  test("should throw an error if axios request fails", async () => {
    const featureNames = ["addressCleanse"];
    const identifier = "customerAccountService";
    const mockToken = "mockToken";
    const mockError = new Error("Axios request failed");

    (getCustomerAccountClientAuthToken as jest.Mock).mockResolvedValue(mockToken);
    (axios as unknown as jest.Mock).mockRejectedValue(mockError);

    await expect(FeatureService.checkFeatureLaunch(featureNames, identifier)).rejects.toThrow("Axios request failed");
  });
});