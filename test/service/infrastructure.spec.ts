import { App, Stack } from "aws-cdk-lib";
import { ServiceResources } from "../../lib/service/infrastructure";
import { Template, Match } from 'aws-cdk-lib/assertions';

describe('ServiceResources', () => {
  test('ensure ServiceResources construct synthesizes as expected', () => {
    const app = new App();
    const testStack = new Stack(app, 'ServiceTest');
    // The following will disable lambda bundling during test executions
    testStack.node.setContext('aws:cdk:bundling-stacks', []);

    new ServiceResources(testStack, 'TestServiceResources');

    const template = Template.fromStack(testStack);

    // Implement Service construct tests here!

    template.hasResourceProperties('AWS::Lambda::Function', {
      Runtime: 'nodejs22.x',
      Timeout: Match.anyValue(),
      Handler: Match.anyValue(),
    });
  });
});
