stack_name=book-transfer-customer-account-service
functional_area=USRM-BookTransfer
squad_name=quotabotz
test_repo_url=https://github.com/lmigtech/book-transfer-customer-account-service.git
test_repo_branch=main
jmx_file=test/performance/AddCustomerPolicy.jmx
required_lgs=1

thread=1
rampup=1
duration=30
communication=<EMAIL>

#max number of past running in trend report - required
thresholds.maxPastResults=10
thresholds.alwaysShowReport=true