[{"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b1", "priorCarrierPolicyNumber": "TEST101", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 42", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 42", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST101"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b1", "priorCarrierPolicyNumber": "TEST102", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 42", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 42", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST102"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b1", "priorCarrierPolicyNumber": "TEST103", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 42", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 42", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST103"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b1", "priorCarrierPolicyNumber": "TEST104", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 42", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 42", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST104"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b1", "priorCarrierPolicyNumber": "TEST105", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 42", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 42", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST105"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b2", "priorCarrierPolicyNumber": "TEST106", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 43", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 43", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST106"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b2", "priorCarrierPolicyNumber": "TEST107", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 43", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 43", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST107"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b2", "priorCarrierPolicyNumber": "TEST108", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 43", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 43", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST108"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b2", "priorCarrierPolicyNumber": "TEST109", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 43", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 43", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST109"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b3", "priorCarrierPolicyNumber": "TEST110", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 44", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 44", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST110"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b3", "priorCarrierPolicyNumber": "TEST111", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 44", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 44", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST111"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b3", "priorCarrierPolicyNumber": "TEST112", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 44", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 44", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST112"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b3", "priorCarrierPolicyNumber": "TEST113", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 44", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 44", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST113"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b4", "priorCarrierPolicyNumber": "TEST114", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 45", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 45", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST114"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b5", "priorCarrierPolicyNumber": "TEST115", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 46", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 46", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST115"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b6", "priorCarrierPolicyNumber": "TEST116", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 47", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 47", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST116"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b7", "priorCarrierPolicyNumber": "TEST117", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 48", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 48", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST117"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b8", "priorCarrierPolicyNumber": "TEST118", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 49", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 49", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST118"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b8", "priorCarrierPolicyNumber": "TEST119", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 49", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 49", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST119"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b8", "priorCarrierPolicyNumber": "TEST120", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 49", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 49", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST120"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b8", "priorCarrierPolicyNumber": "TEST121", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 49", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 49", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST121"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b8", "priorCarrierPolicyNumber": "TEST122", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 49", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 49", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST122"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1b9", "priorCarrierPolicyNumber": "TEST123", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 50", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 50", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1b9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST123"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ba", "priorCarrierPolicyNumber": "TEST124", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 51", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 51", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ba", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST124"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ba", "priorCarrierPolicyNumber": "TEST125", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 51", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 51", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ba", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST125"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ba", "priorCarrierPolicyNumber": "TEST126", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 51", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 51", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ba", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST126"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ba", "priorCarrierPolicyNumber": "TEST127", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 51", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 51", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ba", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST127"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bb", "priorCarrierPolicyNumber": "TEST128", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 52", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 52", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bb", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST128"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bb", "priorCarrierPolicyNumber": "TEST129", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 52", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 52", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bb", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST129"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bb", "priorCarrierPolicyNumber": "TEST130", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 52", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 52", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bb", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST130"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bb", "priorCarrierPolicyNumber": "TEST131", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 52", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 52", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bb", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST131"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bb", "priorCarrierPolicyNumber": "TEST132", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 52", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 52", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bb", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST132"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bc", "priorCarrierPolicyNumber": "TEST133", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 53", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 53", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST133"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bc", "priorCarrierPolicyNumber": "TEST134", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 53", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 53", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST134"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bc", "priorCarrierPolicyNumber": "TEST135", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 53", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 53", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST135"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bc", "priorCarrierPolicyNumber": "TEST136", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 53", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 53", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST136"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bc", "priorCarrierPolicyNumber": "TEST137", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 53", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 53", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST137"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bd", "priorCarrierPolicyNumber": "TEST138", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 54", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 54", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST138"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bd", "priorCarrierPolicyNumber": "TEST139", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 54", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 54", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST139"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bd", "priorCarrierPolicyNumber": "TEST140", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 54", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 54", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST140"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bd", "priorCarrierPolicyNumber": "TEST141", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 54", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 54", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST141"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1be", "priorCarrierPolicyNumber": "TEST142", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 55", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 55", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1be", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST142"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1be", "priorCarrierPolicyNumber": "TEST143", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 55", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 55", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1be", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST143"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1be", "priorCarrierPolicyNumber": "TEST144", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 55", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 55", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1be", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST144"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1be", "priorCarrierPolicyNumber": "TEST145", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 55", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 55", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1be", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST145"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1be", "priorCarrierPolicyNumber": "TEST146", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 55", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 55", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1be", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST146"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bf", "priorCarrierPolicyNumber": "TEST147", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 56", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 56", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bf", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST147"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bf", "priorCarrierPolicyNumber": "TEST148", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 56", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 56", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bf", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST148"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bf", "priorCarrierPolicyNumber": "TEST149", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 56", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 56", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bf", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST149"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bf", "priorCarrierPolicyNumber": "TEST150", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 56", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 56", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bf", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST150"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1bf", "priorCarrierPolicyNumber": "TEST151", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 56", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 56", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1bf", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST151"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c0", "priorCarrierPolicyNumber": "TEST152", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 57", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 57", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST152"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c0", "priorCarrierPolicyNumber": "TEST153", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 57", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 57", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST153"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c0", "priorCarrierPolicyNumber": "TEST154", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 57", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 57", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST154"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c1", "priorCarrierPolicyNumber": "TEST155", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 58", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 58", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST155"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c2", "priorCarrierPolicyNumber": "TEST156", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 59", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 59", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST156"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c3", "priorCarrierPolicyNumber": "TEST157", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 60", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 60", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST157"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c3", "priorCarrierPolicyNumber": "TEST158", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 60", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 60", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST158"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c3", "priorCarrierPolicyNumber": "TEST159", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 60", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 60", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST159"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c4", "priorCarrierPolicyNumber": "TEST160", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 61", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 61", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST160"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c4", "priorCarrierPolicyNumber": "TEST161", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 61", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 61", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST161"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c4", "priorCarrierPolicyNumber": "TEST162", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 61", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 61", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST162"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c4", "priorCarrierPolicyNumber": "TEST163", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 61", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 61", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST163"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c5", "priorCarrierPolicyNumber": "TEST164", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 62", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 62", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST164"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c6", "priorCarrierPolicyNumber": "TEST165", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 63", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 63", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST165"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c7", "priorCarrierPolicyNumber": "TEST166", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 64", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 64", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST166"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c8", "priorCarrierPolicyNumber": "TEST167", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 65", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 65", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST167"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c9", "priorCarrierPolicyNumber": "TEST168", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 66", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 66", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST168"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c9", "priorCarrierPolicyNumber": "TEST169", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 66", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 66", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST169"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c9", "priorCarrierPolicyNumber": "TEST170", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 66", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 66", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST170"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c9", "priorCarrierPolicyNumber": "TEST171", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 66", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 66", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST171"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1c9", "priorCarrierPolicyNumber": "TEST172", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 66", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 66", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1c9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST172"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ca", "priorCarrierPolicyNumber": "TEST173", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 67", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 67", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ca", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST173"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ca", "priorCarrierPolicyNumber": "TEST174", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 67", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 67", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ca", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST174"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ca", "priorCarrierPolicyNumber": "TEST175", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 67", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 67", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ca", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST175"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ca", "priorCarrierPolicyNumber": "TEST176", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 67", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 67", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ca", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST176"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ca", "priorCarrierPolicyNumber": "TEST177", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 67", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 67", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ca", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST177"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1cb", "priorCarrierPolicyNumber": "TEST178", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 68", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 68", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1cb", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST178"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1cb", "priorCarrierPolicyNumber": "TEST179", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 68", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 68", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1cb", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST179"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1cb", "priorCarrierPolicyNumber": "TEST180", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 68", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 68", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1cb", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST180"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1cb", "priorCarrierPolicyNumber": "TEST181", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 68", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 68", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1cb", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST181"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1cc", "priorCarrierPolicyNumber": "TEST182", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 69", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 69", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1cc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST182"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1cc", "priorCarrierPolicyNumber": "TEST183", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 69", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 69", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1cc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST183"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1cc", "priorCarrierPolicyNumber": "TEST184", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 69", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 69", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1cc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST184"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1cc", "priorCarrierPolicyNumber": "TEST185", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 69", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 69", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1cc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST185"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1cd", "priorCarrierPolicyNumber": "TEST186", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 70", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 70", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1cd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST186"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1cd", "priorCarrierPolicyNumber": "TEST187", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 70", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 70", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1cd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST187"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1cd", "priorCarrierPolicyNumber": "TEST188", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 70", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 70", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1cd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST188"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1cd", "priorCarrierPolicyNumber": "TEST189", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 70", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 70", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1cd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST189"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ce", "priorCarrierPolicyNumber": "TEST190", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 71", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 71", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ce", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST190"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ce", "priorCarrierPolicyNumber": "TEST191", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 71", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 71", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ce", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST191"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ce", "priorCarrierPolicyNumber": "TEST192", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 71", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 71", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ce", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST192"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ce", "priorCarrierPolicyNumber": "TEST193", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 71", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 71", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ce", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST193"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1cf", "priorCarrierPolicyNumber": "TEST194", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 72", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 72", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1cf", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST194"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d0", "priorCarrierPolicyNumber": "TEST195", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 73", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 73", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST195"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d1", "priorCarrierPolicyNumber": "TEST196", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 74", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 74", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST196"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d1", "priorCarrierPolicyNumber": "TEST197", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 74", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 74", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST197"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d1", "priorCarrierPolicyNumber": "TEST198", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 74", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 74", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST198"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d2", "priorCarrierPolicyNumber": "TEST199", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 75", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 75", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST199"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d2", "priorCarrierPolicyNumber": "TEST200", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 75", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 75", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST200"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d2", "priorCarrierPolicyNumber": "TEST201", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 75", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 75", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST201"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d3", "priorCarrierPolicyNumber": "TEST202", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 76", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 76", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST202"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d4", "priorCarrierPolicyNumber": "TEST203", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 77", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 77", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST203"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d4", "priorCarrierPolicyNumber": "TEST204", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 77", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 77", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST204"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d4", "priorCarrierPolicyNumber": "TEST205", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 77", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 77", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST205"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d4", "priorCarrierPolicyNumber": "TEST206", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 77", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 77", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST206"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d4", "priorCarrierPolicyNumber": "TEST207", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 77", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 77", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST207"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d5", "priorCarrierPolicyNumber": "TEST208", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 78", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 78", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST208"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d6", "priorCarrierPolicyNumber": "TEST209", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 79", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 79", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST209"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d6", "priorCarrierPolicyNumber": "TEST210", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 79", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 79", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST210"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d6", "priorCarrierPolicyNumber": "TEST211", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 79", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 79", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST211"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d6", "priorCarrierPolicyNumber": "TEST212", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 79", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 79", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST212"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d6", "priorCarrierPolicyNumber": "TEST213", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 79", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 79", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST213"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d7", "priorCarrierPolicyNumber": "TEST214", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 80", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 80", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST214"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d7", "priorCarrierPolicyNumber": "TEST215", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 80", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 80", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST215"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d7", "priorCarrierPolicyNumber": "TEST216", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 80", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 80", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST216"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d7", "priorCarrierPolicyNumber": "TEST217", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 80", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 80", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST217"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d7", "priorCarrierPolicyNumber": "TEST218", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 80", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 80", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST218"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d8", "priorCarrierPolicyNumber": "TEST219", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 81", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 81", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST219"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d9", "priorCarrierPolicyNumber": "TEST220", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 82", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 82", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST220"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d9", "priorCarrierPolicyNumber": "TEST221", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 82", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 82", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST221"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d9", "priorCarrierPolicyNumber": "TEST222", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 82", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 82", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST222"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1d9", "priorCarrierPolicyNumber": "TEST223", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 82", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 82", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1d9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST223"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1da", "priorCarrierPolicyNumber": "TEST224", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 83", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 83", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1da", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST224"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1da", "priorCarrierPolicyNumber": "TEST225", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 83", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 83", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1da", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST225"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1da", "priorCarrierPolicyNumber": "TEST226", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 83", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 83", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1da", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST226"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1da", "priorCarrierPolicyNumber": "TEST227", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 83", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 83", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1da", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST227"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1db", "priorCarrierPolicyNumber": "TEST228", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 84", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 84", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1db", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST228"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1dc", "priorCarrierPolicyNumber": "TEST229", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 85", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 85", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1dc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST229"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1dc", "priorCarrierPolicyNumber": "TEST230", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 85", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 85", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1dc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST230"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1dc", "priorCarrierPolicyNumber": "TEST231", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 85", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 85", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1dc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST231"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1dc", "priorCarrierPolicyNumber": "TEST232", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 85", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 85", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1dc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST232"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1dd", "priorCarrierPolicyNumber": "TEST233", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 86", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 86", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1dd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST233"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1dd", "priorCarrierPolicyNumber": "TEST234", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 86", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 86", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1dd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST234"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1dd", "priorCarrierPolicyNumber": "TEST235", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 86", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 86", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1dd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST235"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1de", "priorCarrierPolicyNumber": "TEST236", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 87", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 87", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1de", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST236"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1de", "priorCarrierPolicyNumber": "TEST237", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 87", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 87", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1de", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST237"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1de", "priorCarrierPolicyNumber": "TEST238", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 87", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 87", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1de", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST238"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1df", "priorCarrierPolicyNumber": "TEST239", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 88", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 88", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1df", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST239"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1df", "priorCarrierPolicyNumber": "TEST240", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 88", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 88", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1df", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST240"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1df", "priorCarrierPolicyNumber": "TEST241", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 88", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 88", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1df", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST241"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1df", "priorCarrierPolicyNumber": "TEST242", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 88", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 88", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1df", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST242"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e0", "priorCarrierPolicyNumber": "TEST243", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 89", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 89", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST243"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e0", "priorCarrierPolicyNumber": "TEST244", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 89", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 89", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST244"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e0", "priorCarrierPolicyNumber": "TEST245", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 89", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 89", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST245"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e1", "priorCarrierPolicyNumber": "TEST246", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 90", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 90", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST246"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e1", "priorCarrierPolicyNumber": "TEST247", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 90", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 90", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST247"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e1", "priorCarrierPolicyNumber": "TEST248", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 90", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 90", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST248"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e1", "priorCarrierPolicyNumber": "TEST249", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 90", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 90", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST249"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e1", "priorCarrierPolicyNumber": "TEST250", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 90", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 90", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST250"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e2", "priorCarrierPolicyNumber": "TEST251", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 91", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 91", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST251"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e3", "priorCarrierPolicyNumber": "TEST252", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 92", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 92", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST252"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e4", "priorCarrierPolicyNumber": "TEST253", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 93", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 93", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST253"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e4", "priorCarrierPolicyNumber": "TEST254", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 93", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 93", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST254"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e4", "priorCarrierPolicyNumber": "TEST255", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 93", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 93", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST255"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e4", "priorCarrierPolicyNumber": "TEST256", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 93", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 93", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST256"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e5", "priorCarrierPolicyNumber": "TEST257", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 94", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 94", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST257"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e6", "priorCarrierPolicyNumber": "TEST258", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 95", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 95", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST258"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e7", "priorCarrierPolicyNumber": "TEST259", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 96", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 96", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST259"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e8", "priorCarrierPolicyNumber": "TEST260", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 97", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 97", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST260"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e8", "priorCarrierPolicyNumber": "TEST261", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 97", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 97", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST261"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e8", "priorCarrierPolicyNumber": "TEST262", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 97", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 97", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST262"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e8", "priorCarrierPolicyNumber": "TEST263", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 97", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 97", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST263"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1e9", "priorCarrierPolicyNumber": "TEST264", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 98", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 98", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1e9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST264"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ea", "priorCarrierPolicyNumber": "TEST265", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 99", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 99", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ea", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST265"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1eb", "priorCarrierPolicyNumber": "TEST266", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 100", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 100", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1eb", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST266"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ec", "priorCarrierPolicyNumber": "TEST267", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 101", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 101", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ec", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST267"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ed", "priorCarrierPolicyNumber": "TEST268", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 102", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 102", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ed", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST268"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ee", "priorCarrierPolicyNumber": "TEST269", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 103", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 103", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ee", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST269"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ee", "priorCarrierPolicyNumber": "TEST270", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 103", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 103", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ee", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST270"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ee", "priorCarrierPolicyNumber": "TEST271", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 103", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 103", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ee", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST271"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ef", "priorCarrierPolicyNumber": "TEST272", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 104", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 104", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ef", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST272"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ef", "priorCarrierPolicyNumber": "TEST273", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 104", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 104", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ef", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST273"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1ef", "priorCarrierPolicyNumber": "TEST274", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 104", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 104", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1ef", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST274"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f0", "priorCarrierPolicyNumber": "TEST275", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 105", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 105", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST275"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f1", "priorCarrierPolicyNumber": "TEST276", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 106", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 106", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST276"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f1", "priorCarrierPolicyNumber": "TEST277", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 106", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 106", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST277"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f1", "priorCarrierPolicyNumber": "TEST278", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 106", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 106", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST278"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f2", "priorCarrierPolicyNumber": "TEST279", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 107", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 107", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST279"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f2", "priorCarrierPolicyNumber": "TEST280", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 107", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 107", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST280"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f2", "priorCarrierPolicyNumber": "TEST281", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 107", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 107", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST281"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f2", "priorCarrierPolicyNumber": "TEST282", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 107", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 107", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST282"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f2", "priorCarrierPolicyNumber": "TEST283", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 107", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 107", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST283"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f3", "priorCarrierPolicyNumber": "TEST284", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 108", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 108", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST284"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f4", "priorCarrierPolicyNumber": "TEST285", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 109", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 109", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST285"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f4", "priorCarrierPolicyNumber": "TEST286", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 109", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 109", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST286"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f4", "priorCarrierPolicyNumber": "TEST287", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 109", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 109", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST287"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f4", "priorCarrierPolicyNumber": "TEST288", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 109", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 109", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST288"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f5", "priorCarrierPolicyNumber": "TEST289", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 110", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 110", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST289"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f5", "priorCarrierPolicyNumber": "TEST290", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 110", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 110", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST290"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f5", "priorCarrierPolicyNumber": "TEST291", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 110", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 110", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST291"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f5", "priorCarrierPolicyNumber": "TEST292", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 110", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 110", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST292"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f6", "priorCarrierPolicyNumber": "TEST293", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 111", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 111", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST293"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f7", "priorCarrierPolicyNumber": "TEST294", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 112", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 112", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST294"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f8", "priorCarrierPolicyNumber": "TEST295", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 113", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 113", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST295"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f8", "priorCarrierPolicyNumber": "TEST296", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 113", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 113", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST296"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f8", "priorCarrierPolicyNumber": "TEST297", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 113", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 113", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST297"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f8", "priorCarrierPolicyNumber": "TEST298", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 113", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 113", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST298"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f8", "priorCarrierPolicyNumber": "TEST299", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 113", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 113", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST299"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d143925a58dc270ac1f9", "priorCarrierPolicyNumber": "TEST300", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 114", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 114", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d143925a58dc270ac1f9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST300"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}]