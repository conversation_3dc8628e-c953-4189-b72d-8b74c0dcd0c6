[{"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889444d", "priorCarrierPolicyNumber": "TEST301", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 115", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 115", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889444d", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST301"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889444d", "priorCarrierPolicyNumber": "TEST302", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 115", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 115", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889444d", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST302"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889444d", "priorCarrierPolicyNumber": "TEST303", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 115", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 115", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889444d", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST303"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889444d", "priorCarrierPolicyNumber": "TEST304", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 115", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 115", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889444d", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST304"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889444d", "priorCarrierPolicyNumber": "TEST305", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 115", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 115", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889444d", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST305"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889444e", "priorCarrierPolicyNumber": "TEST306", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 116", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 116", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889444e", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST306"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889444f", "priorCarrierPolicyNumber": "TEST307", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 117", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 117", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889444f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST307"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889444f", "priorCarrierPolicyNumber": "TEST308", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 117", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 117", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889444f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST308"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889444f", "priorCarrierPolicyNumber": "TEST309", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 117", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 117", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889444f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST309"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894450", "priorCarrierPolicyNumber": "TEST310", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 118", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 118", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894450", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST310"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894451", "priorCarrierPolicyNumber": "TEST311", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 119", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 119", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894451", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST311"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894452", "priorCarrierPolicyNumber": "TEST312", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 120", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 120", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894452", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST312"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894453", "priorCarrierPolicyNumber": "TEST313", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 121", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 121", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894453", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST313"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894454", "priorCarrierPolicyNumber": "TEST314", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 122", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 122", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894454", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST314"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894454", "priorCarrierPolicyNumber": "TEST315", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 122", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 122", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894454", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST315"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894454", "priorCarrierPolicyNumber": "TEST316", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 122", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 122", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894454", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST316"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894454", "priorCarrierPolicyNumber": "TEST317", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 122", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 122", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894454", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST317"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894455", "priorCarrierPolicyNumber": "TEST318", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 123", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 123", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894455", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST318"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894456", "priorCarrierPolicyNumber": "TEST319", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 124", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 124", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894456", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST319"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894456", "priorCarrierPolicyNumber": "TEST320", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 124", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 124", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894456", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST320"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894456", "priorCarrierPolicyNumber": "TEST321", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 124", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 124", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894456", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST321"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894456", "priorCarrierPolicyNumber": "TEST322", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 124", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 124", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894456", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST322"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894456", "priorCarrierPolicyNumber": "TEST323", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 124", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 124", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894456", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST323"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894457", "priorCarrierPolicyNumber": "TEST324", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 125", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 125", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894457", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST324"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894457", "priorCarrierPolicyNumber": "TEST325", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 125", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 125", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894457", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST325"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894457", "priorCarrierPolicyNumber": "TEST326", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 125", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 125", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894457", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST326"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894457", "priorCarrierPolicyNumber": "TEST327", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 125", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 125", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894457", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST327"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894458", "priorCarrierPolicyNumber": "TEST328", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 126", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 126", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894458", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST328"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894459", "priorCarrierPolicyNumber": "TEST329", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 127", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 127", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894459", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST329"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889445a", "priorCarrierPolicyNumber": "TEST330", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 128", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 128", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889445a", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST330"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889445b", "priorCarrierPolicyNumber": "TEST331", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 129", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 129", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889445b", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST331"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889445b", "priorCarrierPolicyNumber": "TEST332", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 129", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 129", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889445b", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST332"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889445b", "priorCarrierPolicyNumber": "TEST333", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 129", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 129", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889445b", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST333"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889445b", "priorCarrierPolicyNumber": "TEST334", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 129", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 129", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889445b", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST334"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889445b", "priorCarrierPolicyNumber": "TEST335", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 129", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 129", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889445b", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST335"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889445c", "priorCarrierPolicyNumber": "TEST336", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 130", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 130", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889445c", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST336"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889445d", "priorCarrierPolicyNumber": "TEST337", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 131", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 131", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889445d", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST337"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889445e", "priorCarrierPolicyNumber": "TEST338", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 132", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 132", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889445e", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST338"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889445e", "priorCarrierPolicyNumber": "TEST339", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 132", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 132", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889445e", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST339"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889445e", "priorCarrierPolicyNumber": "TEST340", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 132", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 132", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889445e", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST340"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889445e", "priorCarrierPolicyNumber": "TEST341", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 132", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 132", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889445e", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST341"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889445f", "priorCarrierPolicyNumber": "TEST342", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 133", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 133", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889445f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST342"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894460", "priorCarrierPolicyNumber": "TEST343", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 134", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 134", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894460", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST343"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894460", "priorCarrierPolicyNumber": "TEST344", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 134", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 134", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894460", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST344"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894460", "priorCarrierPolicyNumber": "TEST345", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 134", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 134", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894460", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST345"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894460", "priorCarrierPolicyNumber": "TEST346", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 134", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 134", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894460", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST346"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894460", "priorCarrierPolicyNumber": "TEST347", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 134", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 134", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894460", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST347"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894461", "priorCarrierPolicyNumber": "TEST348", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 135", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 135", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894461", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST348"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894462", "priorCarrierPolicyNumber": "TEST349", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 136", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 136", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894462", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST349"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894463", "priorCarrierPolicyNumber": "TEST350", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 137", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 137", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894463", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST350"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894464", "priorCarrierPolicyNumber": "TEST351", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 138", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 138", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894464", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST351"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894464", "priorCarrierPolicyNumber": "TEST352", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 138", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 138", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894464", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST352"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894464", "priorCarrierPolicyNumber": "TEST353", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 138", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 138", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894464", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST353"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894465", "priorCarrierPolicyNumber": "TEST354", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 139", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 139", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894465", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST354"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894466", "priorCarrierPolicyNumber": "TEST355", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 140", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 140", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894466", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST355"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894466", "priorCarrierPolicyNumber": "TEST356", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 140", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 140", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894466", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST356"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894466", "priorCarrierPolicyNumber": "TEST357", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 140", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 140", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894466", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST357"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894466", "priorCarrierPolicyNumber": "TEST358", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 140", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 140", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894466", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST358"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894466", "priorCarrierPolicyNumber": "TEST359", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 140", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 140", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894466", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST359"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894467", "priorCarrierPolicyNumber": "TEST360", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 141", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 141", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894467", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST360"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894467", "priorCarrierPolicyNumber": "TEST361", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 141", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 141", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894467", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST361"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894467", "priorCarrierPolicyNumber": "TEST362", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 141", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 141", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894467", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST362"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894467", "priorCarrierPolicyNumber": "TEST363", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 141", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 141", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894467", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST363"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894467", "priorCarrierPolicyNumber": "TEST364", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 141", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 141", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894467", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST364"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894468", "priorCarrierPolicyNumber": "TEST365", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 142", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 142", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894468", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST365"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894468", "priorCarrierPolicyNumber": "TEST366", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 142", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 142", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894468", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST366"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894468", "priorCarrierPolicyNumber": "TEST367", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 142", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 142", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894468", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST367"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894469", "priorCarrierPolicyNumber": "TEST368", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 143", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 143", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894469", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST368"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894469", "priorCarrierPolicyNumber": "TEST369", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 143", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 143", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894469", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST369"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894469", "priorCarrierPolicyNumber": "TEST370", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 143", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 143", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894469", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST370"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894469", "priorCarrierPolicyNumber": "TEST371", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 143", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 143", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894469", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST371"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894469", "priorCarrierPolicyNumber": "TEST372", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 143", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 143", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894469", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST372"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889446a", "priorCarrierPolicyNumber": "TEST373", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 144", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 144", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889446a", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST373"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889446b", "priorCarrierPolicyNumber": "TEST374", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 145", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 145", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889446b", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST374"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889446c", "priorCarrierPolicyNumber": "TEST375", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 146", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 146", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889446c", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST375"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889446d", "priorCarrierPolicyNumber": "TEST376", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 147", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 147", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889446d", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST376"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889446e", "priorCarrierPolicyNumber": "TEST377", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 148", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 148", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889446e", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST377"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889446f", "priorCarrierPolicyNumber": "TEST378", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 149", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 149", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889446f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST378"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894470", "priorCarrierPolicyNumber": "TEST379", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 150", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 150", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894470", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST379"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894471", "priorCarrierPolicyNumber": "TEST380", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 151", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 151", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894471", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST380"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894471", "priorCarrierPolicyNumber": "TEST381", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 151", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 151", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894471", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST381"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894471", "priorCarrierPolicyNumber": "TEST382", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 151", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 151", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894471", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST382"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894471", "priorCarrierPolicyNumber": "TEST383", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 151", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 151", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894471", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST383"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894472", "priorCarrierPolicyNumber": "TEST384", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 152", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 152", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894472", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST384"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894472", "priorCarrierPolicyNumber": "TEST385", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 152", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 152", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894472", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST385"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894472", "priorCarrierPolicyNumber": "TEST386", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 152", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 152", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894472", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST386"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894472", "priorCarrierPolicyNumber": "TEST387", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 152", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 152", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894472", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST387"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894473", "priorCarrierPolicyNumber": "TEST388", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 153", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 153", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894473", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST388"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894473", "priorCarrierPolicyNumber": "TEST389", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 153", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 153", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894473", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST389"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894473", "priorCarrierPolicyNumber": "TEST390", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 153", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 153", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894473", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST390"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894474", "priorCarrierPolicyNumber": "TEST391", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 154", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 154", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894474", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST391"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894475", "priorCarrierPolicyNumber": "TEST392", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 155", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 155", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894475", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST392"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894475", "priorCarrierPolicyNumber": "TEST393", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 155", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 155", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894475", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST393"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894475", "priorCarrierPolicyNumber": "TEST394", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 155", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 155", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894475", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST394"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894475", "priorCarrierPolicyNumber": "TEST395", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 155", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 155", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894475", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST395"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894475", "priorCarrierPolicyNumber": "TEST396", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 155", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 155", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894475", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST396"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894476", "priorCarrierPolicyNumber": "TEST397", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 156", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 156", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894476", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST397"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894477", "priorCarrierPolicyNumber": "TEST398", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 157", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 157", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894477", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST398"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894478", "priorCarrierPolicyNumber": "TEST399", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 158", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 158", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894478", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST399"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894479", "priorCarrierPolicyNumber": "TEST400", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 159", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 159", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894479", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST400"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894479", "priorCarrierPolicyNumber": "TEST401", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 159", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 159", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894479", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST401"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894479", "priorCarrierPolicyNumber": "TEST402", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 159", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 159", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894479", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST402"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447a", "priorCarrierPolicyNumber": "TEST403", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 160", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 160", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447a", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST403"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447a", "priorCarrierPolicyNumber": "TEST404", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 160", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 160", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447a", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST404"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447a", "priorCarrierPolicyNumber": "TEST405", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 160", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 160", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447a", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST405"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447a", "priorCarrierPolicyNumber": "TEST406", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 160", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 160", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447a", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST406"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447b", "priorCarrierPolicyNumber": "TEST407", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 161", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 161", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447b", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST407"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447c", "priorCarrierPolicyNumber": "TEST408", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 162", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 162", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447c", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST408"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447d", "priorCarrierPolicyNumber": "TEST409", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 163", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 163", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447d", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST409"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447e", "priorCarrierPolicyNumber": "TEST410", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 164", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 164", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447e", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST410"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447e", "priorCarrierPolicyNumber": "TEST411", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 164", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 164", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447e", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST411"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447e", "priorCarrierPolicyNumber": "TEST412", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 164", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 164", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447e", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST412"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447e", "priorCarrierPolicyNumber": "TEST413", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 164", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 164", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447e", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST413"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447e", "priorCarrierPolicyNumber": "TEST414", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 164", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 164", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447e", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST414"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447f", "priorCarrierPolicyNumber": "TEST415", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 165", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 165", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST415"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447f", "priorCarrierPolicyNumber": "TEST416", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 165", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 165", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST416"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447f", "priorCarrierPolicyNumber": "TEST417", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 165", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 165", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST417"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447f", "priorCarrierPolicyNumber": "TEST418", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 165", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 165", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST418"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb889447f", "priorCarrierPolicyNumber": "TEST419", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 165", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 165", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb889447f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST419"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894480", "priorCarrierPolicyNumber": "TEST420", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 166", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 166", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894480", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST420"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894481", "priorCarrierPolicyNumber": "TEST421", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 167", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 167", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894481", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST421"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894482", "priorCarrierPolicyNumber": "TEST422", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 168", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 168", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894482", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST422"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894482", "priorCarrierPolicyNumber": "TEST423", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 168", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 168", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894482", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST423"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894482", "priorCarrierPolicyNumber": "TEST424", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 168", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 168", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894482", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST424"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894482", "priorCarrierPolicyNumber": "TEST425", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 168", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 168", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894482", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST425"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894482", "priorCarrierPolicyNumber": "TEST426", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 168", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 168", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894482", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST426"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894483", "priorCarrierPolicyNumber": "TEST427", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 169", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 169", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894483", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST427"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894484", "priorCarrierPolicyNumber": "TEST428", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 170", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 170", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894484", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST428"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894484", "priorCarrierPolicyNumber": "TEST429", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 170", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 170", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894484", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST429"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894484", "priorCarrierPolicyNumber": "TEST430", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 170", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 170", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894484", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST430"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894484", "priorCarrierPolicyNumber": "TEST431", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 170", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 170", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894484", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST431"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894484", "priorCarrierPolicyNumber": "TEST432", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 170", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 170", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894484", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST432"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894485", "priorCarrierPolicyNumber": "TEST433", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 171", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 171", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894485", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST433"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894486", "priorCarrierPolicyNumber": "TEST434", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 172", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 172", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894486", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST434"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894486", "priorCarrierPolicyNumber": "TEST435", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 172", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 172", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894486", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST435"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894486", "priorCarrierPolicyNumber": "TEST436", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 172", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 172", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894486", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST436"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894486", "priorCarrierPolicyNumber": "TEST437", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 172", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 172", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894486", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST437"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894487", "priorCarrierPolicyNumber": "TEST438", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 173", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 173", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894487", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST438"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894487", "priorCarrierPolicyNumber": "TEST439", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 173", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 173", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894487", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST439"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894487", "priorCarrierPolicyNumber": "TEST440", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 173", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 173", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894487", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST440"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894487", "priorCarrierPolicyNumber": "TEST441", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 173", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 173", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894487", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST441"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894488", "priorCarrierPolicyNumber": "TEST442", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 174", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 174", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894488", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST442"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894489", "priorCarrierPolicyNumber": "TEST443", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 175", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 175", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894489", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST443"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894489", "priorCarrierPolicyNumber": "TEST444", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 175", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 175", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894489", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST444"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d179f44ccbefb8894489", "priorCarrierPolicyNumber": "TEST445", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 175", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 175", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d179f44ccbefb8894489", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST445"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448a", "priorCarrierPolicyNumber": "TEST446", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 176", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 176", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448a", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST446"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448a", "priorCarrierPolicyNumber": "TEST447", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 176", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 176", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448a", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST447"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448a", "priorCarrierPolicyNumber": "TEST448", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 176", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 176", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448a", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST448"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448a", "priorCarrierPolicyNumber": "TEST449", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 176", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 176", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448a", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST449"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448a", "priorCarrierPolicyNumber": "TEST450", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 176", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 176", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448a", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST450"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448b", "priorCarrierPolicyNumber": "TEST451", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 177", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 177", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448b", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST451"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448b", "priorCarrierPolicyNumber": "TEST452", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 177", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 177", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448b", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST452"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448b", "priorCarrierPolicyNumber": "TEST453", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 177", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 177", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448b", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST453"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448b", "priorCarrierPolicyNumber": "TEST454", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 177", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 177", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448b", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST454"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448c", "priorCarrierPolicyNumber": "TEST455", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 178", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 178", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448c", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST455"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448d", "priorCarrierPolicyNumber": "TEST456", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 179", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 179", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448d", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST456"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448d", "priorCarrierPolicyNumber": "TEST457", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 179", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 179", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448d", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST457"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448d", "priorCarrierPolicyNumber": "TEST458", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 179", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 179", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448d", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST458"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448e", "priorCarrierPolicyNumber": "TEST459", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 180", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 180", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448e", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST459"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448f", "priorCarrierPolicyNumber": "TEST460", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 181", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 181", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST460"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448f", "priorCarrierPolicyNumber": "TEST461", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 181", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 181", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST461"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448f", "priorCarrierPolicyNumber": "TEST462", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 181", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 181", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST462"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448f", "priorCarrierPolicyNumber": "TEST463", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 181", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 181", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST463"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889448f", "priorCarrierPolicyNumber": "TEST464", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 181", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 181", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889448f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST464"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894490", "priorCarrierPolicyNumber": "TEST465", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 182", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 182", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894490", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST465"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894490", "priorCarrierPolicyNumber": "TEST466", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 182", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 182", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894490", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST466"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894490", "priorCarrierPolicyNumber": "TEST467", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 182", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 182", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894490", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST467"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894491", "priorCarrierPolicyNumber": "TEST468", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 183", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 183", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894491", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST468"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894491", "priorCarrierPolicyNumber": "TEST469", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 183", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 183", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894491", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST469"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894491", "priorCarrierPolicyNumber": "TEST470", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 183", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 183", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894491", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST470"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894491", "priorCarrierPolicyNumber": "TEST471", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 183", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 183", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894491", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST471"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894492", "priorCarrierPolicyNumber": "TEST472", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 184", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 184", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894492", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST472"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894492", "priorCarrierPolicyNumber": "TEST473", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 184", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 184", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894492", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST473"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894492", "priorCarrierPolicyNumber": "TEST474", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 184", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 184", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894492", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST474"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894493", "priorCarrierPolicyNumber": "TEST475", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 185", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 185", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894493", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST475"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894494", "priorCarrierPolicyNumber": "TEST476", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 186", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 186", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894494", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST476"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894495", "priorCarrierPolicyNumber": "TEST477", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 187", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 187", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894495", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST477"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894495", "priorCarrierPolicyNumber": "TEST478", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 187", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 187", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894495", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST478"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894495", "priorCarrierPolicyNumber": "TEST479", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 187", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 187", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894495", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST479"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894495", "priorCarrierPolicyNumber": "TEST480", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 187", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 187", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894495", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST480"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894496", "priorCarrierPolicyNumber": "TEST481", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 188", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 188", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894496", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST481"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894496", "priorCarrierPolicyNumber": "TEST482", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 188", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 188", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894496", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST482"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894496", "priorCarrierPolicyNumber": "TEST483", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 188", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 188", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894496", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST483"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894496", "priorCarrierPolicyNumber": "TEST484", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 188", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 188", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894496", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST484"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894496", "priorCarrierPolicyNumber": "TEST485", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 188", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 188", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894496", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST485"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894497", "priorCarrierPolicyNumber": "TEST486", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 189", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 189", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894497", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST486"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894497", "priorCarrierPolicyNumber": "TEST487", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 189", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 189", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894497", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST487"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894497", "priorCarrierPolicyNumber": "TEST488", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 189", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 189", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894497", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST488"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894497", "priorCarrierPolicyNumber": "TEST489", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 189", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 189", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894497", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST489"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894497", "priorCarrierPolicyNumber": "TEST490", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 189", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 189", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894497", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST490"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894498", "priorCarrierPolicyNumber": "TEST491", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 190", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 190", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894498", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST491"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb8894499", "priorCarrierPolicyNumber": "TEST492", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 191", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 191", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb8894499", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST492"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449a", "priorCarrierPolicyNumber": "TEST493", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 192", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 192", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449a", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST493"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449a", "priorCarrierPolicyNumber": "TEST494", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 192", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 192", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449a", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST494"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449a", "priorCarrierPolicyNumber": "TEST495", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 192", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 192", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449a", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST495"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449a", "priorCarrierPolicyNumber": "TEST496", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 192", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 192", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449a", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST496"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449a", "priorCarrierPolicyNumber": "TEST497", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 192", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 192", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449a", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST497"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449b", "priorCarrierPolicyNumber": "TEST498", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 193", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 193", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449b", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST498"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449b", "priorCarrierPolicyNumber": "TEST499", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 193", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 193", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449b", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST499"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449b", "priorCarrierPolicyNumber": "TEST500", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 193", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 193", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449b", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST500"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449b", "priorCarrierPolicyNumber": "TEST501", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 193", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 193", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449b", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST501"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449c", "priorCarrierPolicyNumber": "TEST502", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 194", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 194", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449c", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST502"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449c", "priorCarrierPolicyNumber": "TEST503", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 194", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 194", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449c", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST503"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449c", "priorCarrierPolicyNumber": "TEST504", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 194", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 194", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449c", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST504"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449c", "priorCarrierPolicyNumber": "TEST505", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 194", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 194", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449c", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST505"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449d", "priorCarrierPolicyNumber": "TEST506", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 195", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 195", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449d", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST506"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449e", "priorCarrierPolicyNumber": "TEST507", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 196", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 196", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449e", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST507"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449f", "priorCarrierPolicyNumber": "TEST508", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 197", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 197", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST508"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449f", "priorCarrierPolicyNumber": "TEST509", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 197", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 197", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST509"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449f", "priorCarrierPolicyNumber": "TEST510", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 197", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 197", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST510"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb889449f", "priorCarrierPolicyNumber": "TEST511", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 197", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 197", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb889449f", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST511"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a0", "priorCarrierPolicyNumber": "TEST512", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 198", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 198", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST512"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a1", "priorCarrierPolicyNumber": "TEST513", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 199", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 199", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST513"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a1", "priorCarrierPolicyNumber": "TEST514", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 199", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 199", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST514"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a1", "priorCarrierPolicyNumber": "TEST515", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 199", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 199", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST515"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a1", "priorCarrierPolicyNumber": "TEST516", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 199", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 199", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST516"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a1", "priorCarrierPolicyNumber": "TEST517", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 199", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 199", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST517"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a2", "priorCarrierPolicyNumber": "TEST518", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 200", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 200", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST518"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a3", "priorCarrierPolicyNumber": "TEST519", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 201", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 201", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST519"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a4", "priorCarrierPolicyNumber": "TEST520", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 202", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 202", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST520"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a5", "priorCarrierPolicyNumber": "TEST521", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 203", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 203", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST521"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a5", "priorCarrierPolicyNumber": "TEST522", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 203", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 203", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST522"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a5", "priorCarrierPolicyNumber": "TEST523", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 203", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 203", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST523"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a5", "priorCarrierPolicyNumber": "TEST524", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 203", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 203", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST524"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a6", "priorCarrierPolicyNumber": "TEST525", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 204", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 204", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST525"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a6", "priorCarrierPolicyNumber": "TEST526", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 204", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 204", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST526"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a6", "priorCarrierPolicyNumber": "TEST527", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 204", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 204", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST527"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a7", "priorCarrierPolicyNumber": "TEST528", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 205", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 205", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST528"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a7", "priorCarrierPolicyNumber": "TEST529", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 205", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 205", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST529"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a7", "priorCarrierPolicyNumber": "TEST530", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 205", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 205", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST530"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a8", "priorCarrierPolicyNumber": "TEST531", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 206", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 206", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST531"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944a9", "priorCarrierPolicyNumber": "TEST532", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 207", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 207", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944a9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST532"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944aa", "priorCarrierPolicyNumber": "TEST533", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 208", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 208", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944aa", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST533"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ab", "priorCarrierPolicyNumber": "TEST534", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 209", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 209", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ab", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST534"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ab", "priorCarrierPolicyNumber": "TEST535", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 209", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 209", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ab", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST535"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ab", "priorCarrierPolicyNumber": "TEST536", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 209", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 209", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ab", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST536"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ac", "priorCarrierPolicyNumber": "TEST537", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 210", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 210", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ac", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST537"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ac", "priorCarrierPolicyNumber": "TEST538", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 210", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 210", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ac", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST538"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ac", "priorCarrierPolicyNumber": "TEST539", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 210", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 210", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ac", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST539"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ac", "priorCarrierPolicyNumber": "TEST540", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 210", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 210", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ac", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST540"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ac", "priorCarrierPolicyNumber": "TEST541", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 210", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 210", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ac", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST541"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ad", "priorCarrierPolicyNumber": "TEST542", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 211", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 211", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ad", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST542"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ad", "priorCarrierPolicyNumber": "TEST543", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 211", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 211", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ad", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST543"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ad", "priorCarrierPolicyNumber": "TEST544", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 211", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 211", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ad", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST544"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ae", "priorCarrierPolicyNumber": "TEST545", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 212", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 212", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ae", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST545"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ae", "priorCarrierPolicyNumber": "TEST546", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 212", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 212", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ae", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST546"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ae", "priorCarrierPolicyNumber": "TEST547", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 212", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 212", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ae", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST547"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ae", "priorCarrierPolicyNumber": "TEST548", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 212", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 212", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ae", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST548"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ae", "priorCarrierPolicyNumber": "TEST549", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 212", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 212", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ae", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST549"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944af", "priorCarrierPolicyNumber": "TEST550", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 213", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 213", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944af", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST550"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944af", "priorCarrierPolicyNumber": "TEST551", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 213", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 213", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944af", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST551"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944af", "priorCarrierPolicyNumber": "TEST552", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 213", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 213", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944af", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST552"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b0", "priorCarrierPolicyNumber": "TEST553", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 214", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 214", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST553"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b0", "priorCarrierPolicyNumber": "TEST554", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 214", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 214", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST554"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b0", "priorCarrierPolicyNumber": "TEST555", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 214", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 214", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST555"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b0", "priorCarrierPolicyNumber": "TEST556", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 214", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 214", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST556"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b1", "priorCarrierPolicyNumber": "TEST557", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 215", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 215", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST557"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b1", "priorCarrierPolicyNumber": "TEST558", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 215", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 215", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST558"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b1", "priorCarrierPolicyNumber": "TEST559", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 215", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 215", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST559"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b1", "priorCarrierPolicyNumber": "TEST560", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 215", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 215", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST560"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b2", "priorCarrierPolicyNumber": "TEST561", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 216", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 216", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST561"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b2", "priorCarrierPolicyNumber": "TEST562", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 216", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 216", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST562"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b2", "priorCarrierPolicyNumber": "TEST563", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 216", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 216", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST563"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b3", "priorCarrierPolicyNumber": "TEST564", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 217", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 217", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST564"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b3", "priorCarrierPolicyNumber": "TEST565", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 217", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 217", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST565"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b3", "priorCarrierPolicyNumber": "TEST566", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 217", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 217", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST566"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b3", "priorCarrierPolicyNumber": "TEST567", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 217", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 217", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST567"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b3", "priorCarrierPolicyNumber": "TEST568", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 217", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 217", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST568"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b4", "priorCarrierPolicyNumber": "TEST569", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 218", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 218", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST569"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b4", "priorCarrierPolicyNumber": "TEST570", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 218", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 218", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST570"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b4", "priorCarrierPolicyNumber": "TEST571", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 218", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 218", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST571"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b4", "priorCarrierPolicyNumber": "TEST572", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 218", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 218", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST572"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b5", "priorCarrierPolicyNumber": "TEST573", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 219", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 219", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST573"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b6", "priorCarrierPolicyNumber": "TEST574", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 220", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 220", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST574"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b6", "priorCarrierPolicyNumber": "TEST575", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 220", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 220", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST575"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b6", "priorCarrierPolicyNumber": "TEST576", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 220", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 220", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST576"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b7", "priorCarrierPolicyNumber": "TEST577", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 221", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 221", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST577"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b8", "priorCarrierPolicyNumber": "TEST578", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 222", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 222", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST578"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944b9", "priorCarrierPolicyNumber": "TEST579", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 223", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 223", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944b9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST579"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ba", "priorCarrierPolicyNumber": "TEST580", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 224", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 224", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ba", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST580"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ba", "priorCarrierPolicyNumber": "TEST581", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 224", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 224", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ba", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST581"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ba", "priorCarrierPolicyNumber": "TEST582", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 224", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 224", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ba", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST582"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944ba", "priorCarrierPolicyNumber": "TEST583", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 224", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 224", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944ba", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST583"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944bb", "priorCarrierPolicyNumber": "TEST584", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 225", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 225", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944bb", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST584"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944bc", "priorCarrierPolicyNumber": "TEST585", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 226", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 226", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944bc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST585"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944bc", "priorCarrierPolicyNumber": "TEST586", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 226", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 226", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944bc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST586"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944bc", "priorCarrierPolicyNumber": "TEST587", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 226", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 226", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944bc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST587"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944bc", "priorCarrierPolicyNumber": "TEST588", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 226", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 226", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944bc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST588"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944bd", "priorCarrierPolicyNumber": "TEST589", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 227", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 227", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944bd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST589"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944be", "priorCarrierPolicyNumber": "TEST590", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 228", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 228", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944be", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST590"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944be", "priorCarrierPolicyNumber": "TEST591", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 228", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 228", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944be", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST591"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944be", "priorCarrierPolicyNumber": "TEST592", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 228", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 228", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944be", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST592"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944be", "priorCarrierPolicyNumber": "TEST593", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 228", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 228", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944be", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST593"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944bf", "priorCarrierPolicyNumber": "TEST594", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 229", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 229", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944bf", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST594"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944c0", "priorCarrierPolicyNumber": "TEST595", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 230", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 230", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944c0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST595"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944c1", "priorCarrierPolicyNumber": "TEST596", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 231", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 231", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944c1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST596"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944c1", "priorCarrierPolicyNumber": "TEST597", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 231", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 231", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944c1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST597"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944c1", "priorCarrierPolicyNumber": "TEST598", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 231", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 231", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944c1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST598"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944c2", "priorCarrierPolicyNumber": "TEST599", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 232", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 232", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944c2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST599"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d17af44ccbefb88944c2", "priorCarrierPolicyNumber": "TEST600", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 232", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 232", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d17af44ccbefb88944c2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST600"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}]