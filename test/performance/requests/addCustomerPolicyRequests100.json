[{"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886bc", "priorCarrierPolicyNumber": "TEST1", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 1", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 1", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886bc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST1"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886bc", "priorCarrierPolicyNumber": "TEST2", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 1", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 1", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886bc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST2"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886bc", "priorCarrierPolicyNumber": "TEST3", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 1", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 1", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886bc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST3"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886bd", "priorCarrierPolicyNumber": "TEST4", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 2", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 2", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886bd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST4"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886bd", "priorCarrierPolicyNumber": "TEST5", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 2", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 2", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886bd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST5"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886bd", "priorCarrierPolicyNumber": "TEST6", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 2", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 2", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886bd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST6"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886bd", "priorCarrierPolicyNumber": "TEST7", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 2", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 2", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886bd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST7"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886bd", "priorCarrierPolicyNumber": "TEST8", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 2", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 2", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886bd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST8"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886be", "priorCarrierPolicyNumber": "TEST9", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 3", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 3", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886be", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST9"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886be", "priorCarrierPolicyNumber": "TEST10", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 3", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 3", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886be", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST10"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886be", "priorCarrierPolicyNumber": "TEST11", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 3", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 3", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886be", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST11"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886bf", "priorCarrierPolicyNumber": "TEST12", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 4", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 4", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886bf", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST12"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c0", "priorCarrierPolicyNumber": "TEST13", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 5", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 5", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST13"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c1", "priorCarrierPolicyNumber": "TEST14", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 6", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 6", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST14"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c2", "priorCarrierPolicyNumber": "TEST15", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 7", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 7", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST15"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c2", "priorCarrierPolicyNumber": "TEST16", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 7", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 7", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST16"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c2", "priorCarrierPolicyNumber": "TEST17", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 7", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 7", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST17"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c2", "priorCarrierPolicyNumber": "TEST18", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 7", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 7", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST18"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c2", "priorCarrierPolicyNumber": "TEST19", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 7", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 7", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST19"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c3", "priorCarrierPolicyNumber": "TEST20", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 8", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 8", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST20"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c4", "priorCarrierPolicyNumber": "TEST21", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 9", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 9", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST21"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c4", "priorCarrierPolicyNumber": "TEST22", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 9", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 9", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST22"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c4", "priorCarrierPolicyNumber": "TEST23", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 9", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 9", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST23"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c4", "priorCarrierPolicyNumber": "TEST24", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 9", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 9", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST24"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c5", "priorCarrierPolicyNumber": "TEST25", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 10", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 10", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST25"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c5", "priorCarrierPolicyNumber": "TEST26", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 10", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 10", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST26"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c5", "priorCarrierPolicyNumber": "TEST27", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 10", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 10", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST27"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c5", "priorCarrierPolicyNumber": "TEST28", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 10", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 10", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST28"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c5", "priorCarrierPolicyNumber": "TEST29", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 10", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 10", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST29"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c6", "priorCarrierPolicyNumber": "TEST30", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 11", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 11", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST30"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c6", "priorCarrierPolicyNumber": "TEST31", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 11", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 11", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST31"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c6", "priorCarrierPolicyNumber": "TEST32", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 11", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 11", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST32"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c7", "priorCarrierPolicyNumber": "TEST33", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 12", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 12", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST33"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c8", "priorCarrierPolicyNumber": "TEST34", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 13", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 13", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST34"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c8", "priorCarrierPolicyNumber": "TEST35", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 13", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 13", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST35"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c8", "priorCarrierPolicyNumber": "TEST36", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 13", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 13", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST36"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c8", "priorCarrierPolicyNumber": "TEST37", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 13", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 13", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST37"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c8", "priorCarrierPolicyNumber": "TEST38", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 13", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 13", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST38"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886c9", "priorCarrierPolicyNumber": "TEST39", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 14", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 14", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886c9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST39"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886ca", "priorCarrierPolicyNumber": "TEST40", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 15", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 15", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886ca", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST40"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886cb", "priorCarrierPolicyNumber": "TEST41", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 16", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 16", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886cb", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST41"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886cc", "priorCarrierPolicyNumber": "TEST42", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 17", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 17", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886cc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST42"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886cc", "priorCarrierPolicyNumber": "TEST43", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 17", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 17", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886cc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST43"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886cc", "priorCarrierPolicyNumber": "TEST44", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 17", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 17", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886cc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST44"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886cc", "priorCarrierPolicyNumber": "TEST45", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 17", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 17", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886cc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST45"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886cc", "priorCarrierPolicyNumber": "TEST46", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 17", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 17", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886cc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST46"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886cd", "priorCarrierPolicyNumber": "TEST47", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 18", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 18", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886cd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST47"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886ce", "priorCarrierPolicyNumber": "TEST48", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 19", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 19", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886ce", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST48"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886ce", "priorCarrierPolicyNumber": "TEST49", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 19", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 19", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886ce", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST49"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886ce", "priorCarrierPolicyNumber": "TEST50", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 19", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 19", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886ce", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST50"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886cf", "priorCarrierPolicyNumber": "TEST51", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 20", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 20", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886cf", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST51"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886cf", "priorCarrierPolicyNumber": "TEST52", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 20", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 20", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886cf", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST52"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886cf", "priorCarrierPolicyNumber": "TEST53", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 20", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 20", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886cf", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST53"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886cf", "priorCarrierPolicyNumber": "TEST54", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 20", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 20", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886cf", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST54"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d0", "priorCarrierPolicyNumber": "TEST55", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 21", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 21", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST55"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d1", "priorCarrierPolicyNumber": "TEST56", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 22", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 22", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST56"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d2", "priorCarrierPolicyNumber": "TEST57", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 23", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 23", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST57"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d2", "priorCarrierPolicyNumber": "TEST58", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 23", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 23", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST58"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d2", "priorCarrierPolicyNumber": "TEST59", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 23", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 23", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST59"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d2", "priorCarrierPolicyNumber": "TEST60", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 23", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 23", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST60"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d3", "priorCarrierPolicyNumber": "TEST61", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 24", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 24", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST61"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d4", "priorCarrierPolicyNumber": "TEST62", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 25", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 25", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST62"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d5", "priorCarrierPolicyNumber": "TEST63", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 26", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 26", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d5", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST63"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d6", "priorCarrierPolicyNumber": "TEST64", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 27", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 27", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST64"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d6", "priorCarrierPolicyNumber": "TEST65", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 27", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 27", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST65"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d6", "priorCarrierPolicyNumber": "TEST66", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 27", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 27", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST66"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d6", "priorCarrierPolicyNumber": "TEST67", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 27", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 27", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST67"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d6", "priorCarrierPolicyNumber": "TEST68", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 27", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 27", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d6", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST68"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d7", "priorCarrierPolicyNumber": "TEST69", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 28", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 28", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST69"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d7", "priorCarrierPolicyNumber": "TEST70", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 28", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 28", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST70"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d7", "priorCarrierPolicyNumber": "TEST71", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 28", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 28", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d7", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST71"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d8", "priorCarrierPolicyNumber": "TEST72", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 29", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 29", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d8", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST72"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d9", "priorCarrierPolicyNumber": "TEST73", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 30", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 30", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST73"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d9", "priorCarrierPolicyNumber": "TEST74", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 30", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 30", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST74"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d9", "priorCarrierPolicyNumber": "TEST75", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 30", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 30", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST75"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d9", "priorCarrierPolicyNumber": "TEST76", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 30", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 30", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST76"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886d9", "priorCarrierPolicyNumber": "TEST77", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 30", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 30", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886d9", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST77"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886da", "priorCarrierPolicyNumber": "TEST78", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 31", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 31", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886da", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST78"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886db", "priorCarrierPolicyNumber": "TEST79", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 32", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 32", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886db", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST79"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886dc", "priorCarrierPolicyNumber": "TEST80", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 33", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 33", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886dc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST80"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886dc", "priorCarrierPolicyNumber": "TEST81", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 33", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 33", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886dc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST81"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886dc", "priorCarrierPolicyNumber": "TEST82", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 33", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 33", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886dc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST82"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886dc", "priorCarrierPolicyNumber": "TEST83", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 33", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 33", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886dc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST83"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886dc", "priorCarrierPolicyNumber": "TEST84", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 33", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 33", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886dc", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST84"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886dd", "priorCarrierPolicyNumber": "TEST85", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 34", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 34", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886dd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST85"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886dd", "priorCarrierPolicyNumber": "TEST86", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 34", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 34", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886dd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST86"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886dd", "priorCarrierPolicyNumber": "TEST87", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 34", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 34", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886dd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST87"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886dd", "priorCarrierPolicyNumber": "TEST88", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 34", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 34", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886dd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST88"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886dd", "priorCarrierPolicyNumber": "TEST89", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 34", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 34", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886dd", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST89"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886de", "priorCarrierPolicyNumber": "TEST90", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 35", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 35", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886de", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST90"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886df", "priorCarrierPolicyNumber": "TEST91", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 36", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 36", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886df", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST91"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886e0", "priorCarrierPolicyNumber": "TEST92", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 37", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 37", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886e0", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST92"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886e1", "priorCarrierPolicyNumber": "TEST93", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 38", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 38", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886e1", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST93"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886e2", "priorCarrierPolicyNumber": "TEST94", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 39", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 39", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886e2", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST94"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886e3", "priorCarrierPolicyNumber": "TEST95", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 40", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 40", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886e3", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST95"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886e4", "priorCarrierPolicyNumber": "TEST96", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 41", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 41", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886e4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST96"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886e4", "priorCarrierPolicyNumber": "TEST97", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 41", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 41", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886e4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST97"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886e4", "priorCarrierPolicyNumber": "TEST98", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 41", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 41", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886e4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST98"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886e4", "priorCarrierPolicyNumber": "TEST99", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 41", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 41", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886e4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST99"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}, {"searchCriteria": {"customerAccountId": "67e1d0e5257ed137f5e886e4", "priorCarrierPolicyNumber": "TEST100", "btCode": "0", "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 41", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}}, "customerMetadata": {"btCode": "0", "customerName": "Automated Customer 41", "address1": "518 E Broad St", "city": "Columbus", "state": "OH", "zip": "43215"}, "customerPolicies": [{"customerAccountId": "67e1d0e5257ed137f5e886e4", "priorPolicy": {"expirationDate": null, "premium": null, "lineOfBusiness": "AUTOB", "carrier": "Prior Carrier", "policyNumber": "TEST100"}, "libertyPolicy": {"quoterComments": null, "policyNumber": null, "effectiveDate": null, "premium": null, "producer": null, "status": null, "lineOfBusiness": null}}], "eventSource": "PERFTEST"}]