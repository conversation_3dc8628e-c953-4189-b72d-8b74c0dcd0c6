<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0" jmeter="5.6.3">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="Test Plan">
      <boolProp name="TestPlan.serialize_threadgroups">true</boolProp>
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="User Defined Variables">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Thread Group">
        <intProp name="ThreadGroup.num_threads">1</intProp>
        <intProp name="ThreadGroup.ramp_time">1</intProp>
        <boolProp name="ThreadGroup.same_user_on_next_iteration">true</boolProp>
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="Loop Controller">
          <stringProp name="LoopController.loops">1</stringProp>
          <boolProp name="LoopController.continue_forever">false</boolProp>
        </elementProp>
      </ThreadGroup>
      <hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Token">
          <stringProp name="HTTPSampler.domain">test-lmidp.libertymutual.com</stringProp>
          <stringProp name="HTTPSampler.protocol">https</stringProp>
          <stringProp name="HTTPSampler.path">/as/token.oauth2?aud=https://book-transfer-customer-account-service.test.amazon-web-services-************-us-east-1</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables">
            <collectionProp name="Arguments.arguments">
              <elementProp name="grant_type" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">client_credentials</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
                <boolProp name="HTTPArgument.use_equals">true</boolProp>
                <stringProp name="Argument.name">grant_type</stringProp>
              </elementProp>
              <elementProp name="client_id" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">${clientId}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
                <boolProp name="HTTPArgument.use_equals">true</boolProp>
                <stringProp name="Argument.name">client_id</stringProp>
              </elementProp>
              <elementProp name="client_secret" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">${clientSecret}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
                <boolProp name="HTTPArgument.use_equals">true</boolProp>
                <stringProp name="Argument.name">client_secret</stringProp>
              </elementProp>
              <elementProp name="scope" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">write</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
                <boolProp name="HTTPArgument.use_equals">true</boolProp>
                <stringProp name="Argument.name">scope</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
        </HTTPSamplerProxy>
        <hashTree>
          <Arguments guiclass="ArgumentsPanel" testclass="Arguments" testname="User Defined Variables">
            <collectionProp name="Arguments.arguments">
              <elementProp name="clientId" elementType="Argument">
                <stringProp name="Argument.name">clientId</stringProp>
                <stringProp name="Argument.value">${__BeanShell( System.getenv(&quot;DOCKERDISTLOADTESTDEPLOY_PEPTOAUTH_ID&quot;) )}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
              <elementProp name="clientSecret" elementType="Argument">
                <stringProp name="Argument.name">clientSecret</stringProp>
                <stringProp name="Argument.value">${__BeanShell( System.getenv(&quot;DOCKERDISTLOADTESTDEPLOY_PEPTOAUTH_SECRET&quot;) )}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </Arguments>
          <hashTree/>
          <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON Extractor">
            <stringProp name="JSONPostProcessor.referenceNames">bearerToken</stringProp>
            <stringProp name="JSONPostProcessor.jsonPathExprs">$.access_token</stringProp>
            <stringProp name="JSONPostProcessor.match_numbers"></stringProp>
          </JSONPostProcessor>
          <hashTree/>
          <BeanShellAssertion guiclass="BeanShellAssertionGui" testclass="BeanShellAssertion" testname="BeanShell Assertion">
            <stringProp name="BeanShellAssertion.query">${__setProperty(bearerToken, ${bearerToken})};</stringProp>
            <stringProp name="BeanShellAssertion.filename"></stringProp>
            <stringProp name="BeanShellAssertion.parameters"></stringProp>
            <boolProp name="BeanShellAssertion.resetInterpreter">false</boolProp>
          </BeanShellAssertion>
          <hashTree/>
        </hashTree>
      </hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Thread Group" enabled="true">
        <intProp name="ThreadGroup.num_threads">4</intProp>
        <intProp name="ThreadGroup.ramp_time">1</intProp>
        <boolProp name="ThreadGroup.same_user_on_next_iteration">true</boolProp>
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="Loop Controller">
          <stringProp name="LoopController.loops">1</stringProp>
          <boolProp name="LoopController.continue_forever">false</boolProp>
        </elementProp>
      </ThreadGroup>
      <hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="AddCustomerPolicy">
          <stringProp name="HTTPSampler.domain">nbd6l2f4w0-vpce-074828fb930397a5a.execute-api.us-east-1.amazonaws.com</stringProp>
          <stringProp name="HTTPSampler.protocol">https</stringProp>
          <stringProp name="HTTPSampler.path">/test/api/policy</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
          <stringProp name="HTTPSampler.method">POST</stringProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">${RequestBody}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
        </HTTPSamplerProxy>
        <hashTree>
          <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager">
            <collectionProp name="HeaderManager.headers">
              <elementProp name="" elementType="Header">
                <stringProp name="Header.name">Authorization</stringProp>
                <stringProp name="Header.value">Bearer ${__property(bearerToken)}</stringProp>
              </elementProp>
              <elementProp name="" elementType="Header">
                <stringProp name="Header.name">Content-Type</stringProp>
                <stringProp name="Header.value">application/fhir+json</stringProp>
              </elementProp>
            </collectionProp>
          </HeaderManager>
          <hashTree/>
          <JSR223PreProcessor guiclass="TestBeanGUI" testclass="JSR223PreProcessor" testname="JSR223 PreProcessor">
            <stringProp name="scriptLanguage">groovy</stringProp>
            <stringProp name="parameters">${__BeanShell(import org.apache.jmeter.services.FileServer;FileServer.getFileServer().getBaseDir();)}</stringProp>
            <stringProp name="filename"></stringProp>
            <stringProp name="cacheKey">true</stringProp>
            <stringProp name="script">def threadGroupName = ${__threadNum};

log.info(&quot;Thread Group: &quot; + ${__threadNum});

def request;

if (threadGroupName == 1) {
  request = new File(args[0] + &quot;/requests/addCustomerPolicyRequests100.json&quot;).text;
}else if(threadGroupName == 2) {
  request = new File(args[0] + &quot;/requests/addCustomerPolicyRequests200.json&quot;).text;
}else if (threadGroupName == 3) {
  request = new File(args[0] + &quot;/requests/addCustomerPolicyRequests300.json&quot;).text;
}else if(threadGroupName == 4) {
	request = new File(args[0] + &quot;/requests/addCustomerPolicyRequests400.json&quot;).text;
}

log.info(&quot;request: &quot;+ request);

vars.put(&quot;RequestBody&quot;, request);</stringProp>
          </JSR223PreProcessor>
          <hashTree/>
        </hashTree>
      </hashTree>
      <ConstantTimer guiclass="ConstantTimerGui" testclass="ConstantTimer" testname="Constant Timer">
        <stringProp name="ConstantTimer.delay">30000</stringProp>
      </ConstantTimer>
      <hashTree/>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="deleteCustomer" enabled="true">
        <intProp name="ThreadGroup.num_threads">1</intProp>
        <intProp name="ThreadGroup.ramp_time">1</intProp>
        <boolProp name="ThreadGroup.same_user_on_next_iteration">true</boolProp>
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="Loop Controller">
          <stringProp name="LoopController.loops">1</stringProp>
          <boolProp name="LoopController.continue_forever">false</boolProp>
        </elementProp>
      </ThreadGroup>
      <hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="deleteCustomer">
          <stringProp name="HTTPSampler.domain">nbd6l2f4w0-vpce-074828fb930397a5a.execute-api.us-east-1.amazonaws.com</stringProp>
          <stringProp name="HTTPSampler.protocol">https</stringProp>
          <stringProp name="HTTPSampler.path">/test/api/customerAccount</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
          <stringProp name="HTTPSampler.method">DELETE</stringProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
          <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">{&#xd;
	&quot;customerIds&quot;:&#xd;
	[&#xd;
   &quot;67e1d0e5257ed137f5e886bd&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886bc&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886cf&quot;,&#xd;
&quot;67e1d143925a58dc270ac1b1&quot;,&#xd;
&quot;67e1d143925a58dc270ac1bf&quot;,&#xd;
&quot;67e1d143925a58dc270ac1be&quot;,&#xd;
&quot;67e1d143925a58dc270ac1c0&quot;,&#xd;
&quot;67e1d143925a58dc270ac1c1&quot;,&#xd;
&quot;67e1d143925a58dc270ac1c2&quot;,&#xd;
&quot;67e1d143925a58dc270ac1c3&quot;,&#xd;
&quot;67e1d179f44ccbefb889444d&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886ca&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e22&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e21&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886cc&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886c4&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886bf&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886c6&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886c5&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886c2&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886c8&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886be&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886c3&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886ce&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886c0&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886c7&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886cd&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886cb&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886d3&quot;,&#xd;
&quot;67e1d143925a58dc270ac1bd&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886c1&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886c9&quot;,&#xd;
&quot;67e1d143925a58dc270ac1bb&quot;,&#xd;
&quot;67e1d143925a58dc270ac1c4&quot;,&#xd;
&quot;67e1d143925a58dc270ac1c7&quot;,&#xd;
&quot;67e1d143925a58dc270ac1c5&quot;,&#xd;
&quot;67e1d143925a58dc270ac1ba&quot;,&#xd;
&quot;67e1d143925a58dc270ac1bc&quot;,&#xd;
&quot;67e1d143925a58dc270ac1b2&quot;,&#xd;
&quot;67e1d143925a58dc270ac1b8&quot;,&#xd;
&quot;67e1d143925a58dc270ac1b7&quot;,&#xd;
&quot;67e1d143925a58dc270ac1c6&quot;,&#xd;
&quot;67e1d143925a58dc270ac1b3&quot;,&#xd;
&quot;67e1d143925a58dc270ac1b6&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886d0&quot;,&#xd;
&quot;67e1d143925a58dc270ac1c8&quot;,&#xd;
&quot;67e1d143925a58dc270ac1c9&quot;,&#xd;
&quot;67e1d143925a58dc270ac1b9&quot;,&#xd;
&quot;67e1d143925a58dc270ac1b5&quot;,&#xd;
&quot;67e1d143925a58dc270ac1b4&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e34&quot;,&#xd;
&quot;67e1d143925a58dc270ac1ca&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886d6&quot;,&#xd;
&quot;67e1d179f44ccbefb8894453&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886d7&quot;,&#xd;
&quot;67e1d179f44ccbefb8894457&quot;,&#xd;
&quot;67e1d179f44ccbefb8894454&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886d8&quot;,&#xd;
&quot;67e1d179f44ccbefb889444f&quot;,&#xd;
&quot;67e1d179f44ccbefb8894464&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886d9&quot;,&#xd;
&quot;67e1d179f44ccbefb8894455&quot;,&#xd;
&quot;67e1d179f44ccbefb889445b&quot;,&#xd;
&quot;67e1d179f44ccbefb8894462&quot;,&#xd;
&quot;67e1d179f44ccbefb8894460&quot;,&#xd;
&quot;67e1d179f44ccbefb889445a&quot;,&#xd;
&quot;67e1d179f44ccbefb8894466&quot;,&#xd;
&quot;67e1d179f44ccbefb8894450&quot;,&#xd;
&quot;67e1d179f44ccbefb889445e&quot;,&#xd;
&quot;67e1d179f44ccbefb8894465&quot;,&#xd;
&quot;67e1d179f44ccbefb889445d&quot;,&#xd;
&quot;67e1d179f44ccbefb889445c&quot;,&#xd;
&quot;67e1d179f44ccbefb8894456&quot;,&#xd;
&quot;67e1d179f44ccbefb889444e&quot;,&#xd;
&quot;67e1d179f44ccbefb8894461&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886db&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886da&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886dc&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886dd&quot;,&#xd;
&quot;67e1d179f44ccbefb889445f&quot;,&#xd;
&quot;67e1d179f44ccbefb8894452&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e25&quot;,&#xd;
&quot;67e1d179f44ccbefb8894451&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e2e&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e35&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e2b&quot;,&#xd;
&quot;67e1d179f44ccbefb8894459&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e24&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e23&quot;,&#xd;
&quot;67e1d179f44ccbefb8894463&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e28&quot;,&#xd;
&quot;67e1d179f44ccbefb8894458&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e32&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e33&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e31&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e27&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e2a&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886de&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e30&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e26&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e2d&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886d1&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e2c&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e2f&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886e0&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886df&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886e1&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886e4&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e29&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886e2&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886e3&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886d2&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886d4&quot;,&#xd;
&quot;67e1d0e5257ed137f5e886d5&quot;,&#xd;
&quot;67e1d143925a58dc270ac1cb&quot;,&#xd;
&quot;67e1d143925a58dc270ac1cc&quot;,&#xd;
&quot;67e1d143925a58dc270ac1cd&quot;,&#xd;
&quot;67e1d143925a58dc270ac1ce&quot;,&#xd;
&quot;67e1d143925a58dc270ac1cf&quot;,&#xd;
&quot;67e1d143925a58dc270ac1d1&quot;,&#xd;
&quot;67e1d143925a58dc270ac1d2&quot;,&#xd;
&quot;67e1d143925a58dc270ac1d5&quot;,&#xd;
&quot;67e1d143925a58dc270ac1d6&quot;,&#xd;
&quot;67e1d143925a58dc270ac1d7&quot;,&#xd;
&quot;67e1d143925a58dc270ac1d4&quot;,&#xd;
&quot;67e1d143925a58dc270ac1d3&quot;,&#xd;
&quot;67e1d143925a58dc270ac1d0&quot;,&#xd;
&quot;67e1d143925a58dc270ac1d9&quot;,&#xd;
&quot;67e1d143925a58dc270ac1d8&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e36&quot;,&#xd;
&quot;67e1d179f44ccbefb8894467&quot;,&#xd;
&quot;67e1d179f44ccbefb8894469&quot;,&#xd;
&quot;67e1d179f44ccbefb8894468&quot;,&#xd;
&quot;67e1d179f44ccbefb889446d&quot;,&#xd;
&quot;67e1d179f44ccbefb8894471&quot;,&#xd;
&quot;67e1d179f44ccbefb8894470&quot;,&#xd;
&quot;67e1d179f44ccbefb889446f&quot;,&#xd;
&quot;67e1d179f44ccbefb889446c&quot;,&#xd;
&quot;67e1d179f44ccbefb8894475&quot;,&#xd;
&quot;67e1d179f44ccbefb889446b&quot;,&#xd;
&quot;67e1d179f44ccbefb8894472&quot;,&#xd;
&quot;67e1d179f44ccbefb889446e&quot;,&#xd;
&quot;67e1d179f44ccbefb889446a&quot;,&#xd;
&quot;67e1d179f44ccbefb8894474&quot;,&#xd;
&quot;67e1d179f44ccbefb8894473&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e38&quot;,&#xd;
&quot;67e1d179f44ccbefb8894479&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e3b&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e37&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e3d&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e3c&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e3e&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e44&quot;,&#xd;
&quot;67e1d179f44ccbefb8894478&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e47&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e3a&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e39&quot;,&#xd;
&quot;67e1d179f44ccbefb8894477&quot;,&#xd;
&quot;67e1d179f44ccbefb8894476&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e40&quot;,&#xd;
&quot;67e1d179f44ccbefb889447a&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e3f&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e48&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e41&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e46&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e42&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e45&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e43&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e49&quot;,&#xd;
&quot;67e1d143925a58dc270ac1da&quot;,&#xd;
&quot;67e1d143925a58dc270ac1db&quot;,&#xd;
&quot;67e1d143925a58dc270ac1dc&quot;,&#xd;
&quot;67e1d143925a58dc270ac1dd&quot;,&#xd;
&quot;67e1d143925a58dc270ac1de&quot;,&#xd;
&quot;67e1d143925a58dc270ac1e0&quot;,&#xd;
&quot;67e1d143925a58dc270ac1df&quot;,&#xd;
&quot;67e1d143925a58dc270ac1e5&quot;,&#xd;
&quot;67e1d143925a58dc270ac1ed&quot;,&#xd;
&quot;67e1d143925a58dc270ac1e1&quot;,&#xd;
&quot;67e1d143925a58dc270ac1e8&quot;,&#xd;
&quot;67e1d143925a58dc270ac1ea&quot;,&#xd;
&quot;67e1d143925a58dc270ac1e3&quot;,&#xd;
&quot;67e1d143925a58dc270ac1e2&quot;,&#xd;
&quot;67e1d143925a58dc270ac1e4&quot;,&#xd;
&quot;67e1d143925a58dc270ac1eb&quot;,&#xd;
&quot;67e1d143925a58dc270ac1e7&quot;,&#xd;
&quot;67e1d143925a58dc270ac1e9&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e4a&quot;,&#xd;
&quot;67e1d143925a58dc270ac1ec&quot;,&#xd;
&quot;67e1d143925a58dc270ac1e6&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e4b&quot;,&#xd;
&quot;67e1d143925a58dc270ac1ee&quot;,&#xd;
&quot;67e1d179f44ccbefb889447b&quot;,&#xd;
&quot;67e1d179f44ccbefb889447e&quot;,&#xd;
&quot;67e1d179f44ccbefb889447d&quot;,&#xd;
&quot;67e1d179f44ccbefb889447c&quot;,&#xd;
&quot;67e1d179f44ccbefb889447f&quot;,&#xd;
&quot;67e1d179f44ccbefb8894482&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e4c&quot;,&#xd;
&quot;67e1d179f44ccbefb8894483&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e53&quot;,&#xd;
&quot;67e1d179f44ccbefb8894480&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e4f&quot;,&#xd;
&quot;67e1d179f44ccbefb8894484&quot;,&#xd;
&quot;67e1d179f44ccbefb8894487&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e51&quot;,&#xd;
&quot;67e1d143925a58dc270ac1ef&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e5a&quot;,&#xd;
&quot;67e1d179f44ccbefb8894489&quot;,&#xd;
&quot;67e1d179f44ccbefb8894486&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e54&quot;,&#xd;
&quot;67e1d179f44ccbefb8894488&quot;,&#xd;
&quot;67e1d17af44ccbefb889448b&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e50&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e4d&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e5d&quot;,&#xd;
&quot;67e1d179f44ccbefb8894481&quot;,&#xd;
&quot;67e1d17af44ccbefb889448a&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e5e&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e52&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e55&quot;,&#xd;
&quot;67e1d179f44ccbefb8894485&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e4e&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e5b&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e59&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e56&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e62&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e58&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e60&quot;,&#xd;
&quot;67e1d17af44ccbefb889448c&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e5f&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e61&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e57&quot;,&#xd;
&quot;67e1d143925a58dc270ac1f0&quot;,&#xd;
&quot;67e1d143925a58dc270ac1f1&quot;,&#xd;
&quot;67e1d143925a58dc270ac1f2&quot;,&#xd;
&quot;67e1d143925a58dc270ac1f4&quot;,&#xd;
&quot;67e1d143925a58dc270ac1f5&quot;,&#xd;
&quot;67e1d143925a58dc270ac1f3&quot;,&#xd;
&quot;67e1d143925a58dc270ac1f7&quot;,&#xd;
&quot;67e1d143925a58dc270ac1f6&quot;,&#xd;
&quot;67e1d143925a58dc270ac1f8&quot;,&#xd;
&quot;67e1d143925a58dc270ac1f9&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e64&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e63&quot;,&#xd;
&quot;67e1d17af44ccbefb889448d&quot;,&#xd;
&quot;67e1d17af44ccbefb889448e&quot;,&#xd;
&quot;67e1d17af44ccbefb889448f&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e65&quot;,&#xd;
&quot;67e1d17af44ccbefb8894496&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e66&quot;,&#xd;
&quot;67e1d17af44ccbefb8894491&quot;,&#xd;
&quot;67e1d17af44ccbefb8894493&quot;,&#xd;
&quot;67e1d17af44ccbefb8894495&quot;,&#xd;
&quot;67e1d17af44ccbefb8894492&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e6d&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e67&quot;,&#xd;
&quot;67e1d17af44ccbefb889449b&quot;,&#xd;
&quot;67e1d17af44ccbefb8894497&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e73&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e69&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e6f&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e6c&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e70&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e72&quot;,&#xd;
&quot;67e1d17af44ccbefb8894498&quot;,&#xd;
&quot;67e1d17af44ccbefb889449a&quot;,&#xd;
&quot;67e1d17af44ccbefb8894499&quot;,&#xd;
&quot;67e1d17af44ccbefb8894494&quot;,&#xd;
&quot;67e1d17af44ccbefb8894490&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e71&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e6e&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e6b&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e74&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e6a&quot;,&#xd;
&quot;67e1d17af44ccbefb889449c&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e68&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e75&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e76&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e77&quot;,&#xd;
&quot;67e1d17af44ccbefb889449f&quot;,&#xd;
&quot;67e1d17af44ccbefb889449d&quot;,&#xd;
&quot;67e1d17af44ccbefb889449e&quot;,&#xd;
&quot;67e1d17af44ccbefb88944a1&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e7d&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e7a&quot;,&#xd;
&quot;67e1d17af44ccbefb88944a5&quot;,&#xd;
&quot;67e1d17af44ccbefb88944ae&quot;,&#xd;
&quot;67e1d17af44ccbefb88944a6&quot;,&#xd;
&quot;67e1d17af44ccbefb88944a3&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e86&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e80&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e85&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e7e&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e81&quot;,&#xd;
&quot;67e1d17af44ccbefb88944ac&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e7f&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e79&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e88&quot;,&#xd;
&quot;67e1d17af44ccbefb88944ad&quot;,&#xd;
&quot;67e1d17af44ccbefb88944b0&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e84&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e83&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e87&quot;,&#xd;
&quot;67e1d17af44ccbefb88944a7&quot;,&#xd;
&quot;67e1d17af44ccbefb88944af&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e82&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e7b&quot;,&#xd;
&quot;67e1d17af44ccbefb88944a8&quot;,&#xd;
&quot;67e1d17af44ccbefb88944ab&quot;,&#xd;
&quot;67e1d17af44ccbefb88944a2&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e78&quot;,&#xd;
&quot;67e1d17af44ccbefb88944a9&quot;,&#xd;
&quot;67e1d17af44ccbefb88944a4&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e7c&quot;,&#xd;
&quot;67e1d17af44ccbefb88944a0&quot;,&#xd;
&quot;67e1d17af44ccbefb88944aa&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e89&quot;,&#xd;
&quot;67e1d17af44ccbefb88944b1&quot;,&#xd;
&quot;67e1d17af44ccbefb88944b2&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e96&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e92&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e8a&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e8c&quot;,&#xd;
&quot;67e1d17af44ccbefb88944b3&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e94&quot;,&#xd;
&quot;67e1d17af44ccbefb88944b8&quot;,&#xd;
&quot;67e1d17af44ccbefb88944be&quot;,&#xd;
&quot;67e1d17af44ccbefb88944b4&quot;,&#xd;
&quot;67e1d17af44ccbefb88944bc&quot;,&#xd;
&quot;67e1d17af44ccbefb88944c0&quot;,&#xd;
&quot;67e1d17af44ccbefb88944b7&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e99&quot;,&#xd;
&quot;67e1d17af44ccbefb88944bd&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e8b&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e8e&quot;,&#xd;
&quot;67e1d17af44ccbefb88944b9&quot;,&#xd;
&quot;67e1d17af44ccbefb88944c1&quot;,&#xd;
&quot;67e1d17af44ccbefb88944b6&quot;,&#xd;
&quot;67e1d17af44ccbefb88944bf&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e8d&quot;,&#xd;
&quot;67e1d17af44ccbefb88944ba&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e91&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e93&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e8f&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e9b&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e90&quot;,&#xd;
&quot;67e1d17af44ccbefb88944c2&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e97&quot;,&#xd;
&quot;67e1d17af44ccbefb88944b5&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e9a&quot;,&#xd;
&quot;67e1d17af44ccbefb88944bb&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e95&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e98&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e9c&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e9d&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0ea6&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0ea9&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0ea8&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0eaa&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0ea1&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0ea3&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0ea0&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0ea4&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e9e&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0eae&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0eac&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0ea2&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e9f&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0eab&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0ea5&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0ea7&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0ead&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0eaf&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0eb0&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0eb1&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0eb2&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0eb3&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0eb9&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0ebd&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0eb5&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0eb7&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0eb6&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0ebb&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0eba&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0ebc&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0eb8&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0ebe&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0eb4&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0ebf&quot;,&#xd;
&quot;67e1d1b47a1c3ad7659b0e5c&quot;&#xd;
]}</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
        </HTTPSamplerProxy>
        <hashTree>
          <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager">
            <collectionProp name="HeaderManager.headers">
              <elementProp name="" elementType="Header">
                <stringProp name="Header.name">Authorization</stringProp>
                <stringProp name="Header.value">Bearer ${__property(bearerToken)}</stringProp>
              </elementProp>
              <elementProp name="" elementType="Header">
                <stringProp name="Header.name">Content-Type</stringProp>
                <stringProp name="Header.value">application/json</stringProp>
              </elementProp>
            </collectionProp>
          </HeaderManager>
          <hashTree/>
        </hashTree>
      </hashTree>
      <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="View Results Tree">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>true</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
            <sentBytes>true</sentBytes>
            <url>true</url>
            <threadCounts>true</threadCounts>
            <idleTime>true</idleTime>
            <connectTime>true</connectTime>
          </value>
        </objProp>
        <stringProp name="filename"></stringProp>
      </ResultCollector>
      <hashTree/>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
