package forge;

import com.lmig.forge.bamboo.specs.patterns.PipelineConfiguration;

public class PipelineParameters {
  /**
   * DO NOT MODIFY THIS CLASS. It will be regenerated systematically by the CloudForge Console.
   * If you want to add additional environments, please do so through the Console.
   *
   * Note: any changes made to plan or deployment permissions within your pipeline spec will be reset to match
   * what exist in the Console. To make any changes to permissions, please do so through the Console.
   *
   **/

  private static final String GENERATED_AT = "Wed, 05 Jun 2024 19:51:44 GMT";

  public static final PipelineConfiguration PIPELINE_CONFIGURATION = PipelineConfiguration.builder()
    .artifactGuid("e2a85e6d-a71d-4f88-a0cb-e5ab31cb75b4")
    .build();
}
