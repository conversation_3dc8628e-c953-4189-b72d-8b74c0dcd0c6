package forge;

import static forge.PipelineParameters.PIPELINE_CONFIGURATION;
import static com.lmig.forge.bamboo.specs.patterns.DeploymentAddOn.CONFIGURE_IDP;
import static com.lmig.forge.bamboo.specs.patterns.DeploymentAddOn.APIGEE_APPLICATION;
import com.atlassian.bamboo.specs.api.BambooSpec;
import com.lmig.forge.bamboo.specs.patterns.AddOns;
import com.lmig.forge.bamboo.specs.patterns.build.aws.CdkTypescriptBuild;
import com.lmig.forge.bamboo.specs.patterns.deployment.aws.CdkTypescriptDeployment;
import static com.lmig.forge.bamboo.specs.forge.CloudForgeEnvironment.TEST;
import static com.lmig.forge.bamboo.specs.forge.CloudForgeEnvironment.deploymentMatches;
import com.atlassian.bamboo.specs.builders.task.ScriptTask;
import com.atlassian.bamboo.specs.model.task.ScriptTaskProperties;

@BambooSpec
public class Pipeline {

        private static final AddOns ADD_ONS_DEPLOY = new AddOns().deploymentAddOns(CONFIGURE_IDP, APIGEE_APPLICATION);

        public static void main(String[] args) {

                // Build
                new CdkTypescriptBuild(PIPELINE_CONFIGURATION)
                                .useCdkV2()
                                .publish();

                // Deployment
                new CdkTypescriptDeployment(PIPELINE_CONFIGURATION)
                                .autoDeployAfterSuccessfulBuild()
                                .cdkStacksToDeploy("${bamboo.forge.artifact.name}-${bamboo.forge.environment.key}")
                                .useCdkV2()
                                //This task runs only for test
                                .postDeploymentTask(new ScriptTask()
                                        .description("Invoke PEPT API endpoint and kick off perf tests")
                                        .interpreter(ScriptTaskProperties.Interpreter.BINSH_OR_CMDEXE).inlineBody(
                                                "curl -L -X POST 'https://api.us.lmig.com/pept-management/launch' -H " +
                                                "'x-api-key: " + "${bamboo.secret.apigeecreds-prd-test.client-id}" + "' -H " +
                                                "'Content-Type: application/json' -d '{\"configURL\":\"https://github.com/lmigtech/book-transfer-customer-account-service/raw/main/test/performance/user_config.properties\"}'"),
                                        deploymentMatches(TEST))
                                .addOns(ADD_ONS_DEPLOY)
                                .publish();
        }
}
