{"compilerOptions": {"target": "ES2018", "module": "commonjs", "lib": ["es2016", "es2017.object", "es2017.string"], "declaration": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "experimentalDecorators": true, "strictPropertyInitialization": false, "typeRoots": ["node_modules/@types"], "skipDefaultLibCheck": true, "skipLibCheck": true, "useUnknownInCatchVariables": false, "inlineSources": true, "sourceMap": true, "outDir": "./dist", "sourceRoot": "./lib"}, "exclude": ["cdk.out", "test", "node_modules", "**/*/dist", "./dist/**/*"]}