import { Context, APIGatewayProxyResult } from 'aws-lambda';
import { CustomerPolicyModel } from '../../../model/customerAccountModel';
import Request, { CustomerPolicySearchCriteria } from '../../../types/request';
import { getHandlerResponse } from '../utility/getHandlerResponse';
import {
  addEcliqAccountsById,
  findCustomerAccountById,
  updateCustomerPolicy,
  initializeMongo,
  searchForExistingCustomerPolicy,
  updateCustomerPolicyWithEcliqAccountNumber
} from './mongo';

export const saveEcliqAccountNumberLambda = async (event: any, context: Context): Promise<APIGatewayProxyResult> => {
  try {
    const request: Request = JSON.parse(event.body);
    const params = event.queryStringParameters;

    const id = params?.id;
    const policyId = params?.policyId;

    console.log(`Request: ${JSON.stringify(request)}`);
    console.log(`Params: ${JSON.stringify(params)}`);

    if (!id) {
      console.log(`No id was provided in ${process.env.ENVIRONMENT_KEY}`);
      return getHandlerResponse(400, { error: 'Customer account id was not provided' });
    }

    if (!policyId) {
      console.log(`No policyId was provided in ${process.env.ENVIRONMENT_KEY}`);
      return getHandlerResponse(400, { error: 'Policy id was not provided' });
    }

    if (!request.ecliqAccounts || request.ecliqAccounts.length === 0) {
      console.log(`No ecliqAccounts were provided in ${process.env.ENVIRONMENT_KEY} for ${id}`);
      return getHandlerResponse(400, { error: 'ecliqAccounts was not provided or is empty' });
    }

    if (request.ecliqAccounts.some((ecliqAccount) => !ecliqAccount.ecliqAccountNumber)) {
      console.log(`An ecliqAccount was missing an ecliqAccountNumber in ${process.env.ENVIRONMENT_KEY} for ${id}`);
      return getHandlerResponse(400, {
        error: 'An ecliqAccount was missing an ecliqAccountNumber',
      });
    }

    try {
      // make sure ecliqAccountCreationDate is a Date
      request.ecliqAccounts.forEach((ecliqAccount) => {
        if (!(ecliqAccount.ecliqAccountCreationDate instanceof Date)) {
          ecliqAccount.ecliqAccountCreationDate = new Date(ecliqAccount.ecliqAccountCreationDate);
        }
      });
    } catch (error) {
      console.error(error);
      return getHandlerResponse(400, {
        error: 'There was an error parsing the ecliqAccountCreationDate',
      });
    }

    console.log('Initializing MongoDB connection...');
    await initializeMongo(process.env.MONGODB_CONNECTION);

    let searchResults = await findCustomerAccountById(id);
    if (searchResults.length === 0) {
      console.log(`No customer account was found in ${process.env.ENVIRONMENT_KEY} with id: ${id}`);
      return getHandlerResponse(200, {
        success: 'No matching customer account was found',
      });
    }

    let updateResult = await addEcliqAccountsById(request.ecliqAccounts, id);
    if (updateResult?.modifiedCount != request.ecliqAccounts.length) {
      console.log(`Saving customer account number to collection failed: ${id} ${JSON.stringify(request.ecliqAccounts)}`);
      return getHandlerResponse(500, {
        error: 'Saving customer account number to collection failed',
      });
    }

    const policyUpdateResult = await updateCustomerPolicyWithEcliqAccountNumber(request.ecliqAccounts[0].ecliqAccountNumber, policyId);
    if (policyUpdateResult > 0) {
      console.log(`Successfully updated ${policyUpdateResult} policy(s).`);
    } else {
      console.log('No policies were updated.');
    }

    console.log(`Completed processing ${request.ecliqAccounts} for id ${id} and policyId ${policyId} in ${process.env.ENVIRONMENT_KEY}`);

    if (updateResult.modifiedCount > 0 && policyUpdateResult > 0) {
      return getHandlerResponse(200, { success: 'Completed processing', data: request.ecliqAccounts });
    }

    if (updateResult.modifiedCount > 0 && policyUpdateResult === 0) {
      console.log(`No customer account was found in ${process.env.ENVIRONMENT_KEY} with policyId: ${policyId}`);
      return getHandlerResponse(200, {
        success: 'Completed processing',
        accountData: {
          success: 'Completed processing',
          data: `Updated ecliqAccountNumber for customer account id ${id}`
        },
        policyData: `No matching customer policy was found for customer account ${policyId}`,
      });
    }

    return getHandlerResponse(200, {
      success: 'Completed processing',
      accountData: request.ecliqAccounts,
      policyData: policyId,
    });
  } catch (error) {
    console.error(error);
    return getHandlerResponse(500, { error: 'There was an error: ' + error.message });
  }
};