import { Collection, InsertManyResult, MongoClient, ObjectId, WithId } from 'mongodb';
import CustomerAccountModel, {
  BookAssessmentModel,
  CustomerMetadataMap,
  CustomerMetadataModel,
  CustomerPolicyModel,
  CustomerPolicyWithAccountDataModel,
  EcliqAccountModel,
  LibertyPolicyModel,
  AqeModel,
  PriorPolicyModel,
  SalesforceModel,
} from '../../../model/customerAccountModel';
import { decryptSecret } from '../utility/decryptSecret';
import { EventSource } from '../../../types/enums';
import Request, {
  CustomerMetadata,
  CustomerPolicy,
  CustomerPolicySearchCriteria,
  EcliqAccount,
} from '../../../types/request';
import { logger } from '../utility/logger';

// This is declared as global variable so that it can be used for warm start
let mongoClient: MongoClient;

export let customerAccountCollection: Collection<CustomerAccountModel>;

export let customerPolicyCollection: Collection<CustomerPolicyModel>;

export let customerAccountArchiveCollection: Collection<CustomerAccountModel>;

export const initializeMongo = async (connectionUrl: string = '') => {
  if (!mongoClient) {
    const mongoConnectionUrl = process.env.IS_LOCAL === 'true' ? connectionUrl : await decryptSecret(connectionUrl);
    console.log(`Connecting to MongoDB`);
    mongoClient = new MongoClient(mongoConnectionUrl);
  
    const db = mongoClient.db('BT');
    customerAccountCollection = db.collection('customerAccounts');
    customerPolicyCollection = db.collection('customerPolicies');
    customerAccountArchiveCollection = db.collection('customerAccountsArchive');
    console.log('Initialized new MongoClient');
  } else {
    console.log('Using existing MongoClient');
  }
};

export const findCustomerAccountById = async (id: string): Promise<WithId<CustomerAccountModel>[]> => {
  return await customerAccountCollection
    .find({
      _id: new ObjectId(id),
    })
    .toArray();
};

export const findCustomerPolicyById = async (id: string): Promise<WithId<CustomerPolicyWithAccountDataModel>[]> => {
  return (await customerPolicyCollection
  .aggregate<WithId<CustomerPolicyWithAccountDataModel>>([
    {
      $match: {
        _id: new ObjectId(id),
      }
    },
    {
      $lookup:
        {
          from: "customerAccounts",
          localField: "customerAccountId",
          foreignField: "_id",
          as: "customerAccounts"
        }
   }
 ])
    .toArray()) as WithId<CustomerPolicyWithAccountDataModel>[];
};

export const findExistingCustomerPoliciesByAccountId = async (
  accountId: ObjectId,
): Promise<WithId<CustomerPolicyModel>[]> => {
  return customerPolicyCollection.find({ customerAccountId: accountId }).toArray();
};

export const searchForAccountByEcliqAccountNumber = async (
  ecliqAccountNumber: string,
): Promise<WithId<CustomerAccountModel>[]> => {
  return customerAccountCollection
    .find({
      'ecliqAccounts.ecliqAccountNumber': ecliqAccountNumber,
    })
    .limit(1)
    .toArray();
};

export const searchForAccountByIdOrEcliqAccountNumberOrQuoteEntryId = async (
  id: string | undefined,
  ecliqAccountNumber: string | undefined,
  quoteEntryId: string | undefined,
): Promise<WithId<CustomerAccountModel>[]> => {
  let orCondition = [];

  if (id) {
    orCondition.push({ _id: new ObjectId(id) });
  }

  if (ecliqAccountNumber) {
    orCondition.push({ 'ecliqAccounts.ecliqAccountNumber': ecliqAccountNumber });
  }

  if (quoteEntryId) {
    orCondition.push({ 'customerMetadata.quoteEntryId': quoteEntryId });
  }

  if (orCondition.length === 0) {
    return [];
  }

  return customerAccountCollection
    .find({
      $or: orCondition
    })
    .limit(1)
    .sort({ lastUpdatedDate: -1 })
    .toArray();
}

export const searchForAccountByQuoteEntryId = (
  quoteEntryId: string,
): Promise<WithId<CustomerAccountModel>[]> => {
  return customerAccountCollection
    .find({
      'quoteEntryIds': quoteEntryId,
    })
    .limit(1)
    .toArray();
};


export const deleteCustomerAccountsAndPolicies = async (customerAccountIds: string[]) => {
  const customerAccountObjectIds = customerAccountIds.map(id => new ObjectId(id));

  // Delete customer policies associated with the customer accounts
  const deletePoliciesResult = await customerPolicyCollection.deleteMany({
      customerAccountId: { $in: customerAccountObjectIds }
  });
  console.log(`Deleted ${deletePoliciesResult.deletedCount} customer policy(ies)`);

  // Delete the customer accounts
  const deleteAccountsResult = await customerAccountCollection.deleteMany({
      _id: { $in: customerAccountObjectIds }
  });
  console.log(`Deleted ${deleteAccountsResult.deletedCount} customer account(s)`);

  return deleteAccountsResult.deletedCount;
};

export const searchForAccountByCustomerMetadata = (
  customerMetadata: CustomerMetadata,
): Promise<WithId<CustomerAccountModel>[]> => {

  const customerMetadataKeys = [
    'btCode',
    'customerName',
    'address1',
    'city',
    'state',
    'zip'
  ];
  
  // Build query to add and conditions for all the customer metadata fileds for each source and 
  // or condition for all sources to match the customer account by customer metadata from any source
  const query = {
    $or: Object.values(EventSource).map(source => {
      return {
        $and: customerMetadataKeys.map(key => {
          return {
            [`customerMetadataMap.${source}.customerMetadata.${key}`]: customerMetadata[key as keyof typeof customerMetadata]
          };
        })
      };
    })
  };

  return customerAccountCollection
  .find(query)
  .limit(1)
  .toArray();
};

export const searchForAccountByPriorCarrierPolicyNumberAndBtCode = (
  priorCarrierPolicyNumber: string,
  btCode: number,
): Promise<WithId<CustomerAccountModel>[]> => {
  return customerPolicyCollection
      .aggregate<WithId<CustomerAccountModel>>([
        {
          $match: {
            btCode: btCode,
            'priorPolicy.policyNumber': priorCarrierPolicyNumber,
          },
        },
        {
          $lookup: {
            from: 'customerAccounts',
            localField: 'customerAccountId',
            foreignField: '_id',
            as: 'customerAccounts',
          },
        },
        {
          $unwind: '$customerAccounts',
        },
        {
          $replaceRoot: {
            newRoot: '$customerAccounts',
          },
        },
      ])
      .toArray();
};

export const searchForExistingCustomerPolicy = async (
  searchCriteria: CustomerPolicySearchCriteria,
): Promise<WithId<CustomerPolicyModel> | null> => {
  return customerPolicyCollection.findOne({
    _id: new ObjectId(searchCriteria.customerPolicyId!),
  });
};

export const addEcliqAccountsById = (ecliqAccounts: EcliqAccount[], id: string) => {
  return customerAccountCollection.bulkWrite(
    ecliqAccounts.map((ecliqAccount) => ({
      updateOne: {
        filter: { _id: new ObjectId(id) },
        update: {
          $addToSet: {
            ecliqAccounts: ecliqAccountModel(ecliqAccount),
          },
        },
      },
    })),
  );
};

export const updateCustomerPolicyWithEcliqAccountNumber = async (
    ecliqAccountNumber: string,
    policyId: string
): Promise<number> => {
  const result = await customerPolicyCollection.updateOne(
      {_id: new ObjectId(policyId)},
      {
        $set: {
          ecliqAccountNumber: ecliqAccountNumber,
          lastUpdatedDate: new Date(Date.now()),
          lastPolicyEvent: {
            policyEventDate: new Date(Date.now()),
            policyEventType: EventSource.SALESFORCE,
          }
        }
      }
  );

  logger.info(`Matched ${result.matchedCount} policy(s)`);
  logger.info(`Updated ${result.modifiedCount} policy(s)`);
  return result.modifiedCount;
};

export const insertNewCustomerAccount = async (request: Request): Promise<(CustomerAccountModel | CustomerPolicyModel)[]> => {
  const currentDate: Date = new Date(Date.now());
  const customerMetadata  = request.customerMetadata!;
  const insertedObjects: (CustomerAccountModel | CustomerPolicyModel)[] = [];
  // save the CustomerAccount
  const eventSourceString = EventSource[request.eventSource as keyof typeof EventSource];
  const customerMetadataMap: CustomerMetadataMap<string, CustomerMetadataModel> = {
    [eventSourceString]: buildCustomerMetadataModel(customerMetadata, currentDate),
  };

  const newCustomerAccountModel: CustomerAccountModel = {
    customerMetadataMap: customerMetadataMap,
    ecliqAccounts: (request.ecliqAccounts || []).filter(account => account.ecliqAccountNumber !== '').map((account) => ecliqAccountModel(account)),
    createdDate: currentDate,
    lastUpdatedDate: currentDate,
  };
  setConcatenatedKeys(customerMetadata, eventSourceString, newCustomerAccountModel);
  if (request.searchCriteria?.customerAccountId) {
    newCustomerAccountModel._id = new ObjectId(request.searchCriteria?.customerAccountId);
  }
  addSalesforceData(eventSourceString, newCustomerAccountModel, request);
  const customerAccountInsertResult = await customerAccountCollection.insertOne(newCustomerAccountModel);
  newCustomerAccountModel._id = customerAccountInsertResult.insertedId;
  insertedObjects.push(newCustomerAccountModel);
  // save the CustomerPolicies, There won't be any customer policies for SALESFORCE quote entry events
  if(request.customerPolicies && request.customerPolicies.length > 0) {
    const insertedCustomerPolicies = await addNewCustomerPoliciesToCustomerAccount(
      request.customerPolicies,
      newCustomerAccountModel as WithId<CustomerAccountModel>,
      currentDate,
      eventSourceString
    );
    insertedObjects.push(...insertedCustomerPolicies);
  }

  return insertedObjects;
};

export const insertNewCustomerAccountForNewBtCode = async (request: Request): Promise<CustomerAccountModel> =>{
  const currentDate: Date = new Date(Date.now());
  const customerMetadata = request.customerMetadata!;
  const insertedObjects: (CustomerAccountModel | CustomerPolicyModel)[] = [];
  // save the CustomerAccount
  const eventSourceString = EventSource[request.eventSource as keyof typeof EventSource];
  const customerMetadataMap: CustomerMetadataMap<string, CustomerMetadataModel> = {
    [eventSourceString]: buildCustomerMetadataModel(customerMetadata, currentDate),
  };
  const newCustomerAccountModel: CustomerAccountModel = {
    customerMetadataMap: customerMetadataMap,
    ecliqAccounts: (request.ecliqAccounts || []).map((account) => ecliqAccountModel(account)),
    createdDate: currentDate,
    lastUpdatedDate: currentDate,
  };

  setConcatenatedKeys(customerMetadata, eventSourceString, newCustomerAccountModel);
  addSalesforceData(eventSourceString, newCustomerAccountModel, request);
  const customerAccountInsertResult = await customerAccountCollection.insertOne(newCustomerAccountModel);
  newCustomerAccountModel._id = customerAccountInsertResult.insertedId;

  return newCustomerAccountModel;
};

export const archiveAndDeleteCustomerAccount = async (customerAccountId: string) => {
  const custAccountId = new ObjectId(customerAccountId)
  const existingCustomerPolicies = await customerPolicyCollection.find({ customerAccountId: custAccountId}).toArray();
  if((existingCustomerPolicies).length == 0){
    const existingAccount = await findCustomerAccountById(customerAccountId)
    const customerAccountArchiveInsertResult = await customerAccountArchiveCollection.insertOne(existingAccount[0]);
    const result = await customerAccountCollection.deleteOne({
      _id: custAccountId})
  }
}

export const updateCustomerAccount = async (request: Request, existingAccount: WithId<CustomerAccountModel>): Promise<WithId<CustomerAccountModel>> => {
  const currentDate: Date = new Date(Date.now());
  let customerMetadataMap: CustomerMetadataMap<string, CustomerMetadataModel> = existingAccount.customerMetadataMap ?? {};
  const eventSourceString = EventSource[request.eventSource as keyof typeof EventSource];
  if (request.customerMetadata) {
    let customerMetadataModel: CustomerMetadataModel = buildCustomerMetadataModel(request.customerMetadata, currentDate);
  
    customerMetadataMap[eventSourceString] !== undefined
      ? customerMetadataModel.createdDate = customerMetadataMap[eventSourceString]?.createdDate ?? currentDate 
      : customerMetadataModel.createdDate = currentDate;
    customerMetadataMap[eventSourceString] = customerMetadataModel;
  }

  // Update salesforceKey, aqeKey, batKey and ecliqKey
  setConcatenatedKeys(request.customerMetadata, eventSourceString, existingAccount);
  // Update quoteEntryIds and quoterComments
  let quoteEntryIds: string[] = existingAccount.quoteEntryIds ?? [];
  let quoterComments: string = existingAccount.quoterComments ?? '';
  
  if(eventSourceString === EventSource.SALESFORCE) {
    quoterComments = request.quoterComments ?? quoterComments;
    if (request.quoteEntryId) {
      quoteEntryIds = [...new Set([...quoteEntryIds, request.quoteEntryId])];
    }
  }
  

  // Update ecliqAccounts
  let ecliqAccounts: EcliqAccountModel[] = existingAccount.ecliqAccounts ?? [];
    if (request.ecliqAccounts) {
        request.ecliqAccounts.forEach((account) => {
            if (account.ecliqAccountNumber !== '' && !ecliqAccounts.some(existingEcliqAccount => existingEcliqAccount.ecliqAccountNumber === account.ecliqAccountNumber)) {
                ecliqAccounts.push(ecliqAccountModel(account));
            }
        });
    }

  await customerAccountCollection.updateOne(
    { _id: existingAccount._id },
    {
      $set: {
        customerMetadataMap: customerMetadataMap,
        ecliqAccounts: ecliqAccounts,
        lastUpdatedDate: currentDate,
        quoteEntryIds: quoteEntryIds,
        quoterComments: quoterComments,
        salesforceCustomerMetadataKey: existingAccount.salesforceCustomerMetadataKey,
        aqeCustomerMetadataKey: existingAccount.aqeCustomerMetadataKey,
        batCustomerMetadataKey: existingAccount.batCustomerMetadataKey,
        ecliqCustomerMetadataKey: existingAccount.ecliqCustomerMetadataKey,
      },
    },
  );

  return existingAccount;
}

export const addNewCustomerPoliciesToCustomerAccount = async (
  customerPolicies: CustomerPolicy[],
  existingCustomerAccountId: WithId<CustomerAccountModel>,
  currentDate: Date,
  eventSource: EventSource,
): Promise<CustomerPolicyModel[]> => {
  const eventSourceString = EventSource[eventSource as keyof typeof EventSource];
  const policiesToInsert = customerPolicies.map((policy) => {
    if (policy.btCode && Number(policy.btCode) !== existingCustomerAccountId.customerMetadataMap[eventSourceString]?.customerMetadata.btCode) {
      console.warn(
        `CustomerPolicy btCode ${policy.btCode} does not match CustomerAccount ` +
          `customerMetadata.btCode ${existingCustomerAccountId.customerMetadataMap[eventSourceString]?.customerMetadata.btCode}`,
      );
    }

    return customerPolicyModel(policy, existingCustomerAccountId._id, currentDate, eventSource);
  });
  if(policiesToInsert.length === 0) {
    return [];
  }
  const newCustomerPolicies: InsertManyResult<CustomerPolicyModel> = await customerPolicyCollection.insertMany(
    policiesToInsert,
  );

  Object.values(newCustomerPolicies.insertedIds).forEach((id, index) => (policiesToInsert[index]._id = id));

  return policiesToInsert;
};

export const updateExistingFieldsOnToCustomerPolicy = async (
  sourceCustomerPolicy: CustomerPolicy,
  targetCustomerPolicy: WithId<CustomerPolicyModel>,
  eventSource: EventSource
) => {
  movePresentFields(sourceCustomerPolicy, targetCustomerPolicy);
  
  const currentDate = new Date(Date.now());

  if (eventSource === EventSource.BAT) {
    targetCustomerPolicy.bookAssessment = targetCustomerPolicy.bookAssessment || {};
    targetCustomerPolicy.bookAssessment.lastUpdatedDate = currentDate;
  }
  targetCustomerPolicy.lastUpdatedDate = currentDate;
  targetCustomerPolicy.lastPolicyEvent = {
    policyEventDate: currentDate,
    policyEventType: eventSource
  }
  await customerPolicyCollection.updateOne({ _id: targetCustomerPolicy._id }, { $set: targetCustomerPolicy });
};

export const updateCustomerPolicy = async (
  customerPolicy: CustomerPolicyModel
): Promise<CustomerPolicyModel> => {
  const result = await customerPolicyCollection.updateOne({ _id: customerPolicy._id }, { $set: customerPolicy });
  logger.info(`Matched ${result.matchedCount} policy(s)`);
  logger.info(`Updated ${result.modifiedCount} policy(s)`);
  return customerPolicy;
}

export const updateAddress2 = (address2: string, id: ObjectId) => {
  const currentDate: Date = new Date(Date.now());

  return customerAccountCollection.updateOne(
    { _id: id },
    {
      $set: {
        'customerMetadata.address2': address2,
        'customerMetadata.addressLastUpdatedDate': currentDate,
        lastUpdatedDate: currentDate,
      },
    },
  );
};

const customerPolicyModel = (
  customerPolicy: CustomerPolicy,
  customerAccountId: ObjectId,
  currentDate: Date,
  eventSource: EventSource
): CustomerPolicyModel => {
  const priorPolicy: PriorPolicyModel =  eventSource !== EventSource.ECLIQ ? {
    lineOfBusiness: customerPolicy.priorPolicy?.lineOfBusiness,
    carrier: customerPolicy.priorPolicy?.carrier,
    policyNumber: customerPolicy.priorPolicy?.policyNumber,
    lastUpdatedSource: eventSource
  }: {};

  // Only update libertyPolicy if the event source is ECLIQ
  const libertyPolicy: LibertyPolicyModel = eventSource === EventSource.ECLIQ ? {
    policyNumber: customerPolicy.libertyPolicy?.policyNumber,
    effectiveDate: customerPolicy.libertyPolicy?.effectiveDate,
    premium: Number(customerPolicy.libertyPolicy?.premium),
    producer: customerPolicy.libertyPolicy?.producer,
    status: customerPolicy.libertyPolicy?.status,
    lineOfBusiness: customerPolicy.libertyPolicy?.lineOfBusiness,
    ecliqId: customerPolicy.libertyPolicy?.ecliqId,
  } : {};

  const bookAssessment: BookAssessmentModel = eventSource === EventSource.BAT ? {
    assessmentId: customerPolicy.bookAssessment?.assessmentId,
    finalAppetiteDecision: customerPolicy.bookAssessment?.finalAppetiteDecision,
    priorExpirationDate: customerPolicy.bookAssessment?.priorExpirationDate,
    priorPremium: Number(customerPolicy.bookAssessment?.priorPremium),
    lastUpdatedDate: currentDate,
    createdDate: currentDate
  } : {};

  const aqe: AqeModel = eventSource === EventSource.AQE ? {
    priorExpirationDate: customerPolicy.aqe?.priorExpirationDate,
    priorPremium: Number(customerPolicy.aqe?.priorPremium),
    status: customerPolicy.aqe?.status,
    businessType: customerPolicy.aqe?.businessType,
    opportunityId: customerPolicy.aqe?.opportunityId,
    nNumber: customerPolicy.aqe?.nNumber,
    lastUpdatedDate: currentDate,
    createdDate: currentDate
  } : {};
  const salesforce: SalesforceModel = eventSource === EventSource.SALESFORCE ? {
    priorExpirationDate: customerPolicy.salesforce?.priorExpirationDate,
    priorPremium: Number(customerPolicy.salesforce?.priorPremium),
    policyEffectiveDate: customerPolicy.salesforce?.policyEffectiveDate,
    policyDetailsId: customerPolicy.salesforce?.policyDetailsId,
    quoteEntryId: customerPolicy.salesforce?.quoteEntryId,
    isManualQuote: customerPolicy.salesforce?.isManualQuote,
    createdDate: currentDate,
    lastUpdatedDate: currentDate
  } : {};
  return {
    btCode: Number(customerPolicy.btCode),
    emailAddress: customerPolicy.emailAddress,
    phoneNumber: customerPolicy.phoneNumber,
    ratingState: customerPolicy.ratingState,
    customerAccountId: customerAccountId,
    ecliqAccountNumber: customerPolicy.ecliqAccountNumber,
    priorPolicy: priorPolicy,
    libertyPolicy: libertyPolicy,
    aqe: aqe,
    salesforce: salesforce,
    bookAssessment: bookAssessment,
    isUpdatedByEcliq: eventSource === EventSource.ECLIQ,
    transactionId: customerPolicy.transactionId,
    // NOTE: passing in the date instead of creating it here so that customerPolicies
    // created in the same request will have identical createdDates
    createdDate: currentDate,
    lastUpdatedDate: currentDate,
    lastPolicyEvent: {
      policyEventDate: currentDate,
      policyEventType: eventSource
    }
  };
};

const ecliqAccountModel = (ecliqAccount: EcliqAccount): EcliqAccountModel => {

  return {
    ecliqAccountNumber: ecliqAccount.ecliqAccountNumber,
    ecliqAccountCreationDate: ecliqAccount.ecliqAccountCreationDate
        ? ecliqAccount.ecliqAccountCreationDate
        : new Date(Date.now()), // default to current date if no date provided
  };
};

// exported for testing only
export const movePresentFields = (source: any, target: any) => {
  for (const key in source) {
    if (source[key] !== undefined) {
      if (typeof source[key] === 'object' && source[key] !== null) {
        if (typeof target[key] !== 'object' || target[key] === null) {
          target[key] = Array.isArray(source[key]) ? [] : {};
        }
        movePresentFields(source[key], target[key]);
      } else {
        target[key] = source[key];
      }
    }
  }
};

/**
 * Set the concatenated keys of customer metadata for the customer account model based on the event source. 
 * We have unique index on these fields to avoid duplicate customer accounts being created.
 * @param customerMetadata 
 * @param eventSourceString 
 * @param newCustomerAccountModel 
 */
const setConcatenatedKeys = (customerMetadata: CustomerMetadata|undefined, eventSourceString: EventSource, newCustomerAccountModel: CustomerAccountModel) => {
  const concatenatedMetadata = customerMetadata?.btCode + '-' + customerMetadata?.customerName + '-' + customerMetadata?.address1
    + '-' + customerMetadata?.city + '-' + customerMetadata?.state + '-' + customerMetadata?.zip;

  switch (eventSourceString) {
    case EventSource.SALESFORCE:
      newCustomerAccountModel.salesforceCustomerMetadataKey = concatenatedMetadata;
      break;
    case EventSource.AQE:
      newCustomerAccountModel.aqeCustomerMetadataKey = concatenatedMetadata;
      break;
    case EventSource.BAT:
      newCustomerAccountModel.batCustomerMetadataKey = concatenatedMetadata;
      break;
    case EventSource.ECLIQ:
      newCustomerAccountModel.ecliqCustomerMetadataKey = concatenatedMetadata;
      break;
    case EventSource.PERFTEST:
        newCustomerAccountModel.perfTestCustomerMetadataKey = concatenatedMetadata;
        break;
    default:
      logger.error(`Invalid event source: ${eventSourceString}`);
  }
}

const addSalesforceData = (eventSourceString: EventSource, newCustomerAccountModel: CustomerAccountModel, request: Request) => {
  if (eventSourceString === EventSource.SALESFORCE) {
    newCustomerAccountModel.quoteEntryIds = request.quoteEntryId ? [request.quoteEntryId] : [];
    newCustomerAccountModel.quoterComments = request.quoterComments ?? '';
  }
}

const buildCustomerMetadataModel = (customerMetadata: CustomerMetadata, currentDate: Date): CustomerMetadataModel => {
  return {
    customerMetadata: {
      btCode: Number(customerMetadata?.btCode),
      customerName: customerMetadata?.customerName,
      address1: customerMetadata?.address1,
      address2: customerMetadata?.address2 ?? '',
      city: customerMetadata?.city,
      state: customerMetadata?.state,
      zip: customerMetadata?.zip,
      addressLastUpdatedDate: currentDate,
    } as CustomerMetadata,
    lastUpdatedDate: currentDate,
    createdDate: currentDate,
  } as CustomerMetadataModel;
}