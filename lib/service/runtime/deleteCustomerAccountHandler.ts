import {Context, APIGatewayProxyResult} from 'aws-lambda';
import {getHandlerResponse} from '../utility/getHandlerResponse';
import {
    deleteCustomerAccountsAndPolicies,
    initializeMongo
} from './mongo';

export const deleteCustomerAccountLambda = async (event: any, context: Context): Promise<APIGatewayProxyResult> => {
    if (!event.headers || event.headers['Content-Type'] !== 'application/json') {
        return getHandlerResponse(400, { error: 'Invalid Content-Type, expected application/json' });
    }
    let customerAccountIds: string[];

    try {
        const body = JSON.parse(event.body);
        customerAccountIds = body.customerIds;
        if (!Array.isArray(customerAccountIds)) {
            throw new Error('Invalid input');
        }
    } catch (error) {
        console.log(error);
        return getHandlerResponse(400, { error: 'Invalid JSON or input format for deleting customerAccountIds' });
    }

    if (customerAccountIds.length === 0) {
        console.log(`Missing customer account ids in request body ${process.env.ENVIRONMENT_KEY}`);
        return getHandlerResponse(400, { error: 'Missing customer account ids in request body' });
    }

    console.log('Initializing MongoDB connection...');
    try {
        await initializeMongo(process.env.MONGODB_CONNECTION);
    } catch (error) {
        console.log('Error initializing MongoDB connection:', error);
        return getHandlerResponse(500, { error: 'Internal Server Error' });
    }

    const searchResults = await deleteCustomerAccountsAndPolicies(customerAccountIds);

    if (searchResults === 0) {
        console.log(`No customer accounts were found with provided ids`);
        return getHandlerResponse(404, { error: `No customer accounts were found with provided ids` });
    }
    return getHandlerResponse(200, { success: 'Customer account(s) ' + searchResults + ' deleted successfully' });
};