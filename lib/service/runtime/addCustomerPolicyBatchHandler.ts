import { Context } from 'aws-lambda';
import * as AWS from 'aws-sdk';
import { captureAWS } from 'aws-xray-sdk';
import Request, { CustomerMetadata, CustomerAccountSearchCriteria } from '../../../types/request';
import { getHandlerResponse } from '../utility/getHandlerResponse';
import { AddressHelper, pipeDelimitedAddress } from '../utility/addressHelper';
import { EmailHelper } from '../utility/emailHelper';
import { FeatureService } from '../utility/featureService';
import { LineOfBusiness } from '../../../model/lob';
import { EventSource } from '../../../types/enums';
import { removeNonNumericChars, removeSpecialChars } from '../utility/regex';
import { Logger } from '@aws-lambda-powertools/logger';
import { Tracer } from '@aws-lambda-powertools/tracer';
import { Metrics } from '@aws-lambda-powertools/metrics';
import type { LambdaInterface } from '@aws-lambda-powertools/commons/types';
import { AuditLogHelper } from "../utility/auditLogHelper";
import { TransactionStatus, TransactionEventRequest } from "@lmig/audit-log-node-starter";

const serviceName = 'customerAccountBatchService';
const logger = new Logger({ serviceName });
const tracer = new Tracer({ serviceName });
const metrics = new Metrics({
  namespace: 'BookTransfer',
  serviceName: serviceName,
});

const AWSXRay = captureAWS(AWS);

const lambda = new AWSXRay.Lambda({
  region: 'us-east-1',
  endpoint: `https://lambda.us-east-1.amazonaws.com`,
});

class AddCustomerPolicyBatchHandler implements LambdaInterface {

  private auditLogHelper!: AuditLogHelper;

  public async initializeHelpers() {
    try {
      this.auditLogHelper = await AuditLogHelper.init();
      logger.info('AuditLogHelper initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize AuditLogHelper', error);
    }
  }

  @logger.injectLambdaContext({ logEvent: false })
  // flushes (publishes) any metrics that have been recorded
  @metrics.logMetrics({ captureColdStartMetric: true })
  @tracer.captureLambdaHandler({ captureResponse: false }) // capture response is false to avoid logging sensitive data
  public async handler(event: any, context: Context) {
    tracer.putAnnotation('awsRequestId', context.awsRequestId);
    const transactionEventRequests: TransactionEventRequest[] = [];

    let requests: Request[] = this.parseEventBody(event.body);
    await this.initializeHelpers();
    if (requests.length === 0) {
      return getHandlerResponse(400, {
        error: 'Invalid request',
      });
    }
    let custBatchEventRequest: TransactionEventRequest | null = null;

    if(requests[0].transactionId !== null && requests[0].transactionId !== undefined) {
      custBatchEventRequest = this.auditLogHelper.buildTransactionEventRequest(
          "CUSTOMER_BATCH_REQUEST_RECEIVED",
          TransactionStatus.SUCCESS,
          new Date(),
          { "origin": "addcustomerPolicyBatchHandler"}
      );
    }

    if (custBatchEventRequest) {
      transactionEventRequests.push(custBatchEventRequest);
    }
    logger.info(`Validating ${requests.length} requests`);

    //validate request and sanitize
    let response;
    let count = 0;
    const addressRequestMap = new Map<number, Request>();
    let emailRequests: Request[] = [];
    let validatedrequests = new Map<Request, any>();
    let validateEventRequest = this.auditLogHelper.buildTransactionEventRequest(
      "VALIDATE_CUSTOMER_METADATA",
      TransactionStatus.IN_PROGRESS,
      new Date(),
      { "origin": "addcustomerPolicyBatchHandler"}
    );
    requests.forEach((request) => {
      response = this.validateCustomerMetadata(request);
      if (response == null) {
        this.sanitizeRequest(request);
        addressRequestMap.set(count, request);
      } else {
        logger.info(`${response.statusCode} Error: ${response.error}`);
        emailRequests.push(request);
      }
      //This will be used to skip invoking the lambda if the customer metadata is invalid
      validatedrequests.set(request, response);
      count++;
    });
    updateEvent(validateEventRequest);
  
    const featureNames = ['addressCleanse'];
    const identifier = 'customeraccountservice';
    const isFeatureLaunched = await FeatureService.checkFeatureLaunch(featureNames, identifier);

    logger.info(`Address cleanse flag is:' ${isFeatureLaunched}`);

    if (isFeatureLaunched) {
      let addressCleanseEventRequest = this.auditLogHelper.buildTransactionEventRequest(
        "CUSTOMER_BATCH_REQUEST_ADDRESS_CLEANSE",
        TransactionStatus.IN_PROGRESS,
        new Date(),
        { "origin": "addcustomerPolicyBatchHandler"}
    );
      await this.batchCleanseAddresses(addressRequestMap, requests);
      updateEvent(addressCleanseEventRequest);
    } else {
      logger.info('Feature not launched: Skipping address cleanse');
    }
    logger.info(`Processing ${requests.length} requests`);

    let custBulkEventRequest = this.auditLogHelper.buildTransactionEventRequest(
        "PROCESS_INDIVIDUAL_CUSTOMER_REQUEST",
        TransactionStatus.IN_PROGRESS,
        new Date(),
        { "origin": "addcustomerPolicyBatchHandler"}
    );

    const results = await Promise.all(requests.map((request) => {
      const validationError = validatedrequests.get(request);
      if (validationError) {
        logger.info("Skipping lambda invocation for invalid customer metadata");
        return Promise.resolve({
          Payload: JSON.stringify({
            statusCode: 200,
            body: JSON.stringify({ success: 'Completed processing', data: validationError })
          })
        } as AWS.Lambda.InvocationResponse);
      }
      
      return this.invokeLambda(request);
    }));

    // Process the results
    const responses = results.map((result) => {
      const payload = JSON.parse(result.Payload as string);
      const responseBody = JSON.parse(payload.body);
      return responseBody.data;
    });

    if(emailRequests.length > 0){
      logger.info("Sending invalid customer metadata email")
      const emailRequestBody = EmailHelper.buildEmailRequest(emailRequests, `[${process.env.ENVIRONMENT_KEY}]: Add Customer Account - Invalid Customer Metadata`)
      const emailServiceUrl = process.env.EMAIL_SERVICE_URL!;
      const response = await EmailHelper.sendEmail(emailServiceUrl, emailRequestBody);
      if(response.status === 200){
        logger.info("Email sent");
      }else{
        logger.info("Failed to send email");
      }
    }
  
    updateEvent(custBulkEventRequest);
    // Get the current X-Ray segment
    const segment = tracer.getSegment();
    const traceId = segment ? segment.id : 'No trace ID';

    try {
      if (requests[0]?.transactionId) {
        const transactionId = requests[0]?.transactionId;
        logger.info(`Transaction id for auditLog ${transactionId}`);

        const endTransactionRequest = this.auditLogHelper.buildEndTransactionRequest(
            TransactionStatus.SUCCESS,
            {
              "origin": "addcustomerPolicyBatchHandler"
            });
        endTransactionRequest.events = transactionEventRequests;
        const endTransactionResponse = await this.auditLogHelper.endTransaction(
            transactionId,
            endTransactionRequest
        );
        logger.info(`Transaction ended successfully for transactionId ${transactionId}: ${endTransactionResponse}`);
      } else {
        logger.warn('Transaction ID is missing in the first request');
      }
    } catch (error) {
      logger.error(`Audit log transaction failed to end: ${error instanceof Error ? error.message : error}`);
    }
    // Log the trace ID
    return getHandlerResponse(200, {
      success: 'Completed processing',
      data: responses,
    });

    function updateEvent(event: TransactionEventRequest) {
      event.status = TransactionStatus.SUCCESS;
      event.endTimeStamp = new Date();
      transactionEventRequests.push(event);
    }
  }

  @tracer.captureMethod({ subSegmentName: 'invokeAddCustomerPolicyLambda' })
  public async invokeLambda(request: Request): Promise<AWS.Lambda.InvocationResponse> {
    const payload = JSON.stringify(request);
    return lambda
      .invoke({
        FunctionName: process.env.ADD_CUSTOMER_POLICY_LAMBDA_NAME!,
        InvocationType: 'RequestResponse',
        Payload: payload,
        Qualifier: 'latest',
      })
      .promise();
  }

  public parseEventBody(body: string | null): Request[] {
    try {
      return body?.startsWith('[') ? JSON.parse(body) : [JSON.parse(body || '{}')];
    } catch (error) {
      logger.error(error);
      return [];
    }
  }

  @tracer.captureMethod({ subSegmentName: 'batchCleanseAddresses' })
  public async batchCleanseAddresses(addressRequestMap: Map<number, Request>, requests: Request[]) {
    if (addressRequestMap.size > 0) {
      const addresses: { [key: string]: CustomerMetadata } = {};
      addressRequestMap.forEach((request, key) => {
        if (request.customerMetadata) {
          addresses[pipeDelimitedAddress(request.customerMetadata)] = request.customerMetadata;
        }
        if (request.searchCriteria?.customerMetadata) {
          addresses[pipeDelimitedAddress(request.searchCriteria.customerMetadata)] =
            request.searchCriteria.customerMetadata;
        }
      });
      // cleanse
      const cleansedAddressMap = await AddressHelper.bulkCleanseAddresses(addresses);

      let originalRequest: Request;
      addressRequestMap.forEach((request, key) => {
        originalRequest = requests[key];
        if (originalRequest.customerMetadata) {
          AddressHelper.assignAddressDetailsToCustomerMetadata(
            originalRequest.customerMetadata,
            cleansedAddressMap[pipeDelimitedAddress(originalRequest.customerMetadata)],
          );
        }
        if (originalRequest.searchCriteria?.customerMetadata) {
          AddressHelper.assignAddressDetailsToCustomerMetadata(
            originalRequest.searchCriteria.customerMetadata,
            cleansedAddressMap[pipeDelimitedAddress(originalRequest.searchCriteria.customerMetadata)],
          );
        }
      });
    }
  }

  public validateCustomerMetadata(request: Request) {
    if (!request.searchCriteria) {
      return { statusCode: 400, error: 'Invalid request' };
    }
    
    if (request.searchCriteria && request.searchCriteria?.customerMetadata) {
      if (!this.hasValidCustomerMetadata(request.searchCriteria.customerMetadata)) {
        return { statusCode: 400, error: 'Invalid customerMetadata' };
      }
    }

    if (request.customerMetadata) {
      if (!this.hasValidCustomerMetadata(request.customerMetadata)) {
        return { statusCode: 400, error: 'Invalid customerMetadata' };
      }
    }
    return null;
  }

  public hasValidCustomerMetadata(customerMetadata: CustomerMetadata): boolean {
    const fields = [
      { name: 'btCode', value: customerMetadata?.btCode },
      { name: 'customerName', value: customerMetadata?.customerName },
      { name: 'address1', value: customerMetadata?.address1 },
      { name: 'city', value: customerMetadata?.city },
      { name: 'state', value: customerMetadata?.state },
      { name: 'zip', value: customerMetadata?.zip },
    ];

    let valid = true;

    fields.forEach((field) => {
      if (typeof field.value === 'string' && field.value === '') {
        logger.info(`Field ${field.name} is empty`);
        valid = false;
      } else if (field.value === undefined || field.value === null) {
        logger.info(`Field ${field.name} is ${field.value}`);
        valid = false;
      }
    });

    return valid;
  }

  public sanitizeRequest(request: Request) {
    // request.customerMetadata && request.searchCriteria.customerMetadata
    this.sanitizeCustomerMetadata(request);
    // all btCodes
    this.sanitizeBtCodes(request);
  }

  public sanitizeBtCodes(request: Request) {
    // request.searchCriteria.btCode
    if (request.searchCriteria?.btCode && typeof request.searchCriteria.btCode === 'string') {
      request.searchCriteria.btCode = parseInt(removeNonNumericChars(request.searchCriteria.btCode));
    }
    // request.searchCriteria.customerMetadata.btCode
    if (
      request.searchCriteria?.customerMetadata?.btCode &&
      typeof request.searchCriteria.customerMetadata?.btCode === 'string'
    ) {
      request.searchCriteria.customerMetadata.btCode = parseInt(
        removeNonNumericChars(request.searchCriteria.customerMetadata.btCode),
      );
    }
    // request.customerMetadata.btCode
    if (request.customerMetadata?.btCode && typeof request.customerMetadata?.btCode === 'string') {
      request.customerMetadata.btCode = parseInt(removeNonNumericChars(request.customerMetadata.btCode));
    }
    // request.customerPolicies[].btCode
    request.customerPolicies?.forEach((customerPolicy) => {
      if (customerPolicy.btCode && typeof customerPolicy.btCode === 'string') {
        customerPolicy.btCode = parseInt(removeNonNumericChars(customerPolicy.btCode));
      }

      if (customerPolicy.priorPolicy && customerPolicy.priorPolicy.lineOfBusiness) {
        customerPolicy.priorPolicy.lineOfBusiness = LineOfBusiness.fromString(
          customerPolicy.priorPolicy.lineOfBusiness,
          EventSource[request.eventSource as keyof typeof EventSource],
        );
      }
    });
  }

  public sanitizeCustomerMetadata(request: Request) {
    if (request.customerMetadata) {
      this.sanitizeMetadata(request.customerMetadata);
    }

    if (request.searchCriteria?.customerMetadata) {
      this.sanitizeMetadata(request.searchCriteria.customerMetadata);
    }
  }

  public sanitizeMetadata(customerMetadata: CustomerMetadata) {
    if (!customerMetadata) {
      throw new Error('customerMetadata is undefined');
    }

    if (customerMetadata.customerName) {
      // Remove special characters and convert to uppercase(Generalizing the customer name)
      customerMetadata.customerName = removeSpecialChars(customerMetadata.customerName).toUpperCase();
    }

    customerMetadata.address1 = removeSpecialChars(customerMetadata.address1);
    customerMetadata.address2 = removeSpecialChars(customerMetadata.address2 ?? '');
    customerMetadata.city = removeSpecialChars(customerMetadata.city);
    customerMetadata.state = removeSpecialChars(customerMetadata.state);
    customerMetadata.zip = removeNonNumericChars(customerMetadata.zip);
  }

  //AQE Move Opportunities changes
  public isNotBtCodeMatching(searchCriteria: CustomerAccountSearchCriteria) {
    return (
      searchCriteria.btCode &&
      searchCriteria.customerMetadata &&
      searchCriteria.customerMetadata.btCode &&
      searchCriteria.btCode !== searchCriteria.customerMetadata.btCode
    );
  }
}

const handlerClass = new AddCustomerPolicyBatchHandler();
const addCustomerPolicyBatchLambda = handlerClass.handler.bind(handlerClass);
export { addCustomerPolicyBatchLambda };
