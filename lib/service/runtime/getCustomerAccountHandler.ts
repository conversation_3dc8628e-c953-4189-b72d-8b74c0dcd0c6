import { APIGatewayProxyResult, Context } from 'aws-lambda';
import { getHandlerResponse } from '../utility/getHandlerResponse';
import { findCustomerAccountById, initializeMongo } from './mongo';

export const getCustomerAccountLambda = async (event: any, context: Context): Promise<APIGatewayProxyResult> => {

  try {
    const accountId = event.pathParameters?.accountId;

    if (!accountId) {
      console.log(`No accountId was provided in ${process.env.ENVIRONMENT_KEY}`);
      return getHandlerResponse(400, { error: 'Missing accountId in pathParameters' });
    }

    console.log('Initializing MongoDB connection...');
    await initializeMongo(process.env.MONGODB_CONNECTION);

    const foundCustomerAccounts = await findCustomerAccountById(accountId);

    if (foundCustomerAccounts.length === 0) {
      console.log(`No customer account was found with accountId: ${accountId}`);
      return getHandlerResponse(404, '');
    }

    return getHandlerResponse(200, foundCustomerAccounts[0]);
  } catch (error) {
    return getHandlerResponse(500, { error: 'There was an error: ' + error.message });
  }
};
