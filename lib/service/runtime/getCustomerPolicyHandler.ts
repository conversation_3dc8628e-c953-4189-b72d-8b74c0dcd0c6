import { APIGatewayProxyResult, Context } from 'aws-lambda';
import { getHandlerResponse } from '../utility/getHandlerResponse';
import { findCustomerAccountById, findCustomerPolicyById, initializeMongo } from './mongo';

export const getCustomerPolicyLambda = async (event: any, context: Context): Promise<APIGatewayProxyResult> => {

  try {
    const policyId = event.pathParameters?.policyId;

    if (!policyId) {
      console.log(`No policyId was provided in ${process.env.ENVIRONMENT_KEY}`);
      return getHandlerResponse(400, { error: 'Missing policyId in pathParameters' });
    }

    console.log('Initializing MongoDB connection...');
    await initializeMongo(process.env.MONGODB_CONNECTION);

    const foundCustomerPolicies = await findCustomerPolicyById(policyId);

    if (foundCustomerPolicies.length === 0) {
      console.log(`No customer policy was found with policyId: ${policyId}`);
      return getHandlerResponse(404, '');
    }

    const foundCustomerPolicy = foundCustomerPolicies[0];
    const customerAccount = foundCustomerPolicy.customerAccounts ? foundCustomerPolicy.customerAccounts[0] : undefined;
    foundCustomerPolicy.customerAccounts = undefined;

    return getHandlerResponse(200, {customerAccount: customerAccount, customerPolicy: foundCustomerPolicy});
  } catch (error) {
    return getHandlerResponse(500, { error: 'There was an error: ' + error.message });
  }
};
