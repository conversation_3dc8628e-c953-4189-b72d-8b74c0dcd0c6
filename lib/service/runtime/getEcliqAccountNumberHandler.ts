import { Context, APIGatewayProxyResult } from 'aws-lambda';
import { getHandlerResponse } from '../utility/getHandlerResponse';
import { initializeMongo, searchForAccountByPriorCarrierPolicyNumberAndBtCode } from './mongo';
import { removeNonNumericChars } from '../utility/regex';

export const getEcliqAccountNumberLambda = async (event: any, context: Context): Promise<APIGatewayProxyResult> => {

  try {
    const params = event.queryStringParameters;

    if (!params) {
      console.log(`No query parameters were provided in ${process.env.ENVIRONMENT_KEY}`);
      return getHandlerResponse(400, { error: 'Missing query string parameters' });
    }

    if (!params.priorPolicyNumber) {
      console.log(`No priorPolicyNumber was provided in ${process.env.ENVIRONMENT_KEY}`);
      return getHandlerResponse(400, { error: 'Missing priorPolicyNumber in queryStringParameters' });
    }

    if (!params.btCode) {
      console.log(`No btCode was provided in ${process.env.ENVIRONMENT_KEY}`);
      return getHandlerResponse(400, { error: 'Missing btCode in queryStringParameters' });
    }

    const priorCarrierPolicyNumber = params.priorPolicyNumber;
    let btCode = params.btCode;
    if (typeof btCode === 'string') {
      btCode = parseInt(removeNonNumericChars(btCode));
    }

    console.log('Initializing MongoDB connection...');
    await initializeMongo(process.env.MONGODB_CONNECTION);

    const searchResults = await searchForAccountByPriorCarrierPolicyNumberAndBtCode(priorCarrierPolicyNumber, btCode);
    if (searchResults. length === 0) {
      console.log(
        `No customer account was found with priorCarrierPolicyNumber: ${priorCarrierPolicyNumber} and btCode: ${btCode}`,
      );
      return getHandlerResponse(200, '');
    }

    // Sort the ecliqAccounts by ecliqAccountCreationDate in descending order
    const sortedEcliqAccounts = searchResults[0].ecliqAccounts
      ?.slice()
      .sort((a, b) => b.ecliqAccountCreationDate.getTime() - a.ecliqAccountCreationDate.getTime());

    // Return the most recent ecliqAccountNumber by ecliqAccountCreationDate, else ''
    return getHandlerResponse(
      200,
      sortedEcliqAccounts && sortedEcliqAccounts.length > 0 ? sortedEcliqAccounts[0].ecliqAccountNumber : '',
    );
  } catch (error) {
    return getHandlerResponse(500, { error: 'There was an error: ' + error.message });
  }
};
