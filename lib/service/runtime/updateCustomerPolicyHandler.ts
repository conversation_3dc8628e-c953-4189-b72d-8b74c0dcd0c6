import { Context, APIGatewayProxyResult } from 'aws-lambda';
import { CustomerPolicyChangeRequest } from '../../../types/request';
import { getHandlerResponse } from '../utility/getHandlerResponse';
import { initializeMongo, searchForExistingCustomerPolicy, updateExistingFieldsOnToCustomerPolicy } from './mongo';
import { EventSource } from '../../../types/enums';

export const updateCustomerPolicyLambda = async (event: any, context: Context): Promise<APIGatewayProxyResult> => {
  try {
    const request: CustomerPolicyChangeRequest = JSON.parse(event.body);

    console.log('Initializing MongoDB connection...');
    await initializeMongo(process.env.MONGODB_CONNECTION);

    const foundPolicy = await searchForExistingCustomerPolicy(request.searchCriteria);

    if (!foundPolicy) {
      return getHandlerResponse(400, {
        error: 'No customer policy was found with the provided search criteria',
      });
    }
    
    const eventSource = EventSource[request.eventSource as keyof typeof EventSource];
    await updateExistingFieldsOnToCustomerPolicy(request.customerPolicy, foundPolicy, eventSource);

    return getHandlerResponse(200, `Customer policy ${foundPolicy._id} updated successfully`);
  } catch (error) {
    console.error(error);
    return getHandlerResponse(500, {
      error: 'There was an error: ' + error.message,
    });
  }
};
