import { Context } from 'aws-lambda';
import Request, { CustomerMetadata, CustomerPolicy } from '../../../types/request';
import Response from '../../../types/response';
import { getHandlerResponse } from '../utility/getHandlerResponse';
import  * as MoveOppAccountSearch from '../search/moveOpportunitiesCustomerAccountSearch';
import * as DefaultAccountSearch from '../search/customerAccountSearch';
import { CustomerAccountSearchHelper }  from '../search/customerAccountSearchHelper';
import {
  initializeMongo,
  insertNewCustomerAccount,
  addNewCustomerPoliciesToCustomerAccount,
  findExistingCustomerPoliciesByAccountId,
  updateCustomerAccount,
  updateCustomerPolicy,
  insertNewCustomerAccountForNewBtCode,
  archiveAndDeleteCustomerAccount,
  searchForAccountByIdOrEcliqAccountNumberOrQuoteEntryId,
} from './mongo';
import CustomerAccountModel, { CustomerPolicyModel } from '../../../model/customerAccountModel';
import { WithId, ObjectId } from 'mongodb';
import { EventSource } from '../../../types/enums';
import { Logger } from '@aws-lambda-powertools/logger';
import { AuditLogHelper } from "../utility/auditLogHelper";
import {TransactionStatus, TransactionEvent} from "@lmig/audit-log-node-starter";
import { Tracer } from '@aws-lambda-powertools/tracer';
import { Metrics } from '@aws-lambda-powertools/metrics';
import type { LambdaInterface } from '@aws-lambda-powertools/commons/types';

const serviceName = 'customerAccountService';
const logger = new Logger({ serviceName });
const tracer = new Tracer({ serviceName });
const metrics = new Metrics({
  namespace: 'BookTransfer',
  serviceName: serviceName,
});

class AddCustomerPolicyHandler implements LambdaInterface {

  private static auditLogHelperInstance: AuditLogHelper | null = null;
  private auditLogHelper!: AuditLogHelper;
  
    public async initializeHelpers() {
      if (!AddCustomerPolicyHandler.auditLogHelperInstance) {
        try {
          AddCustomerPolicyHandler.auditLogHelperInstance = await AuditLogHelper.init();
          logger.info('AuditLogHelper initialized successfully');
        } catch (error) {
          logger.error('Failed to initialize AuditLogHelper', error);
        }
      }
      this.auditLogHelper = AddCustomerPolicyHandler.auditLogHelperInstance!;
    }

  // flushes (publishes) any metrics that have been recorded
  @metrics.logMetrics({ captureColdStartMetric: true })
  @logger.injectLambdaContext({ logEvent: false })
  @tracer.captureLambdaHandler({ captureResponse: false }) // capture response is false to avoid logging sensitive data
  public async handler(event: Request, context: Context) {
    tracer.putAnnotation('awsRequestId', context.awsRequestId);
    await this.initializeMongo();
    //validate request and sanitize
    let response;
    let retries = 3;
    while (retries > 0) {
      response = await this.processRequest(event);
      if (response.error && response.error.includes('E11000 duplicate key')) {
        retries--;
        logger.warn(`Duplicate key error encountered. Retries left: ${retries}`);
      } else {
        break;
      }
    }

    if (response?.error) {
      // Don't change this log as it is used to create an alert
      logger.error(`Error processing request: ${JSON.stringify(response)}`);
    }

    return getHandlerResponse(200, { success: 'Completed processing', data: response });
  }

  @tracer.captureMethod({ subSegmentName: 'processRequest' })
  public async processRequest(
    request: Request
  ): Promise<{
    statusCode: number;
    success?: string;
    error?: string;
  }> {
    const response: Partial<Response> = { searchCriteria: request.searchCriteria, customerPolicies: [] };
    await this.initializeHelpers();
    
    const transactionEvents: TransactionEvent[] = [];

    //generate mongo id
    const transactionId = new ObjectId().toString();
    const parentTransactionId = request.transactionId || '';

    if (transactionId) {
      request.transactionId = transactionId;
    }
   
    const endTransaction = (status: TransactionStatus, extra: Record<string, any> = {}) => {
      if (transactionId) {
        logger.info(`Ending transaction with status: ${status}`);
        const endTransactionEvent = this.auditLogHelper.buildTransactionEvent('END', status, new Date(), {
          origin: "addcustomerPolicyHandler",
          ...extra,
        });
        transactionEvents.push(endTransactionEvent);
        const transactionRequest = this.auditLogHelper.buildTransactionRequest(transactionId, 'CUSTOMER_ACCOUNT_UPSERT', status, transactionEvents,
          {
            "origin": "addcustomerPolicyHandler",
            "relatedTransactionId": parentTransactionId,
          }
        );
        transactionRequest.completedDateTime = new Date();
        // Make the create transaction call non-blocking
        this.auditLogHelper.createTransaction(transactionRequest).catch(error => {
        logger.error(`Audit log transaction failed : ${error}`);
        });
        logger.info('Transaction Ended');
      }
    };

    try {
      //Start transaction event
      transactionEvents.push(this.auditLogHelper.buildTransactionEvent("START", TransactionStatus.SUCCESS, new Date(), {
        "origin": "addcustomerPolicyHandler",
      }));

      if(!request.eventSource) {
         endTransaction(TransactionStatus.FAILED, { error: "Missing event source"});
        return { statusCode: 400, error: 'Missing event source' };
      }

      if(!request.searchCriteria) {
        endTransaction(TransactionStatus.FAILED, { error: "Missing search criteria"});
        return { statusCode: 400, error: 'Missing search criteria' };
      }

      if(Object.keys(request.searchCriteria).length === 0) {
        endTransaction(TransactionStatus.FAILED, { error: 'Invalid search criteria' });
        return { statusCode: 400, error: 'Invalid search criteria' };
      }

      const eventSource = EventSource[request.eventSource as keyof typeof EventSource];

      let existingAccounts;
      let foundCriteria = { value: '' };

      logger.info('Searching for existing customer account...');
      let searchEventRequest = this.auditLogHelper.buildTransactionEvent(
        "CUSTOMER_ACCOUNT_SEARCH",
        TransactionStatus.IN_PROGRESS,
        new Date(),
        { "origin": "addcustomerPolicyHandler"});

      if (request.eventSource === 'AQE' && CustomerAccountSearchHelper.isNotBtCodeMatching(request.searchCriteria)) {
        existingAccounts = await MoveOppAccountSearch.moveOppAccountSearch(request.searchCriteria, foundCriteria);
      } else {
        existingAccounts = await DefaultAccountSearch.defaultOppAccountSearch(request.searchCriteria, foundCriteria);
      }

      updateEvent(searchEventRequest);

      if (existingAccounts.length > 0) {
        if (typeof existingAccounts[0]._id === 'string') {
          existingAccounts[0]._id = new ObjectId(existingAccounts[0]._id);
        }
        //AQE Move Opportunities
        //When customer account match is found for Original BT code
        //And the request is coming as part of move opportunities
        //then create a new customer account with new BT code
        if (
          (foundCriteria.value === 'priorCarrierPolicyNumberAndOriginalBtCode' ||
            foundCriteria.value === 'customerMetadataAndOriginalBtCode') &&
            CustomerAccountSearchHelper.isNotBtCodeMatching(request.searchCriteria)
        ) {
          let createEventRequest = this.auditLogHelper.buildTransactionEvent(
            "CREATE_CUSTOMER_ACCOUNT_POLICY_FOR_NEW_BT_CODE",
            TransactionStatus.IN_PROGRESS,
            new Date(),
            { "origin": "addcustomerPolicyHandler::createCustomerAccountForNewBtCode"});
          let subEvents: TransactionEvent[] = [];
          await this.createCustomerAccountForNewBtCode(request, eventSource, existingAccounts[0], response, subEvents);
          updateEvent(createEventRequest);
          transactionEvents.push(...subEvents)
        } else {
          // Found a customer account. Update it if needed
          logger.info('Found existing accounts');
          let updateEventRequest = this.auditLogHelper.buildTransactionEvent(
            "UPDATE_CUSTOMER_ACCOUNT_POLICY",
            TransactionStatus.IN_PROGRESS,
            new Date(),
            { "origin": "addcustomerPolicyHandler::updateExistingCustomerAccount"});
          let subEvents: TransactionEvent[] = [];
          await this.updateExistingCustomerAccount(request, eventSource, existingAccounts[0], response, subEvents);
          updateEvent(updateEventRequest);
          transactionEvents.push(...subEvents);
        }
      } else {
        let createEventRequest = this.auditLogHelper.buildTransactionEvent(
          "CREATE_CUSTOMER_ACCOUNT_POLICY",
          TransactionStatus.IN_PROGRESS,
          new Date(),
          { "origin": "addcustomerPolicyHandler::createNewCustomerAccount",
            "policiesCount": request.customerPolicies?.length || 0,
          });
        
        // Did not find a customer account. Create one
        const createResponse = await this.createNewCustomerAccount(request, response);
        if (createResponse) {
          endTransaction(TransactionStatus.FAILED, { error: createResponse.error });
          // 400 error occurred
          return createResponse;
        }
        updateEvent(createEventRequest);
        
      }
      if (response.customerMetadata) {
        response.customerMetadata.customerMetadata.addressCleanseApplied = 'Y';
      }
      if (response.searchCriteria && response.searchCriteria.customerMetadata) {
        response.searchCriteria.customerMetadata.addressCleanseApplied = 'Y';
      }

      logger.info('Done.');
      endTransaction(TransactionStatus.SUCCESS);
      return { statusCode: 200, success: 'Completed processing', ...response };
    } catch (error) {
      logger.error(error);
      endTransaction(TransactionStatus.FAILED, { error: error.message });
      // TODO: send request to DLQ
      return { statusCode: 500, error: `There was an error: ${error.message}`, ...response };
    }

    function updateEvent(event: TransactionEvent) {
      event.status = TransactionStatus.SUCCESS;
      event.endTimeStamp = new Date();
      transactionEvents.push(event);
    }
  }

  @tracer.captureMethod({ subSegmentName: 'initializeMongo' })
  private async initializeMongo() {
    await initializeMongo(process.env.MONGODB_CONNECTION);
  }

  @tracer.captureMethod({ subSegmentName: 'updateExistingCustomerAccount' })
  public async updateExistingCustomerAccount(
    request: Request,
    eventSource: EventSource,
    existingAccount: WithId<CustomerAccountModel>,
    response: Partial<Response>,
    subEvents: TransactionEvent[],
  ): Promise<void> {
    logger.info(`Updating existing customer account ${JSON.stringify(existingAccount._id)}...`);
    response.id = existingAccount._id.toString();

    const eventSourceString = EventSource[request.eventSource as keyof typeof EventSource];
    logger.info(`existing account: ${existingAccount._id}`);
    logger.info(`searchCriteria customerAccountId: ${request.searchCriteria.customerAccountId}`);
    let updateEventRequest = this.auditLogHelper.buildTransactionEvent(
      "UPDATE_CUSTOMER_ACCOUNT",
      TransactionStatus.IN_PROGRESS,
      new Date(),
      { "origin": "addcustomerPolicyHandler::updateExistingCustomerAccount",
        "existingCustomerAccountId": existingAccount._id.toString()});
    existingAccount = await updateCustomerAccount(request, existingAccount);
    this.updateSubEvent(updateEventRequest, subEvents);
    

    if (!request.customerPolicies || request.customerPolicies.length == 0) {
      logger.info('No customer policies data in the request');
      return;
    }

    let searchPolicyEventRequest = this.auditLogHelper.buildTransactionEvent(
      "SEARCH_CUSTOMER_POLICIES",
      TransactionStatus.IN_PROGRESS,
      new Date(),
      { "origin": "addcustomerPolicyHandler::updateExistingCustomerAccount",
        "existingCustomerAccountId": existingAccount._id.toString()});

    let existingCustomerPolicies = [];
    if (
      request.searchCriteria.customerAccountId &&
      request.searchCriteria.customerAccountId !== existingAccount._id.toString()
    ) {
      logger.info(
        `Searching for existing customer policies on account: ${request.searchCriteria.customerAccountId}...`,
      );
      existingCustomerPolicies = await findExistingCustomerPoliciesByAccountId(
        new ObjectId(request.searchCriteria.customerAccountId),
      );
    } else {
      logger.info(`Searching for existing customer policies on account: ${existingAccount._id.toString()}...`);
      existingCustomerPolicies = await findExistingCustomerPoliciesByAccountId(existingAccount._id);
    }

    this.updateSubEvent(searchPolicyEventRequest, subEvents);

    //AQE Move Opportunities
    //When the request is coming from AQE move opportunities
    //And a customer match is found with new BT code then
    //Update the customer policies customerAccountId
    this.setCustomerPolicyBtCode(request.customerPolicies, request.customerMetadata?.btCode);
    existingCustomerPolicies.forEach((existingPolicy) => {
      existingPolicy.customerAccountId = existingAccount._id!;
    });
    logger.info(`Found ${existingCustomerPolicies.length} existing policies on ${existingAccount._id}`);
    logger.info(`Existing policies ids: ${existingCustomerPolicies.map((policy) => policy._id?.toString())}`);

    let updatePolicyEventRequest = this.auditLogHelper.buildTransactionEvent(
      "UPDATE_CUSTOMER_POLICIES",
      TransactionStatus.IN_PROGRESS,
      new Date(),
      { "origin": "addcustomerPolicyHandler::updateExistingCustomerAccount",
        "policiesUpdatedCount": existingCustomerPolicies.length,
      });

    const policiesToCreate = await this.filterPoliciesByEventSource(
      request,
      existingCustomerPolicies,
      existingAccount,
      response,
      eventSource,
    );

    if (policiesToCreate.length > 0) {
      logger.info(`Adding ${policiesToCreate.length} policies to existing account ${existingAccount._id}...`);
      let createPoliciesEventRequest = this.auditLogHelper.buildTransactionEvent(
        "CREATE_CUSTOMER_POLICIES",
        TransactionStatus.IN_PROGRESS,
        new Date(),
        { "origin": "addcustomerPolicyHandler::updateExistingCustomerAccount",
          "policiesCreatedCount": policiesToCreate.length,
        });
      await Promise.all(policiesToCreate.map(async (policy) => {
        policy.transactionId = await this.startOppToQeEvent(request.transactionId || '', 'updateExistingCustomerAccount', eventSource, 'CREATE_POLICY');
      }));
      const insertedPolicies = await addNewCustomerPoliciesToCustomerAccount(
        policiesToCreate,
        existingAccount,
        new Date(Date.now()),
        eventSource,
      );
      insertedPolicies.forEach((id) =>
        logger.info(`Created policy on ${JSON.stringify(existingAccount._id)}:`, JSON.stringify(id.toString())),
      );

      response.customerPolicies!.push(...insertedPolicies);
      this.updateSubEvent(createPoliciesEventRequest, subEvents);
    } else {
      this.updateSubEvent(updatePolicyEventRequest, subEvents);
      logger.info(`No new policies to add to existing account ${existingAccount._id}`);
    }
    //When searchCriteria.btCode and searchCriteria.customerMetadata.btCode are not matching
    //and  there are no existing policies to the customer account
    //then archive the existing customer account and delete from customer account collection
    if (
      CustomerAccountSearchHelper.isNotBtCodeMatching(request.searchCriteria) &&
      request.searchCriteria &&
      request.searchCriteria.customerAccountId
    ) {
      logger.info(`Archive and delete`);
     
      let archiveAccountEventRequest = this.auditLogHelper.buildTransactionEvent(
        "ARCHIVE_AND_DELETE_CUSTOMER_ACCOUNT",
        TransactionStatus.IN_PROGRESS,
        new Date(),
        { "origin": "addcustomerPolicyHandler::createCustomerAccountForNewBtCode",
          "customerAccountId": request.searchCriteria.customerAccountId,
        });
      await archiveAndDeleteCustomerAccount(request.searchCriteria.customerAccountId);
      this.updateSubEvent(archiveAccountEventRequest, subEvents);
    }
  }

  private updateSubEvent(createPoliciesEventRequest: TransactionEvent, subEvents: TransactionEvent[]) {
    createPoliciesEventRequest.status = TransactionStatus.SUCCESS;
    createPoliciesEventRequest.endTimeStamp = new Date();
    subEvents.push(createPoliciesEventRequest);
  }

  @tracer.captureMethod({ subSegmentName: 'filterPoliciesByEventSource' })
  public async filterPoliciesByEventSource(
    request: Request,
    existingCustomerPolicies: CustomerPolicyModel[],
    existingAccount: WithId<CustomerAccountModel>,
    response: Partial<Response>,
    eventSource: EventSource,
  ): Promise<CustomerPolicy[]> {
    const policiesToCreate: CustomerPolicy[] = [];

    for (const policy of request.customerPolicies) {
      console.log("Policy:",policy);
      let shouldCreate = false;

      switch (eventSource) {
        case EventSource.BAT:
          let existingPolicyBAT = existingCustomerPolicies.find(
            (existingCustomerPolicy) =>
              this.isBATPolicyExists(existingCustomerPolicy, policy),
          );

          if (existingPolicyBAT) {
            response.customerPolicies!.push(existingPolicyBAT);
            policy.transactionId = await this.startOppToQeEvent(request.transactionId || '', 'filterPoliciesByEventSource', eventSource, 'UPDATE_POLICY');
            await this.updatePolicyBAT(existingPolicyBAT, policy);
          } else {
            shouldCreate = true;
          }
          break;

        case EventSource.AQE:
          let existingPolicyAQE = existingCustomerPolicies.find(
            (existingCustomerPolicy) =>
              this.isAQEPolicyExists(existingCustomerPolicy, policy),
          );
          console.log("existingPolicyAQE:",existingPolicyAQE);
          if (existingPolicyAQE) {
            response.customerPolicies!.push(existingPolicyAQE);
            policy.transactionId = await this.startOppToQeEvent(request.transactionId || '', 'filterPoliciesByEventSource', eventSource, 'UPDATE_POLICY');
            await this.updatePolicyAQE(existingPolicyAQE, policy);
          } else {
            shouldCreate = true;
          }
          break;

        case EventSource.ECLIQ:
          let existingPolicyEcliq = this.getExistingEcliqpolicy(policy, existingCustomerPolicies);

          if (existingPolicyEcliq) {
            response.customerPolicies!.push(existingPolicyEcliq);
            policy.transactionId = await this.startOppToQeEvent(request.transactionId || '', 'filterPoliciesByEventSource', eventSource, 'UPDATE_POLICY');
            await this.updatePolicyEcliq(existingPolicyEcliq, policy);
          } else {
            shouldCreate = true;
          }
          break;
        case EventSource.SALESFORCE:
          let existingPolicySalesforce = existingCustomerPolicies.find(
            (existingCustomerPolicy) =>

              this.isSalesforcePolicyExists(existingCustomerPolicy, policy),
          );
          console.log("existingCustomerPolicy:",existingPolicySalesforce);
          if (existingPolicySalesforce) {
            response.customerPolicies!.push(existingPolicySalesforce);
            policy.transactionId = await this.startOppToQeEvent(request.transactionId || '', 'filterPoliciesByEventSource', eventSource, 'UPDATE_POLICY');
           await this.updatePolicySalesforce(existingPolicySalesforce, policy);
          } else {
            shouldCreate = true;
          }
          break;

      case EventSource.PERFTEST:
        let existingPolicyPerfTest = existingCustomerPolicies.find(
          (existingCustomerPolicy) => existingCustomerPolicy._id?.equals(new ObjectId(policy._id))
        );

        if (existingPolicyPerfTest) {
          shouldCreate = true;
        }
        break;
        
      default:
        throw new Error(`Unsupported event source: ${eventSource}`);
    }

      if (shouldCreate) {
        policiesToCreate.push(policy);
      }
    }

    return policiesToCreate;
  }

  private isSalesforcePolicyExists(existingCustomerPolicy: CustomerPolicyModel, policy: CustomerPolicy): boolean {
    console.log("Policy number for existing: ",existingCustomerPolicy.priorPolicy?.policyNumber);
    console.log("Policy number for new: ",policy.priorPolicy?.policyNumber);
    console.log("LOB for existing: ", existingCustomerPolicy.priorPolicy?.lineOfBusiness);
    console.log("LOB for new:",policy.priorPolicy?.lineOfBusiness);
    return existingCustomerPolicy.salesforce?.policyDetailsId === policy.salesforce?.policyDetailsId ||
      (existingCustomerPolicy.priorPolicy?.policyNumber === policy.priorPolicy?.policyNumber &&
      existingCustomerPolicy.priorPolicy?.lineOfBusiness === policy.priorPolicy?.lineOfBusiness) ||
      (!existingCustomerPolicy.priorPolicy?.policyNumber &&
      existingCustomerPolicy.priorPolicy?.lineOfBusiness === policy.priorPolicy?.lineOfBusiness &&
      [
        existingCustomerPolicy.bookAssessment,
        existingCustomerPolicy.aqe,
      ].some(source =>
        source?.priorExpirationDate &&
        policy.salesforce?.priorExpirationDate &&
        new Date(source.priorExpirationDate).toISOString() === new Date(policy.salesforce.priorExpirationDate).toISOString() &&
        Number(source.priorPremium) === Number(policy.salesforce?.priorPremium)
      ));
  }

  private getExistingEcliqpolicy(policy: CustomerPolicy, existingCustomerPolicies: CustomerPolicyModel[]): CustomerPolicyModel | undefined {
    const lmLOBs = {
      'AUTOB': 'AUTOB', 'BOP': 'BOP', 'CFRM': 'FARM', 'CGL': 'CGL', 'CPKGE': 'CPKGE', 'CPSP': 'CPKGE', 'EXLIA': 'UMBRC', 'INMAR': 'INMRC',
      'INMRC': 'INMRC', 'PROPC': 'PROP', 'UMBRC': 'UMBRC', 'WORK': 'WORK'
    };
    const packageLOBs = { 'BOP': 'CPKGE', 'CPKGE': 'BOP', 'CPSP': 'BOP', 'CFRM': 'CPKGE' };

    const firstLobToSearch = lmLOBs[policy.libertyPolicy?.lineOfBusiness as keyof typeof lmLOBs] || 'CPKGE';


    let existingPolicyEcliq = existingCustomerPolicies.find(
      (existingCustomerPolicy) => existingCustomerPolicy.libertyPolicy?.ecliqId === policy.libertyPolicy?.ecliqId ||
        (!(['EBLIA', 'LL'].includes(policy.libertyPolicy?.lineOfBusiness || '')) &&
          existingCustomerPolicy.ecliqAccountNumber === policy.ecliqAccountNumber &&
          existingCustomerPolicy.priorPolicy?.lineOfBusiness === firstLobToSearch &&
          !existingCustomerPolicy.libertyPolicy?.ecliqId)
    );

    const secondLobToSearch = packageLOBs[policy.libertyPolicy?.lineOfBusiness as keyof typeof packageLOBs] || 'BOP';
    if (!existingPolicyEcliq && !(['EBLIA', 'LL'].includes(policy.libertyPolicy?.lineOfBusiness || '')) && secondLobToSearch) {
      logger.info(`Searching for existing policy with line of business: ${secondLobToSearch}`);
      existingPolicyEcliq = existingCustomerPolicies.find(
        (existingCustomerPolicy) => existingCustomerPolicy.ecliqAccountNumber === policy.ecliqAccountNumber &&
          existingCustomerPolicy.priorPolicy?.lineOfBusiness === secondLobToSearch &&
          !existingCustomerPolicy.libertyPolicy?.ecliqId
      );
    }
    return existingPolicyEcliq;
  }

  private isAQEPolicyExists(existingCustomerPolicy: CustomerPolicyModel, policy: CustomerPolicy): boolean {
      console.log("Policy number for existing: ",existingCustomerPolicy.priorPolicy?.policyNumber);
      console.log("Policy number for new: ",policy.priorPolicy?.policyNumber);
      console.log("LOB for existing: ", existingCustomerPolicy.priorPolicy?.lineOfBusiness);
      console.log("LOB for new:",policy.priorPolicy?.lineOfBusiness);
    return existingCustomerPolicy.aqe?.opportunityId === policy.aqe?.opportunityId ||
      (existingCustomerPolicy.priorPolicy?.policyNumber === policy.priorPolicy?.policyNumber &&
        existingCustomerPolicy.priorPolicy?.lineOfBusiness === policy.priorPolicy?.lineOfBusiness) ||
        // If priorPolicy policynumber not exists then match with priorPolicy lineOfBusiness, priorExpirationDate and priorPremium
        // of bookAssessment or salesforce
      (!existingCustomerPolicy.priorPolicy?.policyNumber && 
        existingCustomerPolicy.priorPolicy?.lineOfBusiness === policy.priorPolicy?.lineOfBusiness && 
        [
          existingCustomerPolicy.bookAssessment,
          existingCustomerPolicy.salesforce,
        ].some(source =>
          source?.priorExpirationDate &&
          policy.aqe?.priorExpirationDate &&
          new Date(source.priorExpirationDate).toISOString() === new Date(policy.aqe.priorExpirationDate).toISOString() &&
          Number(source.priorPremium) === Number(policy.aqe?.priorPremium)
        ));
  }

  private isBATPolicyExists(existingCustomerPolicy: CustomerPolicyModel, policy: CustomerPolicy): boolean {
    console.log("Policy number for existing: ",existingCustomerPolicy.priorPolicy?.policyNumber);
    console.log("Policy number for new: ",policy.priorPolicy?.policyNumber);
    console.log("LOB for existing: ", existingCustomerPolicy.priorPolicy?.lineOfBusiness);
    console.log("LOB for new:",policy.priorPolicy?.lineOfBusiness);
    return existingCustomerPolicy.bookAssessment?.assessmentId === policy.bookAssessment?.assessmentId ||
      (existingCustomerPolicy.priorPolicy?.lineOfBusiness === policy.priorPolicy?.lineOfBusiness &&
        existingCustomerPolicy.priorPolicy?.policyNumber === policy.priorPolicy?.policyNumber) ||
        // If priorPolicy policynumber not exists then match with priorPolicy lineOfBusiness, priorExpirationDate and priorPremium
        // of aqe or salesforce
        (!existingCustomerPolicy.priorPolicy?.policyNumber && 
          existingCustomerPolicy.priorPolicy?.lineOfBusiness === policy.priorPolicy?.lineOfBusiness && 
          [
            existingCustomerPolicy.aqe,
            existingCustomerPolicy.salesforce,
          ].some(source =>
            source?.priorExpirationDate &&
            policy.bookAssessment?.priorExpirationDate &&
            new Date(source.priorExpirationDate).toISOString() === new Date(policy.bookAssessment.priorExpirationDate).toISOString() &&
            Number(source.priorPremium) === Number(policy.bookAssessment?.priorPremium)
          ));
  }

  // Helper functions to update policies based on event source
  // Update logic for BAT
  @tracer.captureMethod({ subSegmentName: 'updatePolicyBAT' })
  public async updatePolicyBAT(existingPolicy: CustomerPolicyModel, policy: CustomerPolicy) {
    const currentDate = new Date(Date.now());
    if (!existingPolicy.bookAssessment?.createdDate) {
      existingPolicy.bookAssessment = { createdDate: currentDate };
    }
    existingPolicy.bookAssessment.assessmentId = policy.bookAssessment?.assessmentId;
    existingPolicy.bookAssessment.assessmentDate = policy.bookAssessment?.assessmentDate;
    existingPolicy.bookAssessment.finalAppetiteDecision = policy.bookAssessment?.finalAppetiteDecision;
    existingPolicy.bookAssessment.priorExpirationDate = policy.bookAssessment?.priorExpirationDate;
    existingPolicy.bookAssessment.priorPremium = Number(policy.bookAssessment?.priorPremium);
    existingPolicy.bookAssessment.lastUpdatedDate = currentDate;
    existingPolicy.lastUpdatedDate = currentDate;
    existingPolicy.lastPolicyEvent = {
      policyEventDate: currentDate,
      policyEventType: EventSource.BAT,
    };
    existingPolicy.transactionId = policy.transactionId;
    const custPolicy = await updateCustomerPolicy(existingPolicy);
    logger.info(`Customer Policy ${custPolicy._id} updated from BAT`);
  }

  // Update logic for AQE
  @tracer.captureMethod({ subSegmentName: 'updatePolicyAQE' })
  public async updatePolicyAQE(existingPolicy: CustomerPolicyModel, policy: CustomerPolicy) {
    const currentDate = new Date(Date.now());
    existingPolicy.emailAddress = policy.emailAddress;
    existingPolicy.phoneNumber = policy.phoneNumber;
    existingPolicy.ratingState = policy.ratingState;
    existingPolicy.btCode = Number(policy.btCode);

    // Update aqe fields
    if (!existingPolicy.aqe?.createdDate) {
      existingPolicy.aqe = { createdDate: currentDate };
    }
    existingPolicy.aqe.businessType = policy.aqe?.businessType;
    existingPolicy.aqe.opportunityId = policy.aqe?.opportunityId;
    existingPolicy.aqe.nNumber = policy.aqe?.nNumber;
    existingPolicy.aqe.status = policy.aqe?.status;
    existingPolicy.aqe.priorExpirationDate = policy.aqe?.priorExpirationDate;
    existingPolicy.aqe.priorPremium = Number(policy.aqe?.priorPremium);
    existingPolicy.aqe.lastUpdatedDate = currentDate;

    existingPolicy.lastPolicyEvent = {
      policyEventDate: currentDate,
      policyEventType: EventSource.AQE,
    };

    existingPolicy.lastUpdatedDate = currentDate;
    existingPolicy.transactionId = policy.transactionId;
    const custPolicy = await updateCustomerPolicy(existingPolicy);
    logger.info(`Customer Policy ${custPolicy._id} updated from AQE`);
  }

  // Update logic for SALESFORCE
  @tracer.captureMethod({ subSegmentName: 'updatePolicySalesforce' })
  public async updatePolicySalesforce(existingPolicy: CustomerPolicyModel, policy: CustomerPolicy) {
    const currentDate = new Date(Date.now());
    // Update salesforce fields
    if (!existingPolicy.salesforce?.createdDate) {
      existingPolicy.salesforce = { createdDate: currentDate };
    }
    existingPolicy.salesforce.isManualQuote = policy.salesforce?.isManualQuote;
    existingPolicy.salesforce.policyEffectiveDate = policy.salesforce?.policyEffectiveDate;
    existingPolicy.salesforce.priorExpirationDate = policy.salesforce?.priorExpirationDate;
    existingPolicy.salesforce.priorPremium = Number(policy.salesforce?.priorPremium);
    existingPolicy.salesforce.policyDetailsId = policy.salesforce?.policyDetailsId;
    existingPolicy.salesforce.quoteEntryId = policy.salesforce?.quoteEntryId;

    this.updatePriorPolicy(existingPolicy, policy);

    existingPolicy.lastPolicyEvent = {
      policyEventDate: currentDate,
      policyEventType: EventSource.SALESFORCE,
    };
    existingPolicy.salesforce.lastUpdatedDate = currentDate;
    existingPolicy.lastUpdatedDate = currentDate;
    existingPolicy.transactionId = policy.transactionId;
    const custPolicy = await updateCustomerPolicy(existingPolicy);
    logger.info(`Customer Policy ${custPolicy._id} updated from salesforce`);
  }

  @tracer.captureMethod({ subSegmentName: 'updatePolicyEcliq' })
  public async updatePolicyEcliq(existingPolicy: CustomerPolicyModel, policy: CustomerPolicy) {
    const currentDate = new Date(Date.now());
    if (!existingPolicy.libertyPolicy?.createdDate) {
      existingPolicy.libertyPolicy = { createdDate: currentDate };
    }
    existingPolicy.libertyPolicy.lastUpdatedDate = currentDate;
    existingPolicy.libertyPolicy.effectiveDate = policy.libertyPolicy?.effectiveDate;
    existingPolicy.libertyPolicy.policyNumber = policy.libertyPolicy?.policyNumber;
    existingPolicy.libertyPolicy.lineOfBusiness = policy.libertyPolicy?.lineOfBusiness;
    existingPolicy.libertyPolicy.status = policy.libertyPolicy?.status;
    existingPolicy.libertyPolicy.premium = Number(policy.libertyPolicy?.premium);
    existingPolicy.libertyPolicy.lineType = policy.libertyPolicy?.lineType;
    existingPolicy.libertyPolicy.producer = policy.libertyPolicy?.producer;
    existingPolicy.libertyPolicy.ecliqId = policy.libertyPolicy?.ecliqId;
    existingPolicy.lastPolicyEvent = {
      policyEventDate: currentDate,
      policyEventType: EventSource.ECLIQ,
    };
    existingPolicy.ecliqAccountNumber = policy.ecliqAccountNumber;
    existingPolicy.lastUpdatedDate = currentDate;
    existingPolicy.isUpdatedByEcliq = true;
    existingPolicy.transactionId = policy.transactionId;

    const custPolicy = await updateCustomerPolicy(existingPolicy);
    logger.info(`Customer Policy ${custPolicy._id} updated from ecliq`);
  }

  @tracer.captureMethod({ subSegmentName: 'createNewCustomerAccount' })
  public async createNewCustomerAccount(request: Request, response: Partial<Response>) {
    logger.info('Creating new customer account...');

    // validate
    if (!request.customerMetadata) {
      return { statusCode: 400, error: 'Missing customerMetadata' };
    }

    // customerMetadata was checked for completeness pre-address cleanse

    // create
    this.setCustomerPolicyBtCode(request.customerPolicies, request.customerMetadata?.btCode);
    if (request.customerPolicies && request.customerPolicies.length > 0) {
      await Promise.all(request.customerPolicies.map(async (policy) => {
        policy.transactionId = await this.startOppToQeEvent(request.transactionId || '', 'createNewCustomerAccount', request.eventSource, 'CREATE_POLICY');
      }));
    }
    const insertResult = await insertNewCustomerAccount(request);
    const insertedAccount = insertResult[0] as CustomerAccountModel;
    const insertedPolicies = insertResult.slice(1) as CustomerPolicyModel[];

    // add ids back to the response
    logger.info(`Created new customer account:' ${insertedAccount._id?.toString()}`);
    logger.info(
      `Created policies on ${insertedAccount._id?.toString()}:, ${insertedPolicies.map((policy) =>
        policy._id?.toString(),
      )}`,
    );
    response.id = insertedAccount._id?.toString();
    response.customerPolicies!.push(...insertedPolicies);

    return null;
  }

  private async startOppToQeEvent(transactionId: string, methodName:string, source:string, action: string) {
    const oppToQETransactionId = new ObjectId().toString();
    try {
      logger.info(`Starting SC_OPP_TO_QE transaction`);
      const startEvent = this.auditLogHelper.buildTransactionEvent('START', TransactionStatus.IN_PROGRESS, new Date(), {
        "origin": `addcustomerPolicyHandler::${methodName}`
      });
      this.auditLogHelper.createTransaction(
        this.auditLogHelper.buildTransactionRequest(oppToQETransactionId, 'SC_OPP_TO_QE', TransactionStatus.IN_PROGRESS, [startEvent], {
          "origin": `addcustomerPolicyHandler::${methodName}`,
          "relatedTransactionId": transactionId,
          "source": source,
          "action": action
        })
      );
      logger.info(`SC_OPP_TO_QE transaction started with ID: ${oppToQETransactionId}`);
    } catch (error) {
      logger.error(`Audit log transaction failed to start: ${error}`);
    }
    return oppToQETransactionId;
  }

  //AQE Move Opportunities have 2 BT codes, original BT code and new BT code
  //Original BT code is where opportunity is currently assigned
  //New BT code is where the opportunity will be moved
  //When there are no customer account for New BT code then create new customer account
  //When there are no customer policies associated to the customer account with original BT code then
  //move the customer account with original BT code to archive collection
  //delete the customer account with original BT code from the customer account collection
  @tracer.captureMethod({ subSegmentName: 'createCustomerAccountForNewBtCode' })
  public async createCustomerAccountForNewBtCode(
    request: Request,
    eventSource: EventSource,
    existingAccount: WithId<CustomerAccountModel>,
    response: Partial<Response>,
    subEvents: TransactionEvent[]
  ) {
    let createAccountEventRequest = this.auditLogHelper.buildTransactionEvent(
       "CREATE_CUSTOMER_ACCOUNT",
        TransactionStatus.IN_PROGRESS,
        new Date(),
        { "origin": "addcustomerPolicyHandler::createCustomerAccountForNewBtCode"});
    logger.info(`Creating new customer account for customer metadata with new BT code...`);
    const insertResult = await insertNewCustomerAccountForNewBtCode(request);
   this.updateSubEvent(createAccountEventRequest, subEvents);
    
    logger.info(`Created new customer account, ${insertResult._id}`);
    response.id = insertResult._id?.toString();
    
    this.setCustomerPolicyBtCode(request.customerPolicies, request.customerMetadata?.btCode);
    let searchPolicyEventRequest = this.auditLogHelper.buildTransactionEvent(
       "SEARCH_CUSTOMER_POLICIES",
        TransactionStatus.IN_PROGRESS,
        new Date(),
        { "origin": "addcustomerPolicyHandler::createCustomerAccountForNewBtCode",
          "existingCustomerAccountId": existingAccount._id.toString()});
    const existingCustomerPolicies = await findExistingCustomerPoliciesByAccountId(existingAccount._id);
    if (existingCustomerPolicies.length > 0) {
      existingCustomerPolicies.forEach((existingPolicy) => {
        existingPolicy.customerAccountId = insertResult._id!;
      });
    }

    this.updateSubEvent(searchPolicyEventRequest, subEvents);

    let updateEventRequest = this.auditLogHelper.buildTransactionEvent(
      "UPDATE_CUSTOMER_POLICIES",
      TransactionStatus.IN_PROGRESS,
      new Date(),
      { "origin": "addcustomerPolicyHandler::createCustomerAccountForNewBtCode",
        "policiesUpdatedCount": existingCustomerPolicies.length,
      });

    const policiesToCreate = await this.filterPoliciesByEventSource(
      request,
      existingCustomerPolicies,
      existingAccount,
      response,
      eventSource,
    );

    if (policiesToCreate.length > 0) {
      logger.info(`Adding ${policiesToCreate.length} policies to existing account ${existingAccount._id}...`);
      let createPoliciesEventRequest = this.auditLogHelper.buildTransactionEvent(
        "CREATE_CUSTOMER_POLICIES",
        TransactionStatus.IN_PROGRESS,
        new Date(),
        { "origin": "addcustomerPolicyHandler::createCustomerAccountForNewBtCode",
          "policiesCreatedCount": policiesToCreate.length,
        });
      await Promise.all(policiesToCreate.map(async (policy) => {
        policy.transactionId = await this.startOppToQeEvent(request.transactionId || '', 'createCustomerAccountForNewBtCode', eventSource, 'CREATE_POLICY');
      }));
      const insertedPolicies = await addNewCustomerPoliciesToCustomerAccount(
        policiesToCreate,
        existingAccount,
        new Date(Date.now()),
        eventSource,
      );
      insertedPolicies.forEach((id) =>
        logger.info(`Created policy on ${JSON.stringify(existingAccount._id)}:`, JSON.stringify(id.toString())),
      );
      response.customerPolicies!.push(...insertedPolicies);
      this.updateSubEvent(createPoliciesEventRequest, subEvents);
    } else {
      this.updateSubEvent(updateEventRequest, subEvents);
      logger.info(`No new policies to add to existing account ${existingAccount._id}`);
    }

    //When searchCriteria.btCode and searchCriteria.customerMetadata.btCode are not matching
    //and  there are no existing policies to the customer account
    //then archive the existing customer account and delete from customer account collection
    if (request.searchCriteria.customerAccountId) {
      logger.info(`Archive and delete`);
      let archiveAccountEventRequest = this.auditLogHelper.buildTransactionEvent(
        "ARCHIVE_AND_DELETE_CUSTOMER_ACCOUNT",
        TransactionStatus.IN_PROGRESS,
        new Date(),
        { "origin": "addcustomerPolicyHandler::createCustomerAccountForNewBtCode",
          "customerAccountId": request.searchCriteria.customerAccountId,
        });
      await archiveAndDeleteCustomerAccount(request.searchCriteria.customerAccountId);
     this.updateSubEvent(archiveAccountEventRequest, subEvents);
    }
  }

  @tracer.captureMethod({ subSegmentName: 'searchForAccountByIdOrEcliqAccountNumberOrQuoteEntryId' })
  private async searchForAccountByIdOrEcliqAccountNumberOrQuoteEntryId(
    customerAccountId: string | undefined,
    ecliqAccountNumber: string | undefined,
    quoteEntryId: string | undefined,
  ): Promise<WithId<CustomerAccountModel>[]> {
    return await searchForAccountByIdOrEcliqAccountNumberOrQuoteEntryId(
      customerAccountId,
      ecliqAccountNumber,
      quoteEntryId,
    );
  }

  @tracer.captureMethod({ subSegmentName: 'updatePriorPolicy' })
  public updatePriorPolicy(existingPolicy: CustomerPolicyModel, policy: CustomerPolicy) {
    if (!existingPolicy.priorPolicy) {
      existingPolicy.priorPolicy = { lastUpdatedSource: EventSource.SALESFORCE };
    }
    let updateSource = false;
    if (!existingPolicy.priorPolicy?.lineOfBusiness) {
      existingPolicy.priorPolicy.lineOfBusiness = policy.priorPolicy?.lineOfBusiness;
      updateSource = true;
    }

    if (!existingPolicy.priorPolicy?.carrier) {
      existingPolicy.priorPolicy.carrier = policy.priorPolicy?.carrier;
      updateSource = true;
    }

    if (!existingPolicy.priorPolicy?.policyNumber) {
      existingPolicy.priorPolicy.policyNumber = policy.priorPolicy?.policyNumber;
      updateSource = true;
    }

    if (updateSource) {
      existingPolicy.priorPolicy.lastUpdatedSource = EventSource.SALESFORCE;
    }
  }

  public setCustomerPolicyBtCode(policies: CustomerPolicy[], btCode: string|number|undefined){
    if(policies && policies.length > 0){
      for (const policy of policies) {
        if(policy.btCode == undefined){
          policy.btCode = btCode;
        }
      }
    }
  }
}

const handlerClass = new AddCustomerPolicyHandler();
const addCustomerPolicyLambda = handlerClass.handler.bind(handlerClass);
export { addCustomerPolicyLambda };
