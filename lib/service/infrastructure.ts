import { ISubnet, IVpc, SecurityGroup, Subnet, Vpc, Port } from 'aws-cdk-lib/aws-ec2';
import { AnyPrincipal, ManagedPolicy, Role, ServicePrincipal } from 'aws-cdk-lib/aws-iam';
import * as xray from 'aws-cdk-lib/aws-xray';
import { Key } from 'aws-cdk-lib/aws-kms';
import { Alias, LayerVersion, Runtime, Tracing } from 'aws-cdk-lib/aws-lambda';
import { RetentionDays } from 'aws-cdk-lib/aws-logs';
import {
  Aws,
  CfnOutput,
  Duration,
  RemovalPolicy,
  aws_apigateway as apigw,
  aws_logs as logs,
  aws_iam as iam,
  aws_lambda,
  Stack,
} from 'aws-cdk-lib';
import {
  AccountCustomResource,
  LMBaseStackProps,
  PapiSecretProvider,
  SubnetCustomResource,
  VPCCustomResource,
  VPCEndpointCustomResource,
  KMSEncryptCustomResource,
  SecurityGroupCustomResource,
  LambdaLayerCustomResource,
} from '@lmig/swa-cdk-core';
import { Datadog } from 'datadog-cdk-constructs-v2';
import { StackConfiguration } from '../../bin/config';
import { NodejsFunction, SourceMapMode } from 'aws-cdk-lib/aws-lambda-nodejs';
import { join } from 'path';
import { Construct } from 'constructs';
import { PythonFunction } from '@aws-cdk/aws-lambda-python-alpha';
import { LambdaFactory } from '@lmig/bt-cdk-common';

export class ServiceResources extends Construct {
  private readonly API_ENDPOINT = 'api';

    /*
    Add CA Certs Layers to lambdas that call APIs
    https://libertymutual.atlassian.net/wiki/spaces/ETSPC/pages/**********/AWS+Internal+CA+Cert+Migration#Lambda-Functions
    https://docs.aws.amazon.com/lambda/latest/dg/lambda-nodejs.html#nodejs-certificate-loading
    */
    public certsLayer = new LambdaLayerCustomResource(this, 'certsLayer', {
      layerName: 'aws-cs-shared-lambda-layers-certificates',
      targetAccountId: '************', // layer is in this account
      updateOnDeployment: true,
    });

  constructor(scope: Construct, id: string, props?: LMBaseStackProps) {
    super(scope, id);

    const lambdaRole: Role = new Role(this, 'LambdaRole', {
      assumedBy: new ServicePrincipal('lambda.amazonaws.com'),
      managedPolicies: [
        ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
        ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaVPCAccessExecutionRole'),
        ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaENIManagementAccess'),
      ],
    });

    const account: AccountCustomResource = new AccountCustomResource(this, 'Account');

    const vpcCR: VPCCustomResource = new VPCCustomResource(this, 'VPCCR', {
      vpcEnv: account.environmentClassification(),
      excludeVpcEndpoints: true,
    });

    const subnetCRs: SubnetCustomResource[] = [];
    let azs: string[] = ['1'];

    if (process.env.VPC_AZS) {
      azs = process.env.VPC_AZS.split(',');
    }

    for (const az of azs) {
      const subnet1: SubnetCustomResource = new SubnetCustomResource(this, 'PrivateSubnetAz' + az, {
        az: az,
        name: process.env.VPC_NAME || 'private',
        vpcId: vpcCR.vpcId(),
      });
      subnetCRs.push(subnet1);
    }

    const subnets: ISubnet[] = subnetCRs.map((subnetCR, index) => {
      return Subnet.fromSubnetAttributes(this, 'subnet' + index, {
        subnetId: subnetCR.ref,
        availabilityZone: subnetCR.availabilityZone(),
      });
    });

    const vpc: IVpc = Vpc.fromVpcAttributes(this, 'VPC', {
      vpcId: vpcCR.ref,
      availabilityZones: subnetCRs.map((subnet) => subnet.availabilityZone()),
    });

    const kmsKey: Key = new Key(this, 'KMSKey', {
      enableKeyRotation: true,
      removalPolicy: RemovalPolicy.DESTROY,
      pendingWindow: Duration.days(7),
      description: `${StackConfiguration.name}-kms-key KMS Key`,
    });

    // Use KMS Key to encrypt MongoDB Connection string
    const kmsEncryptCustomResource = new KMSEncryptCustomResource(this, `MongoDbUrlEncrypted`, {
      kmsKeyId: kmsKey.keyId,
      plainText: StackConfiguration.mongoDbConnection,
    });
    
    const encryptedDbUrl = kmsEncryptCustomResource.cipherText();
    const secretProvider = new PapiSecretProvider(this, 'PapiSecretProvider');
    const environment: { [key: string]: string } = {
      ...StackConfiguration.environment,
      MONGODB_CONNECTION: StackConfiguration.isLocal ? StackConfiguration.mongoDbConnection : encryptedDbUrl,
      ENVIRONMENT_KEY: StackConfiguration.environmentKey,
      ADDRESS_CLEANSE_URL: StackConfiguration.addressCleanseUrl,
      BULK_ADDRESS_CLEANSE_URL: StackConfiguration.bulkAddressCleanseUrl,
      ADDRESS_CLEANSE_SCOPE: StackConfiguration.addressCleanseScope,
      ADDRESS_CLEANSE_AUDIENCE: StackConfiguration.azureTokenUrl,
      ADDRESS_CLEANSE_CLIENT_ID: StackConfiguration.azureClientId,
      ADDRESS_CLEANSE_CLIENT_SECRET: StackConfiguration.azureClientSecret,
      LAUNCH_LOGIC_ACCESS_TOKEN_URL: StackConfiguration.launchLogicAccessTokenUrl,
      LAUNCH_LOGIC_URL: StackConfiguration.launchLogicUrl,
      PING_CLIENT_ID: StackConfiguration.pingClientId,
      PING_CLIENT_SECRET: StackConfiguration.pingClientSecret,
      FEATURE_MANAGER_SCOPE: StackConfiguration.featureManagerScope,
      EMAIL_SERVICE_URL: StackConfiguration.emailServiceUrl,
      AUDIT_LOG_SERVICE: StackConfiguration.auditLogServiceUrl,
      AUDIT_LOG_SERVICE_IDP_TOKEN_URL: StackConfiguration.getAuditLogServiceIdpParams().accessTokenUrl,
      AUDIT_LOG_SERVICE_IDP_SCOPE: StackConfiguration.getAuditLogServiceIdpParams().scope,
      AUDIT_LOG_SERVICE_IDP_AUDIENCE: StackConfiguration.getAuditLogServiceIdpParams().audience,
    };

    const securityGroup = new SecurityGroup(this, 'SG', {
      vpc,
      allowAllOutbound: true,
      description: StackConfiguration.name + '-sg',
      securityGroupName: StackConfiguration.name + '-sg',
    });

    //  Create function and reference handler in function folder
    const addCustomerPolicyLambda = this.createLambdaFunction(
      'addCustomerPolicy',
      {
        ...environment,
      },
      vpc,
      subnets,
      [securityGroup],
      lambdaRole,
      512,
      8,
      Tracing.ACTIVE
    );

    const batchLambdaRole = lambdaRole;
    batchLambdaRole.addManagedPolicy(ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaRole'));

    const addCustomerPolicyBatchLambda = this.createLambdaFunction(
      'addCustomerPolicyBatch',
      {
        ADD_CUSTOMER_POLICY_LAMBDA_NAME: StackConfiguration.isLocal
          ? 'addCustomerPolicy-v1-sandbox'
          : addCustomerPolicyLambda.lambda.functionName,
        ...environment,
      },
      vpc,
      subnets,
      [securityGroup],
      batchLambdaRole,
      1024,
      2,
    );
    const saveEcliqAccountNumberLambda = this.createLambdaFunction(
      'saveEcliqAccountNumber',
      environment,
      vpc,
      subnets,
      [securityGroup],
      lambdaRole,
    );
    const getEcliqAccountNumberLambda = this.createLambdaFunction(
      'getEcliqAccountNumber',
      environment,
      vpc,
      subnets,
      [securityGroup],
      lambdaRole,
    );
    const getCustomerPolicyLambda = this.createLambdaFunction(
      'getCustomerPolicy',
      environment,
      vpc,
      subnets,
      [securityGroup],
      lambdaRole,
    );
    const getCustomerAccountLambda = this.createLambdaFunction(
      'getCustomerAccount',
      environment,
      vpc,
      subnets,
      [securityGroup],
      lambdaRole,
    );
    const updateCustomerPolicyLambda = this.createLambdaFunction(
      'updateCustomerPolicy',
      environment,
      vpc,
      subnets,
      [securityGroup],
      lambdaRole,
    );

    const deleteCustomerAccountLambda = this.createLambdaFunction(
        'deleteCustomerAccount',
        environment,
        vpc,
        subnets,
        [securityGroup],
        lambdaRole,
    );

    kmsKey.grantEncryptDecrypt(addCustomerPolicyLambda.lambda);
    kmsKey.grantEncryptDecrypt(addCustomerPolicyBatchLambda.lambda);
    kmsKey.grantEncryptDecrypt(saveEcliqAccountNumberLambda.lambda);
    kmsKey.grantEncryptDecrypt(getEcliqAccountNumberLambda.lambda);
    kmsKey.grantEncryptDecrypt(getCustomerPolicyLambda.lambda);
    kmsKey.grantEncryptDecrypt(getCustomerAccountLambda.lambda);
    kmsKey.grantEncryptDecrypt(updateCustomerPolicyLambda.lambda);

    // Add encrypted secrets to lambda environment
    for (const key in StackConfiguration.secrets) {
      addCustomerPolicyLambda.lambda.addEnvironment(
        key,
        secretProvider.getEncryptedSecretVal(StackConfiguration.secrets[key], kmsKey),
      );
    }

    // Add permissions
    addCustomerPolicyLambda.lambda.addPermission('APIgatewayARNPermissions', {
      action: 'lambda:InvokeFunction',
      principal: new AnyPrincipal(),
      sourceArn: `arn:aws:execute-api:${Aws.REGION}:${Aws.ACCOUNT_ID}:*`,
    });

    addCustomerPolicyLambda.lambda.addPermission('LambdaInvokePermission', {
      action: 'lambda:InvokeFunction',
      principal: new AnyPrincipal(),
      sourceArn: `arn:aws:lambda:${Aws.REGION}:${Aws.ACCOUNT_ID}:*`,
    });

    addCustomerPolicyBatchLambda.lambda.addPermission('APIgatewayARNPermissions', {
      action: 'lambda:InvokeFunction',
      principal: new AnyPrincipal(),
      sourceArn: `arn:aws:execute-api:${Aws.REGION}:${Aws.ACCOUNT_ID}:*`,
    });

    addCustomerPolicyBatchLambda.lambda.addPermission('LambdaInvokePermission', {
      action: 'lambda:InvokeFunction',
      principal: new AnyPrincipal(),
      sourceArn: `arn:aws:lambda:${Aws.REGION}:${Aws.ACCOUNT_ID}:*`,
    });

    // Add certsLayer to all Lambda functions
    const lambdaFunctions = [
      addCustomerPolicyLambda.lambda,
      addCustomerPolicyBatchLambda.lambda,
      saveEcliqAccountNumberLambda.lambda,
      getEcliqAccountNumberLambda.lambda,
      getCustomerPolicyLambda.lambda,
      getCustomerAccountLambda.lambda,
      updateCustomerPolicyLambda.lambda,
      deleteCustomerAccountLambda.lambda,
    ];

    lambdaFunctions.forEach(lambda => {
      lambda.addLayers(LayerVersion.fromLayerVersionArn(this, `${lambda.functionName}-lm-internal-ca-layer`, this.certsLayer.latestVersionArn()));
      lambda.addEnvironment('NODE_EXTRA_CA_CERTS', '/opt/certs/lm-approved-certs.pem');
    });



    // Create API Gateway resources
    const apiGateway = this.createRestApiResources(vpc);

    // Define API Gateway root
    const apiResourceEndpoint = apiGateway.root.addResource(this.API_ENDPOINT);

    // Add API Gateway endpoints
    const policyParallelEndpoint = apiResourceEndpoint.addResource('policy');
    policyParallelEndpoint.addMethod(
      'POST',
      new apigw.LambdaIntegration(
        StackConfiguration.isLocal
          ? addCustomerPolicyBatchLambda.lambda
          : addCustomerPolicyBatchLambda.alias,
        {
          proxy: true
        }
      ),
    );

    const ecliqAccountNumberEndpoint = apiResourceEndpoint.addResource('ecliqAccountNumber');
    // POST /ecliqAccountNumber
    ecliqAccountNumberEndpoint.addMethod(
      'POST',
      new apigw.LambdaIntegration(saveEcliqAccountNumberLambda.alias, { proxy: true }),
    );
    // GET /ecliqAccountNumber
    ecliqAccountNumberEndpoint.addMethod(
      'GET',
      new apigw.LambdaIntegration(getEcliqAccountNumberLambda.alias, { proxy: true }),
    );

    const customerAccountEndpoint = apiResourceEndpoint.addResource('customerAccount');
    const customerAccountWithAccountIdEndpoint = customerAccountEndpoint.addResource('{accountId}');
    // GET /customerAccount/{accountId}
    customerAccountWithAccountIdEndpoint.addMethod(
      'GET',
      new apigw.LambdaIntegration(getCustomerAccountLambda.alias, { proxy: true }),
    );
    if (['test', 'development'].includes(StackConfiguration.environmentKey)) {

      customerAccountEndpoint.addMethod(
          'DELETE',
          new apigw.LambdaIntegration(deleteCustomerAccountLambda.alias, {proxy: true}),
          {
            requestModels: {
              'application/json': new apigw.Model(this, 'DeleteCustomerAccountModel', {
                restApi: apiGateway,
                contentType: 'application/json',
                schema: {
                  type: apigw.JsonSchemaType.OBJECT,
                  properties: {
                    customerIds: {
                      type: apigw.JsonSchemaType.ARRAY,
                      items: { type: apigw.JsonSchemaType.STRING },
                    },
                  },
                  required: ['customerIds'],
                },
              }),
            },
            methodResponses: [{ statusCode: '200' }],
          }
      );
      
      kmsKey.grantEncryptDecrypt(deleteCustomerAccountLambda.lambda);

      new CfnOutput(this, 'deleteCustomerAccountLambdaName', {
        value: deleteCustomerAccountLambda.lambda.functionName,
      });

      new CfnOutput(this, 'deleteCustomerAccountLambdaArn', {
        value: deleteCustomerAccountLambda.lambda.functionArn,
      });
    }


    const customerPolicyEndpoint = apiResourceEndpoint.addResource('customerPolicy');
    const customerPolicyWithPolicyIdEndpoint = customerPolicyEndpoint.addResource('{policyId}');
    // GET /customerPolicy/{policyId}
    customerPolicyWithPolicyIdEndpoint.addMethod(
      'GET',
      new apigw.LambdaIntegration(getCustomerPolicyLambda.alias, { proxy: true }),
    );
    // POST /customerPolicy
    customerPolicyEndpoint.addMethod(
      'POST',
      new apigw.LambdaIntegration(updateCustomerPolicyLambda.alias, { proxy: true }),
    );

    // Datadog Construct
    if (StackConfiguration.ddEnabled) {
      const datadog = new Datadog(this, 'Datadog', {
        nodeLayerVersion: 68,
        extensionLayerVersion: 17,
        enableDatadogTracing: true,
        enableMergeXrayTraces: false,
        enableDatadogLogs: true,
        apiKey: StackConfiguration.ddKey,
      });
      datadog.addLambdaFunctions([
        addCustomerPolicyLambda.lambda,
        addCustomerPolicyBatchLambda.lambda,
        saveEcliqAccountNumberLambda.lambda,
        getEcliqAccountNumberLambda.lambda,
      ]);

      new CfnOutput(this, StackConfiguration.name + ' - SWA Datadog Dashboard', {
        value:
          'https://app.datadoghq.com/dashboard/9cb-et9-v7c?tpl_var_Environment=' +
          StackConfiguration.environmentKey +
          '&tpl_var_Service=' +
          StackConfiguration.name,
      });
    }

    new CfnOutput(this, 'SWALambdaName', {
      value: addCustomerPolicyLambda.lambda.functionName,
    });

    new CfnOutput(this, 'SWALambdaArn', {
      value: addCustomerPolicyLambda.lambda.functionArn,
    });

    new CfnOutput(this, 'SaveEcliqAccountNumberLambdaName', {
      value: saveEcliqAccountNumberLambda.lambda.functionName,
    });

    new CfnOutput(this, 'SaveEcliqAccountNumberLambdaArn', {
      value: saveEcliqAccountNumberLambda.lambda.functionArn,
    });

    new CfnOutput(this, 'getEcliqAccountNumberLambdaName', {
      value: getEcliqAccountNumberLambda.lambda.functionName,
    });

    new CfnOutput(this, 'getEcliqAccountNumberLambdaArn', {
      value: getEcliqAccountNumberLambda.lambda.functionArn,
    });
  }

  public createAuthorizerFunction() {
    
    const allowedScopes = 'read,write';
    const scopeToResourceMap = `read:GET:/*,write:POST:/*,write:PUT:/*,write:PATCH:/*,write:DELETE:/*`;

    const authFn = new PythonFunction(this, 'Authorizer', {
      runtime: aws_lambda.Runtime.PYTHON_3_11,
      entry: __dirname.concat('/authorizer/runtime'),
      index: 'authorizer.py',
      bundling: {
        buildArgs: {
          PIP_INDEX_URL: 'https://repo.forge.lmig.com/api/pypi/python/simple',
          PIP_TRUSTED_HOST: 'pypi.org',
        },
      },
      logRetention: logs.RetentionDays.ONE_MONTH,
      timeout: Duration.seconds(31),
      memorySize: 256,
      environment: {
        RESOURCE_ALLOWED_ENTITLEMENTS: allowedScopes,
        GATEWAY_RESOURCE_ACCESS_RULES: scopeToResourceMap,
        PING_IDP_RESOURCE_ID: StackConfiguration.pingResourceId,
        PING_DEPLOY_ENV: StackConfiguration.getPingDeployEnvironment(),
      },
    });

    authFn.addLayers(LayerVersion.fromLayerVersionArn(this, `${authFn.functionName}-lm-internal-ca-layer`, this.certsLayer.latestVersionArn()));
    authFn.addEnvironment('REQUESTS_CA_BUNDLE', '/opt/certs/lm-approved-certs.pem');
    return authFn;
  }

  private createLambdaFunction(
    id: string,
    environment: { [key: string]: string },
    vpc: IVpc,
    subnets: ISubnet[],
    securityGroups: SecurityGroup[],
    lambdaRole: Role,
    memorySize?: number,
    provisionedConcurrentExecutions?: number,
    tracing?: Tracing,
  ): { lambda: NodejsFunction; alias: aws_lambda.IVersion } {
    const bundlingOptions = StackConfiguration.isLocal
      ? {
          sourceMap: true,
          minify: false,
          sourceMapMode: SourceMapMode.INLINE,
        }
      : { sourceMap: false, minify: true };

    const nodeFunc = new NodejsFunction(this, `${id}Function`, {
      bundling: {
        target: 'es2020',
        keepNames: true,
        ...bundlingOptions,
      },
      runtime: Runtime.NODEJS_22_X,
      handler: `${id}Lambda`, 
      entry: join(__dirname, 'runtime', `${id}Handler.ts`), 
      role: lambdaRole,
      logRetention: RetentionDays.TWO_WEEKS,
      logRetentionRole: lambdaRole,
      timeout: Duration.seconds(180),
      environment,
      vpc,
      vpcSubnets: {
        subnets: subnets,
      },
      securityGroups: securityGroups,
      functionName: `${id}-v1-${StackConfiguration.environmentKey}`,
      tracing: tracing ? tracing : Tracing.ACTIVE,
      description: `Handler for ${id} with version ${StackConfiguration.version}`,
      memorySize: memorySize ? memorySize : 128,
    });

    if (provisionedConcurrentExecutions && provisionedConcurrentExecutions > 0) {
      // Create an alias for the Lambda function
      const functionAlias = new Alias(this, `${id}LambdaAlias`, {
        aliasName: 'latest',
        version: nodeFunc.currentVersion,
        provisionedConcurrentExecutions, // Set the number of provisioned concurrency instances
      });
    }

    return {
      lambda: nodeFunc,
      alias: nodeFunc.currentVersion,
    };
  }

  private createRestApiResources(vpc: IVpc): apigw.RestApi {
    const apiAccessLogGroup = new logs.LogGroup(this, 'AccessLogGroup', {
      retention: logs.RetentionDays.ONE_MONTH,
    });

    // Add the vpc_owner_account_id property if deploying to an account with a shared VPC
    const vpcEndpoint = new VPCEndpointCustomResource(this, 'ExecuteApiVpcEndpointLookup', {
      serviceName: `com.amazonaws.${Aws.REGION}.execute-api`,
      vpcId: vpc.vpcId,
      tagFilters: {
        'Tag:lm_owner': 'cloud-services',
      },
    });

    const gatewayResourcePolicy = new iam.PolicyDocument({
      statements: [
        //Default statement
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          principals: [new iam.StarPrincipal()],
          actions: ['execute-api:Invoke'],
          resources: [`execute-api:/${StackConfiguration.environmentKey}/*`],
        }),
        // Deny all traffic that is not encrypted
        new iam.PolicyStatement({
          sid: 'EnforceTLS',
          effect: iam.Effect.DENY,
          principals: [new iam.StarPrincipal()],
          actions: ['execute-api:*'],
          resources: ['*'],
          conditions: {
            Bool: {
              'aws:SecureTransport': false,
            },
          },
        }),
      ],
    });

    // only set the authorizer for deployed env, we wont use it locally
    const defaultMethodOptions = StackConfiguration.isLocal
      ? {}
      : {
          authorizationType: apigw.AuthorizationType.CUSTOM,
          authorizer: new apigw.RequestAuthorizer(this, 'RequestAuthorizer', {
            handler: this.createAuthorizerFunction(),
            identitySources: [apigw.IdentitySource.header('Authorization')],
          }),
        };

    const apiGateway = new apigw.RestApi(this, `${StackConfiguration.name}-api`, {
      cloudWatchRole: false,
      defaultMethodOptions,
      policy: gatewayResourcePolicy,
      deployOptions: {
        accessLogDestination: new apigw.LogGroupLogDestination(apiAccessLogGroup),
        accessLogFormat: apigw.AccessLogFormat.jsonWithStandardFields({
          caller: true,
          ip: true,
          httpMethod: true,
          protocol: true,
          requestTime: true,
          resourcePath: true,
          responseLength: true,
          status: true,
          user: true,
        }),
        metricsEnabled: true,
        dataTraceEnabled: false,
        loggingLevel: apigw.MethodLoggingLevel.INFO,
        stageName: StackConfiguration.environmentKey,
      },
    });

    const cfnRestApi = apiGateway.node.defaultChild as apigw.CfnRestApi;
    cfnRestApi.endpointConfiguration = {
      types: [apigw.EndpointType.PRIVATE],
      vpcEndpointIds: [vpcEndpoint.ref],
    };

    new CfnOutput(this, 'ExecuteApiUrl', {
      value: `https://${apiGateway.restApiId}-${vpcEndpoint.ref}.execute-api.${Aws.REGION}.amazonaws.com/${apiGateway.deploymentStage.stageName}/`,
      description: 'URL to hit the api gateway through the account vpc, without providing the x-apigw-api-id header',
    });

    return apiGateway;
  }
}
