import Request  from "../../../types/request";
import { logger } from './logger';
import axios from 'axios';

export class EmailHelper {

    static buildEmailRequest = (requests: Request[], subject: String) => {
        const emailRequest: any = {
            toList: [
                '<EMAIL>'
            ],
            subject: subject,
            body: JSON.stringify(requests),
        }
        return emailRequest;
    }

    static async sendEmail(url: string, emailRequest: any){
        try{
            return await axios.post(url, emailRequest);
        }catch (error) {
            logger.error('Exception occurred while sending email', error.message);
            return error;
        }
    }
}
