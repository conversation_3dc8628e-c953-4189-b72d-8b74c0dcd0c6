import axios from 'axios';
import { setupAxiosRetry } from './axiosRetryHelper';
import {
  ResponseAddress,
  AddressDetails,
  UnparsedAddressRequest,
  MultipleUnparsedAddressRequest,
  Address,
} from '../../../model/addressModels';
import { getCustomerAccountClientAuthToken } from './authenticationTokenHelper';
import { logger } from './logger';
import { CustomerMetadata } from '../../../types/request';

export class AddressHelper {
  private static async addressCleanseToken(): Promise<string> {
    const addrClientId = process.env.ADDRESS_CLEANSE_CLIENT_ID || '';
    const addrClientSecret = process.env.ADDRESS_CLEANSE_CLIENT_SECRET || '';
    const addrAudience = process.env.ADDRESS_CLEANSE_AUDIENCE || '';
    const addrScope = process.env.ADDRESS_CLEANSE_SCOPE || '';

    return getCustomerAccountClientAuthToken(addrClientId, addrClientSecret, addrAudience, addrScope);
  }

  static async bulkCleanseAddresses(addresses: { [key: string]: CustomerMetadata }): Promise<any> {
    const customerAccountToken = await AddressHelper.addressCleanseToken();

    const addressKeys = Object.keys(addresses);
    const addressesValues = Object.values(addresses);

    const responseAddresses = (await AddressHelper.sendAddressCleanseRequest(
      createMultipleUnparsedAddressRequest(addressesValues),
      process.env.BULK_ADDRESS_CLEANSE_URL || '',
      customerAccountToken,
    )) as ResponseAddress;

    if (responseAddresses.addresses.length !== addressKeys.length) {
      // address cleanse can return multiple addresses for a single input
      // ex: 1140 Whitewater Rd -> 1140 E Whitewater Rd and 1140 W Whitewater Rd
      // checking that the # of unique addressRequestId values is the correct size
      const addressRequestIds = new Set();
      responseAddresses.addresses.forEach(address => {
        addressRequestIds.add(address.addressRequestId);
      });
      if (addressRequestIds.size !== addressKeys.length) {
        throw new Error(
          `Number of addresses returned ${addressRequestIds.size} does not match number of addresses sent ${addressKeys.length}`,
        );
      }
    }

    const cleansedAddresses: { [key: string]: AddressDetails } = {};

    for (const address of responseAddresses.addresses) {
      const addressDetails = AddressHelper.addressDetailsFromResponse(address.address);
      cleansedAddresses[addressKeys[parseInt(address.addressRequestId) - 1]] = addressDetails;
    }

    return cleansedAddresses;
  }

  static async processAddress(
    addressLine: string,
    addressLine2: string,
    city: string,
    state: string,
    zipcode: string,
    countryCode: string = 'US',
  ): Promise<any> {
    let customerMetadata: any = {};

    try {
      const customerAccountToken = await AddressHelper.addressCleanseToken();

      const unparsedAddressRequest: UnparsedAddressRequest = createUnparsedAddressRequest(
        addressLine,
        createAddressLastLine(city, state, zipcode),
        countryCode,
        addressLine2 || undefined,
      );

      const responseAddress = (await AddressHelper.sendAddressCleanseRequest(
        unparsedAddressRequest,
        process.env.ADDRESS_CLEANSE_URL || '',
        customerAccountToken,
      )) as ResponseAddress;

      // Extract and return relevant fields
      AddressHelper.assignAddressDetailsToCustomerMetadata(
        customerMetadata,
        AddressHelper.addressDetailsFromResponse(responseAddress.addresses[0].address),
      );
    } catch (error: any) {
      // Log error details
      logger.error('Error in address cleanse method execution: ', error.message);
      if (error.response) {
        logger.error('Error response data:', error.response.data);
        logger.error('Error response status:', error.response.status);
        logger.error('Error response headers:', error.response.headers);
      } else if (error.request) {
        logger.error('Error request data:', error.request);
      } else {
        logger.error('Error message:', error.message);
      }
    }
    return customerMetadata;
  }

  static async assignAddressDetailsToCustomerMetadata(customerMetadata: CustomerMetadata, address: AddressDetails) {
    customerMetadata.address1 = address.addressLine;
    customerMetadata.address2 = address.unit ? `${address.unit.unitType} ${address.unit.unitNumber}` : '';
    customerMetadata.city = address.city;
    customerMetadata.state = address.state;
    customerMetadata.zip = address.postalCodeInfo.postalCode;
  }

  // visible for tests
  static addressDetailsFromResponse(address: Address): AddressDetails {
    return {
      addressLine: address.addressLine,
      city: address.city,
      state: address.state,
      postalCodeInfo: address.postalCodeInfo,
      unit: address.unit,
      houseNumber: address.houseNumber,
    };
  }

  private static async sendAddressCleanseRequest(
    inputBodyData: UnparsedAddressRequest | MultipleUnparsedAddressRequest,
    url: string,
    token: string,
  ): Promise<any> {
    const addressRequestConfig = {
      method: 'post',
      maxBodyLength: Infinity,
      url: url,
      headers: {
        Authorization: `Bearer ${token}`,
      },
      data: JSON.stringify(inputBodyData),
    };

    setupAxiosRetry(axios);

    logger.info('Sending request to Address cleanse');
    const reusifyResponse = await axios.request(addressRequestConfig);
    logger.info('Received response from Address cleanse');
    // Log the request for granularity
    logger.info('STATUS', reusifyResponse.status, reusifyResponse.statusText);

    return reusifyResponse.data;
  }
}

export const pipeDelimitedAddress = (customerMetadata: CustomerMetadata) => {
  return `${customerMetadata.address1}|${customerMetadata.address2 ?? ''}|${customerMetadata.city}|${
    customerMetadata.state
  }|${customerMetadata.zip}`;
};

// exported only for tests
export const createMultipleUnparsedAddressRequest = (addresses: CustomerMetadata[]): MultipleUnparsedAddressRequest => {
  return {
    unparsedAddresses: addresses.map(
      (address) =>
        createUnparsedAddressRequest(
          address.address1,
          createAddressLastLine(address.city, address.state, address.zip),
          'US',
          address.address2,
        ).unparsedAddress,
    ),
  };
};

// exported only for tests
export const createUnparsedAddressRequest = (
  addressLine: string,
  addressLastLine: string,
  countryCode: string,
  addressLine2?: string,
): UnparsedAddressRequest => {
  const unparsedAddress: UnparsedAddressRequest = {
    unparsedAddress: {
      addressLine,
      addressLastLine,
      countryCode,
    },
  };
  if (addressLine2) {
    unparsedAddress.unparsedAddress.addressLine2 = addressLine2;
  }
  return unparsedAddress;
};

// exported only for tests
export const createAddressLastLine = (city: string, state: string, zipcode: string): string => {
  return `${city}, ${state} ${zipcode}`;
};