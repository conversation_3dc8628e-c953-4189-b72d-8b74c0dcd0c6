import {
    AuditLogService,
    AuditLogServiceParams,
    Transaction,
    TransactionEvent,
    EndTransactionRequest,
    StartTransactionRequest,
    TransactionEventRequest,
    TransactionResponse,
    TransactionStatus
} from "@lmig/audit-log-node-starter";

export class AuditLogHelper {
    readonly auditLogService: AuditLogService | null;

    AuditLogHelper() {
    }

    private constructor(auditLogService: AuditLogService | null) {
        this.auditLogService = auditLogService;
    }


    public static async init() {
        let auditLogService = null;
        try {
            auditLogService = await AuditLogService.initialize(await this.getParams());
        } catch (error) {
            console.log(error);
        }

        return new AuditLogHelper(auditLogService);
    }

    static getParams = async (): Promise<AuditLogServiceParams> => {
        const params: AuditLogServiceParams = {
            accessTokenUrl: process.env.AUDIT_LOG_SERVICE_IDP_TOKEN_URL || '',
            audience: process.env.AUDIT_LOG_SERVICE_IDP_AUDIENCE || '',
            scope: (process.env.AUDIT_LOG_SERVICE_IDP_SCOPE || '').replace(/,/g, ' '),
            clientId: process.env.PING_CLIENT_ID || '',
            clientSecret: process.env.PING_CLIENT_SECRET || '',
            environment: process.env.ENVIRONMENT_KEY || '',
        } as AuditLogServiceParams;
        return params;
    };

    buildStartTransactionRequest = (
        typeValue: string,
        transactionMetaInfo: Object
    ): StartTransactionRequest => {
        return {
            type: typeValue,
            metaInfo: {transactionMetaInfo: transactionMetaInfo}
        } as StartTransactionRequest;
    };

    buildTransactionEventRequest = (
        name: string,
        status: TransactionStatus,
        eventTimeStamp: Date,
        eventMetaInfo: Object
    ): TransactionEventRequest => {
        return {
            name: name,
            status: status,
            eventTimeStamp: eventTimeStamp || null,
            metaInfo: {eventMetaInfo: eventMetaInfo},
        } as TransactionEventRequest;
    };

    buildEndTransactionRequest = (
        status: TransactionStatus,
        eventMetaInfo: Object
    ): EndTransactionRequest => {
        return {
            status: status,
            metaInfo: {eventMetaInfo: eventMetaInfo}
        } as EndTransactionRequest;
    };

    buildTransactionEvent = (
        name: string,
        status: TransactionStatus,
        eventTimeStamp: Date,
        eventMetaInfo: Object): TransactionEvent => {
        return {
            name: name,
            status: status,
            eventTimeStamp: eventTimeStamp || new Date(),
            endTimeStamp: eventTimeStamp || new Date(),
            metaInfo: {eventMetaInfo: eventMetaInfo}
        } as TransactionEvent;
    };

    buildTransactionRequest = (
        id: string,
        type: string,
        status: TransactionStatus,
        events: TransactionEvent[],
        transactionMetaInfo: Object): Transaction => {

        return {
            id: id,
            version: 1,
            startDateTime: new Date(),
            lastModifiedDate: new Date(),
            type: type,
            status: status,
            events: events,
            metaInfo: {transactionMetaInfo: transactionMetaInfo}
        } as Transaction;
    };

    async createTransaction(transactionRequest: Transaction): Promise<Transaction|null> {
        try {
            return await this.auditLogService!.createTransaction(transactionRequest);
        }catch (error) {
            console.log(error);
            return Promise.resolve(null);
        }
      }

    async startTransaction(startTransactionRequest: StartTransactionRequest): Promise<TransactionResponse|null> {
        try {
            return await this.auditLogService!.startTransaction(startTransactionRequest);
        }catch (error) {
            console.log(error);
            return new Promise(resolve => resolve(null));
        }
      }

      async startTransactions(startTransactionRequests: StartTransactionRequest[]): Promise<TransactionResponse[]|null> {
        try {
            return await this.auditLogService!.startTransactions(startTransactionRequests);
        }catch (error) {
            console.log(error);
            return new Promise(resolve => resolve(null));
        }
      }


    async addTransactionEvent(transactionId: string, transactionEventRequest: TransactionEventRequest): Promise<TransactionResponse | null> {
        try {
            return await this.auditLogService!.addTransactionEvent(transactionId, transactionEventRequest);
        } catch (error) {
            console.log(error);
            return new Promise(resolve => resolve(null));
        }
    }

    async endTransaction(transactionId: string, endTransactionRequest: EndTransactionRequest): Promise<TransactionResponse | null> {
        try {
            return await this.auditLogService!.endTransaction(transactionId, endTransactionRequest);
        } catch (error) {
            console.log(error);
            return new Promise(resolve => resolve(null));
        }
    }

    async endTransactions(endTransactionRequests: Map<string, EndTransactionRequest>): Promise<TransactionResponse[] | null> {
        try {
            return await this.auditLogService!.endTransactions(endTransactionRequests);
        } catch (error) {
            console.log(error);
            return new Promise(resolve => resolve(null));
        }
    }


}