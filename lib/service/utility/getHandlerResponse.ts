import { APIGatewayProxyResult } from 'aws-lambda';

export const getHandlerResponse = (
  statusCode: number,
  body: any,
  headers?: { [header: string]: string | number | boolean },
  isBase64Encoded?: boolean,
  multiValueHeaders?: { [header: string]: (string | number | boolean)[] },
): APIGatewayProxyResult => {
  return {
    body: typeof body === 'string' ? body : JSON.stringify(body),
    statusCode,
    headers,
    isBase64Encoded,
    multiValueHeaders,
  };
};
