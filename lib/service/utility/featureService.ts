import axios from "axios";
import { setupAxiosRetry } from './axiosRetryHelper';
import { getCustomerAccountClientAuthToken } from "./authenticationTokenHelper";
import { logger } from "./logger";

// Define the interface for FeatureLaunchRequest
interface FeatureLaunchRequest {
  featureNames: string[];
  identifier: string;
}

export class FeatureService {
  static async checkFeatureLaunch (
    featureNames: string[],
    identifier: string
  ): Promise<boolean> {
    try {
      // Changed environment variables to config file
      const launchLogicEndpoint = process.env.LAUNCH_LOGIC_URL || '';
      const pingClientId = process.env.PING_CLIENT_ID || '';
      const pingClientSecret = process.env.PING_CLIENT_SECRET || '';
      const launchLogicAccessTokenUrl = process.env.LAUNCH_LOGIC_ACCESS_TOKEN_URL || '';
      const featureScope = process.env.FEATURE_MANAGER_SCOPE || '';
      const featureManagerToken = await getCustomerAccountClientAuthToken(
        pingClientId,
        pingClientSecret,
        launchLogicAccessTokenUrl,
        featureScope
      );

      const inputBodyData: FeatureLaunchRequest = {
        featureNames,
        identifier
      };

      const featureRequestConfig = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${launchLogicEndpoint}/feature/islaunched`,
        headers: {
          Authorization: `Bearer ${featureManagerToken}`,
          'Content-Type': 'application/json'
        },
        data: JSON.stringify(inputBodyData),
      };

      setupAxiosRetry(axios);

      // Use featureRequestConfig to make the axios call
      const response = await axios(featureRequestConfig);

      // Extract the flag from the response
      const featureName = featureNames[0];
      const isLaunched = response.data[featureName];
      logger.debug('Feature launch response', response.data);
      return isLaunched;

    } catch (error: any) {
      logger.error('Error checking feature launch', error);
      throw error;   
    }
  }
}
