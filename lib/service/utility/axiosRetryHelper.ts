// axiosRetryHelper.ts
import axios from 'axios';
import axiosRetry from 'axios-retry';

export function setupAxiosRetry(axiosInstance: typeof axios) {
    axiosRetry(axiosInstance, {
        retries: 3, // Number of retry attempts
        retryDelay: (retryCount) => {
            return retryCount * 1000; // Time between retries in milliseconds
        },
        retryCondition: (error) => {
            return error.response?.status === 403; // Retry only on 403 status code
        },
    });
}