export const removeNonNumericChars = (input: string | null | undefined): string => {
  // keep numeric values only
  return typeof input === 'string' ? input?.replace(/[^0-9]/g, '') : '';
};

export const removeSpecialChars = (input: string | null | undefined): string => {
  if (typeof input !== 'string') {
    return '';
  }

  // Remove special characters
  let sanitized = input.replace(/[^a-zA-Z0-9\s]/g, '');

  // Trim leading and trailing spaces, Replace multiple spaces with a single space.
  sanitized = sanitized.replace(/\s+/g, ' ').trim();

  return sanitized;
};
