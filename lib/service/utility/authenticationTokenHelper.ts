import url = require('url');
import axios, {AxiosResponse} from 'axios';
import {logger} from './logger';

export const getCustomerAccountClientAuthToken = async (
    clientId: string,
    clientSecret: string,
    idpUrl: string,
    scopes: string
): Promise<string> => {
    const params = new url.URLSearchParams({
        grant_type: 'client_credentials',
        client_id: clientId,
        client_secret: clientSecret,
        scope: scopes,
    });

  try {
      console.log('Getting token from IDP');
      const tokenResponse: AxiosResponse = await axios.post(`${idpUrl}`, params.toString());
      if (tokenResponse.status != 200) {
          const message = 'Unexpected response getting token';
          console.error({ message, tokenResponse });
          logger.error(message);
      }
      return tokenResponse.data.access_token;
  } catch (error) {
      logger.error('Error occurred while getting token', { error, idpUrl, params: params.toString() });
      throw error;
  }
}
