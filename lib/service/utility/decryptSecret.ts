import { KMS } from "@aws-sdk/client-kms";

export const decryptSecret = async (encryptedValue: string) => {
  const params = {
    CiphertextBlob: Buffer.from(encryptedValue, 'base64'),
  };
  const decryptionResult = await new KMS().decrypt(params);
  if (!decryptionResult.Plaintext) {
    throw new Error(`Error decrypting value ${decryptionResult}`);
  }
  return new TextDecoder().decode(decryptionResult.Plaintext);
};