import { CustomerAccountSearchCriteria } from '../../../types/request';
import CustomerAccountModel, { CustomerPolicyModel } from '../../../model/customerAccountModel';
import { WithId } from 'mongodb';
import { Logger } from '@aws-lambda-powertools/logger';
import {
  searchForAccountByIdOrEcliqAccountNumberOrQuoteEntryId,
  searchForAccountByCustomerMetadata,
  searchForAccountByPriorCarrierPolicyNumberAndBtCode
} from '../runtime/mongo';

import { Tracer } from '@aws-lambda-powertools/tracer';

const serviceName = 'MoveOpportunitiesCustomerAccountSearch';
const logger = new Logger({ serviceName });
const tracer = new Tracer({ serviceName });

export class MoveOpportunitiesCustomerAccountSearch {

  constructor(){}
  
  /**
   * Search for the existing customer account for AQE Move Opportunity requires specialized handling
   * Move Opportunity deals with 2 different BT codes, Original BT and New BT code
   * Along with the existing search criteria this will perform an additional search criteria to identify
   * if an account is found based on Original BT Code or New BT code
   * The insert or update to the customer account is identified based on
   * if the customer account is found with the Original BT code or
   * if the customer account is found with the New BT code.
   * 
   * @param searchCriteria
   * @param foundCriteria 
   * @returns 
   */
  @tracer.captureMethod({ subSegmentName: 'searchForExistingCustomerAccount' })
  public async searchForExistingCustomerAccount(searchCriteria: CustomerAccountSearchCriteria,
    foundCriteria: { value: string }): Promise<WithId<CustomerAccountModel>[]> {

    let result: WithId<CustomerAccountModel>[] = [];

    //BT code in searchCriteria is different from searchCriteria.customerMetadata when move opportunity is performed in AQE
    let newBtCode = null;
    if (
      searchCriteria.btCode &&
      searchCriteria.customerMetadata &&
      searchCriteria.customerMetadata.btCode &&
      searchCriteria.btCode !== searchCriteria.customerMetadata.btCode
    ) {
      newBtCode = searchCriteria.customerMetadata.btCode;
      searchCriteria.customerMetadata.btCode = searchCriteria.btCode;
    }

    if (searchCriteria.customerAccountId || searchCriteria.ecliqAccountNumber || searchCriteria.quoteEntryId) {
      logger.info(`Searching for customer account by customerAccountId or ecliqAccountNumber or quoteEntryId`);
      foundCriteria.value = 'customerAccountIdOrEcliqAccountNumberOrQuoteEntryId';
      result = await searchForAccountByIdOrEcliqAccountNumberOrQuoteEntryId(
      searchCriteria.customerAccountId,
      searchCriteria.ecliqAccountNumber,
      searchCriteria.quoteEntryId,
      );
    }

    // check using priorCarrierPolicyNumber
    if (result.length === 0 && searchCriteria.priorCarrierPolicyNumber && searchCriteria.btCode) {
      logger.info(`Searching for customer account by priorCarrierPolicyNumber and btCode`,);
      foundCriteria.value = 'priorCarrierPolicyNumberAndOriginalBtCode';
      result = await searchForAccountByPriorCarrierPolicyNumberAndBtCode(
        searchCriteria.priorCarrierPolicyNumber,
        Number(searchCriteria.btCode),
      );
    }


    if ((!result || result.length === 0) && searchCriteria.customerMetadata && newBtCode) {
      logger.info(`Searching for customer account by customerMetadata`);
      foundCriteria.value = 'customerMetadataAndOriginalBtCode';
      result = await searchForAccountByCustomerMetadata(searchCriteria.customerMetadata);
      searchCriteria.customerMetadata.btCode = newBtCode;
    }
    
    if ((!result || result.length === 0) && searchCriteria.customerMetadata) {
      logger.info(`Searching for customer account by customerMetadata with new btCode`);
      foundCriteria.value = 'customerMetadataAndNewBtCode';
      result = await searchForAccountByCustomerMetadata(searchCriteria.customerMetadata);
      foundCriteria.value = '';
    }

    if(result && result.length > 0){
      let additionalSearchResult: WithId<CustomerAccountModel>[] = [];
      logger.info(`Additional searching for customer account when BT codes are not matching`);
      if (foundCriteria.value === 'priorCarrierPolicyNumberAndOriginalBtCode' || 
        foundCriteria.value === 'customerMetadataAndOriginalBtCode') {
        if (newBtCode && searchCriteria.customerMetadata) {
          logger.info(`Additional searching for customer account by customerMetadata and new BT code`);
          searchCriteria.customerMetadata.btCode = newBtCode;
          additionalSearchResult = await searchForAccountByCustomerMetadata(searchCriteria.customerMetadata);
          if (additionalSearchResult.length > 0) {
            foundCriteria.value = 'customerMetadataAndNewBtCode';
          }
        }
      } else {
        if (searchCriteria.priorCarrierPolicyNumber && searchCriteria.btCode) {
          logger.info(`Additional searching for customer account by priorCarrierPolicyNumber and original btCode`,);
          additionalSearchResult = await searchForAccountByPriorCarrierPolicyNumberAndBtCode(
            searchCriteria.priorCarrierPolicyNumber,
            Number(searchCriteria.btCode),
          );
          if(additionalSearchResult.length > 0){
            foundCriteria.value = 'priorCarrierPolicyNumberAndOriginalBtCode';
          }
        }

        if (
          additionalSearchResult.length === 0 &&
          searchCriteria.btCode &&
          searchCriteria.customerMetadata &&
          searchCriteria.customerMetadata.btCode
        ) {
          logger.info(`Additional searching for customer account by customerMetadata and original BT code`);
          searchCriteria.customerMetadata.btCode = searchCriteria.btCode;
          additionalSearchResult = await searchForAccountByCustomerMetadata(searchCriteria.customerMetadata);
          if (additionalSearchResult.length > 0) {
            foundCriteria.value = 'customerMetadataAndOriginalBtCode';
          }
        }
        if (searchCriteria.customerMetadata && newBtCode) {
          logger.info(`Additional searching for customer account by customerMetadata and new BT code`);
          searchCriteria.customerMetadata.btCode = newBtCode;
          additionalSearchResult = await searchForAccountByCustomerMetadata(searchCriteria.customerMetadata);
          if (additionalSearchResult.length > 0) {
            foundCriteria.value = 'customerMetadataAndNewBtCode';
            return additionalSearchResult;
          }
        }
      }
    }
    
    return result;
  }
}

const moveOppAccountSearchClass = new MoveOpportunitiesCustomerAccountSearch();
const moveOppAccountSearch = moveOppAccountSearchClass.searchForExistingCustomerAccount.bind(moveOppAccountSearchClass);
export { moveOppAccountSearch };