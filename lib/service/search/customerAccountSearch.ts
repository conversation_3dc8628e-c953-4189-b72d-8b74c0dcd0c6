import { CustomerAccountSearchCriteria } from '../../../types/request';
import CustomerAccountModel from '../../../model/customerAccountModel';

import {
  searchForAccountByCustomerMetadata,
  searchForAccountByIdOrEcliqAccountNumberOrQuoteEntryId,
  searchForAccountByPriorCarrierPolicyNumberAndBtCode,
} from '../runtime/mongo';
import { WithId } from 'mongodb';
import { Logger } from '@aws-lambda-powertools/logger';
import { Tracer } from '@aws-lambda-powertools/tracer';

const serviceName = 'DefaultCustomerAccountSearch';
const logger = new Logger({ serviceName });
const tracer = new Tracer({ serviceName });

export class DefaultCustomerAccountSearch {

  constructor(){}

  @tracer.captureMethod({ subSegmentName: 'searchForExistingCustomerAccount' })
  public async searchForExistingCustomerAccount(searchCriteria: CustomerAccountSearchCriteria,
    foundCriteria: { value: string }): Promise<WithId<CustomerAccountModel>[]> {
    
    let result: WithId<CustomerAccountModel>[] = [];

    if (searchCriteria.customerAccountId || searchCriteria.ecliqAccountNumber || searchCriteria.quoteEntryId) {
      logger.info(`Searching for customer account by customerAccountId or ecliqAccountNumber or quoteEntryId`);
      result = await searchForAccountByIdOrEcliqAccountNumberOrQuoteEntryId(
      searchCriteria.customerAccountId,
      searchCriteria.ecliqAccountNumber,
      searchCriteria.quoteEntryId,
      );
    }

    // check using priorCarrierPolicyNumber
    if (result.length === 0 && searchCriteria.priorCarrierPolicyNumber && searchCriteria.btCode) {
      logger.info(`Searching for customer account by priorCarrierPolicyNumber and btCode`,);
      foundCriteria.value = 'priorCarrierPolicyNumberAndOriginalBtCode';
      result = await searchForAccountByPriorCarrierPolicyNumberAndBtCode(
        searchCriteria.priorCarrierPolicyNumber,
        Number(searchCriteria.btCode),
      );
    }

    // check using customerMetadata
    if ((!result || result.length === 0) && searchCriteria.customerMetadata) {
      logger.info(`Searching for customer account by customerMetadata`);
      result = await searchForAccountByCustomerMetadata(searchCriteria.customerMetadata);
      logger.info(`Found customer account by customerMetadata: ${result.length} results`);
    }

    return result;
  }
}

const defaultOppAccountSearchClass = new DefaultCustomerAccountSearch();
const defaultOppAccountSearch = defaultOppAccountSearchClass.searchForExistingCustomerAccount.bind(defaultOppAccountSearchClass);
export { defaultOppAccountSearch };