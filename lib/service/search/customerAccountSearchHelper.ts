import { CustomerAccountSearchCriteria } from '../../../types/request';

export class CustomerAccountSearchHelper {
      //AQE Move Opportunities changes
    public static isNotBtCodeMatching(searchCriteria: CustomerAccountSearchCriteria) {
        return (
        searchCriteria.btCode &&
        searchCriteria.customerMetadata &&
        searchCriteria.customerMetadata.btCode &&
        searchCriteria.btCode !== searchCriteria.customerMetadata.btCode
        );
    }
}