from typing import Dict, Any
from lambda_token_authorizer import GatewayAuthorizer, JwtValidator
from aws_lambda_powertools import Logger
from aws_lambda_powertools.utilities.typing import LambdaContext


logger = Logger("Authorizer")
validator = JwtValidator().with_ping_support()


@logger.inject_lambda_context(log_event=True)
def handler(event: Dict[str, Any], _: LambdaContext):
    """
    Make sure the following env variables are set, or pass a config object.
    See class docs for expected env variable content.

    RESOURCE_ALLOWED_ENTITLEMENTS

    Make sure the following env variables are set, or pass a config object.
    See method docs for expected env variable content.

    PING_IDP_RESOURCE_ID
    PING_DEPLOY_ENV

    Make sure the following env variables are set, or pass a config object.
    See method docs for expected env variable content.

    GATEWAY_RESOURCE_ACCESS_RULES

    generate an IAM Policy-based lambda response that's compliant with API Gateway
    lambda authorizers.

    This return value can be added augmented as needed.

    For example, you can add in a `context` key with additional user context,
    or the encoded/decoded jwt token. This context will be passed to your backend
    lambda functions.
    """
    global validator
    try:
        parsed_jwt = validator.validate(event['headers']['Authorization'])

        return GatewayAuthorizer.from_lambda_event(event).to_lambda_response(parsed_jwt)
    except Exception as ex:
        logger.exception("Got an exception during auth %s", ex)
        raise Exception("Unauthorized") from ex
