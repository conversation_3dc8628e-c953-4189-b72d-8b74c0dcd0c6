import { Aspects, Aws, Stage, Stack } from "aws-cdk-lib";
import { Construct } from "constructs";
import { LMBaseStack, LmGlobalDenyAspect, LmPermissionsBoundaryType } from "@lmig/swa-cdk-core";
import { ServiceResources } from "../lib/service/infrastructure";
import { StackConfiguration } from "./config";


export class AppStack {
    constructor(scope: Construct, id: string) {

        const cdkStack = new LMBaseStack(scope, id, {
            stackName: StackConfiguration.name,
            lmPermissionsBoundaryType: LmPermissionsBoundaryType.ORGANIZATION_GUID,
            env: {
                account: Aws.ACCOUNT_ID,
                region: Aws.REGION
            },
            tags: StackConfiguration.stackTags
        });

        const serviceResources = new ServiceResources(cdkStack, 'Service');

        Aspects.of(cdkStack).add(new LmGlobalDenyAspect())
    }
}