import { SwaDefaultStackTags } from "@lmig/swa-cdk-core";

const isLocal = process.env.IS_LOCAL === 'true';
const artifactKey = (process.env.bamboo_forge_artifact_key || "book-transfer-customer-account-service");
const appName = artifactKey + "-" + (process.env.bamboo_forge_environment_key || "sandbox");
const environmentKey = process.env.bamboo_forge_environment_key || 'sandbox';
const version = process.env.bamboo_result_version || "1.0.0" + "-" + "SNAPSHOT";
const tags = new SwaDefaultStackTags();

// Adding Datadog specific tags
tags.addTags({
    service: appName,
    env: environmentKey,
    version: version
});

const getMongoUrl = (): string => {
    switch (environmentKey) {
        case 'sandbox':
        case 'development':
            return `mongodb+srv://${process.env.bamboo_secret_bt_general_mongo_development_read_write_short_username}:${process.env.bamboo_secret_bt_general_mongo_development_read_write_short_password}@bt-general-mongo-development-pl-0.vo783.mongodb.net/BT?retryWrites=true&w=majority`;
            break;
        case 'unit-test':
        case 'test':
        case 'non-production':
            return `mongodb+srv://${process.env.bamboo_secret_bt_general_mongo_test_read_write_username}:${process.env.bamboo_secret_bt_general_mongo_test_read_write_password}@bt-general-mongo-nonprod-pl-0.qz15w.mongodb.net/BT?retryWrites=true&w=majority`;
            break;
        case 'production':
            return `mongodb+srv://${process.env.bamboo_secret_bt_general_mongo_production_read_write_username}:${process.env.bamboo_secret_bt_general_mongo_production_read_write_password}@bt-general-mongo-production-pl-0.i5vew.mongodb.net/BT?retryWrites=true&w=majority`;
            break;
        default:
            return '';
            break;
    }
};

const getEmailServiceURL = (): string => {
    if(environmentKey === 'production'){
        return 'https://emailservice.pdc.paas.lmig.com/v2/sendemail';
    }else{
        return 'https://emailservice-test.pdc.np.paas.lmig.com/v2/sendemail';
    }
}

// Environment variables that are not secrets/sensitive
const environment: { [key: string]: string } = {
    DD_ENV: environmentKey, //This sets the env tag in Datadog. Use it to separate out your staging, development, and production environments.
    DD_SERVICE: appName, //This sets the service tag in Datadog. Use it to group related Lambda functions into a service.
    DD_VERSION: version, //This sets the version tag in Datadog. Use it to enable Deployment Tracking.
    IS_LOCAL: String(isLocal)
};

// Key is environment variable name to use within lambda code, and value is the secret name in papi/console.forge.lmig.com
const secrets: { [key: string]: string } = {};
export const StackConfiguration = {
    version: version,

    environment,
    secrets,

    //Datadog Key
    ddKey: process.env["bamboo_secret_global_datadog_us_apikey"] || "",
    //Enable Datadog - set to true to enable Datadog observability or false to disable
    ddEnabled: !isLocal, // Disable Datadog for local development

    stackTags: tags.tags,

    // LM metadata
    name: appName,
    environmentKey: environmentKey,
    mongoDbConnection: getMongoUrl(),
    
    pingResourceId: process.env.bamboo_oauth_api_id || 'api-gw-client',
    isLocal: isLocal,

    //Microsoft Azure Token Credentials
    azureTokenUrl: 'https://login.microsoftonline.com/08a83339-90e7-49bf-9075-957ccd561bf1/oauth2/v2.0/token',
    azureClientId: environmentKey === 'production'
        ? process.env.bamboo_secret_book_transfer_customer_account_service_azure_client_id || exports.NOT_FOUND_SECRET
        : environmentKey === 'test'
            ? process.env.bamboo_secret_book_transfer_customer_account_service_azure_client_test_id || exports.NOT_FOUND_SECRET
            : environmentKey === 'sandbox' 
              ? process.env.bamboo_secret_book_transfer_customer_account_service_azure_client_sandbox_id || exports.NOT_FOUND_SECRET
              : process.env.bamboo_secret_book_transfer_customer_account_service_azure_client_development_id || exports.NOT_FOUND_SECRET,
    azureClientSecret: environmentKey === 'production'
        ? process.env.bamboo_secret_book_transfer_customer_account_service_azure_client_secret || exports.NOT_FOUND_SECRET
        : environmentKey === 'test'
            ? process.env.bamboo_secret_book_transfer_customer_account_service_azure_client_test_secret || exports.NOT_FOUND_SECRET
            : environmentKey === 'sandbox' 
              ? process.env.bamboo_secret_book_transfer_customer_account_service_azure_client_sandbox_secret || exports.NOT_FOUND_SECRET
              : process.env.bamboo_secret_book_transfer_customer_account_service_azure_client_development_secret || exports.NOT_FOUND_SECRET,
    launchLogicAccessTokenUrl: environmentKey === 'production'
        ? 'https://lmidp.libertymutual.com/as/token.oauth2?aud=https://launchlogic-service.production.cloud-foundry-prod-us-east-1'
        : environmentKey === 'test'
            ?  'https://test-lmidp.libertymutual.com/as/token.oauth2?aud=https://launchlogic-service.test.cloud-foundry-non-prod-us-east-1'
            : 'https://test-lmidp.libertymutual.com/as/token.oauth2?aud=https://launchlogic-service.development.cloud-foundry-non-prod-us-east-1',
    launchLogicUrl: environmentKey === 'production'
        ? 'https://launchlogicservice.us-east-1.paas.lmig.com'
        : environmentKey === 'test'
            ? 'https://launchlogicservice-test.us-east-1.np.paas.lmig.com'
            : 'https://launchlogicservice-development.us-east-1.np.paas.lmig.com',
    featureManagerScope: 'feature.read',

    //Oauth credentials for ping
    pingClientId: process.env.bamboo_secret_api_gw_client_id || exports.NOT_FOUND_SECRET,
    pingClientSecret: process.env.bamboo_secret_api_gw_client_secret || exports.NOT_FOUND_SECRET,   
    
    //Address Cleanse
    addressCleanseScope: environmentKey === 'production'
      ? '156662e8-d751-4935-8c65-63d319de09bb/.default'
      : '0e27f52c-2dc9-4d00-83d2-a2a301073376/.default', // Address Cleanse Scope
    addressCleanseUrl:
        environmentKey === 'production'
            ? 'https://api.us.lmig.com/reusify/address-cleanse/v1/unparsed-address'
            : 'https://api-tst.us.lmig.com/reusify/address-cleanse/v1/unparsed-address', // Address Cleanse URL
    bulkAddressCleanseUrl:
        environmentKey === 'production'
            ? 'https://api.us.lmig.com/reusify/address-cleanse/v1/multiple-unparsed-address'
            : 'https://api-tst.us.lmig.com/reusify/address-cleanse/v1/multiple-unparsed-address',
    emailServiceUrl: getEmailServiceURL(),

    auditLogServiceUrl: environmentKey === 'production'
        ? 'audit-log-rest-api.us-east-1.paas.lmig.com'
        : environmentKey === 'test'
            ?  'audit-log-rest-api-test.us-east-1.np.paas.lmig.com'
            : 'audit-log-rest-api-development.us-east-1.np.paas.lmig.com',
    getAuditLogServiceIdpParams: (): { [key: string]: string } => {
        return {
            accessTokenUrl:
                process.env.bamboo_audit_log_api_accessTokenUrl ||
                process.env.bamboo_audit_log_api_staging_accessTokenUrl ||
                process.env.bamboo_audit_log_api_test_accessTokenUrl ||
                process.env.bamboo_audit_log_api_dev_accessTokenUrl ||
                '',
            audience:
                process.env.bamboo_audit_log_api_audience ||
                process.env.bamboo_audit_log_api_staging_audience ||
                process.env.bamboo_audit_log_api_test_audience ||
                process.env.bamboo_audit_log_api_dev_audience ||
                '',
            scope:
                process.env.bamboo_audit_log_api_scopes ||
                process.env.bamboo_audit_log_api_staging_scopes ||
                process.env.bamboo_audit_log_api_test_scopes ||
                process.env.bamboo_audit_log_api_dev_scopes ||
                '',
        };
    },
    getPingDeployEnvironment: (): string => {
        if (environmentKey === 'production') {
            return 'production';
        } else {
            return 'nonproduction';
        }
    },
}
