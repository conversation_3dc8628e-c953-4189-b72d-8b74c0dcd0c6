import { LmStatus } from './enums';

export default interface Request {
  searchCriteria: CustomerAccountSearchCriteria;
  customerMetadata?: CustomerMetadata;
  customerPolicies: CustomerPolicy[];
  ecliqAccounts: EcliqAccount[];
  quoteEntryId?: string;
  quoterComments?: string;
  eventSource: string;
  transactionId?: string;
}

export interface CustomerPolicyChangeRequest {
  searchCriteria: CustomerPolicySearchCriteria;
  customerPolicy: CustomerPolicy;
  eventSource: string;
}

export interface CustomerAccountSearchCriteria {
  /**
   * Top to bottom precedence. New lines for different search criteria groups.
   */
  customerAccountId?: string;
  quoteEntryId?: string;

  ecliqAccountNumber?: string;

  priorCarrierPolicyNumber?: string;
  btCode?: string | number;

  customerMetadata?: CustomerMetadata;
}

export interface CustomerPolicySearchCriteria {
  customerPolicyId?: string;
}

export interface CustomerPolicy {
  _id?: string;
  btCode?: string | number;
  emailAddress?: string;
  phoneNumber?: string;
  ratingState?: string;
  ecliqAccountNumber?: string;
  btAgencyTransferCode?: number;
  btAgencySubcode?: number;
  priorPolicy?: PriorPolicy;
  transactionId?: string;
  libertyPolicy?: LibertyPolicy;
  aqe?: Aqe;
  salesforce?: salesforce;
  bookAssessment?: BookAssessment;
}

export interface PriorPolicy {
  lineOfBusiness?: string;
  carrier?: string;
  policyNumber?: string; 
}

export interface LibertyPolicy {
  effectiveDate?: Date;
  lineOfBusiness?: string;
  policyNumber?: string;
  premium?: number;
  producer?: string;
  status?: LmStatus;
  lineType?: string; // PL vs SC
  ecliqId: string;
}

export interface CommonData {
  priorExpirationDate?: Date;
  priorPremium?: number;
}

export interface Aqe  extends CommonData {
  status?: string;
  businessType?: string;
  opportunityId?: number;
  nNumber?: string;
}

export interface salesforce extends CommonData {
  policyDetailsId?: string;
  quoteEntryId?: string;
  isManualQuote?: boolean;
  quoterComments?: string;
  policyEffectiveDate?: Date;
}

export interface BookAssessment extends CommonData {
  assessmentId?: number;
  assessmentDate?: Date;
  finalAppetiteDecision?: string;
}

export interface CustomerMetadata {
  btCode: string | number;
  customerName: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  zip: string;
  addressCleanseApplied?: String;
  quoteEntryId?: string;
}

export interface EcliqAccount {
  ecliqAccountNumber: string;
  ecliqAccountCreationDate: Date;
}
