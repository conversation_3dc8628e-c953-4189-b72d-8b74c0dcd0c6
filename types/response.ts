import { CustomerMetadataModel, CustomerPolicyModel } from "../model/customerAccountModel";
import { CustomerAccountSearchCriteria } from "./request";

export default interface Response {
  id?: string;
  success?: string;
  error?: string;
  statusCode?: number;
  searchCriteria?: CustomerAccountSearchCriteria;
  customerMetadata?: CustomerMetadataModel;
  customerPolicies?: CustomerPolicyModel[];
  ecliqAccountInfo?: EcliqAccountInfo[];
}

export interface PolicyInfo {
  id: string;
  error?: string;
}

export interface EcliqAccountInfo {
  id: string | undefined;
  ecliqAccountNumber?: string;
  ecliqAccountCreationDate?: Date | undefined;
  error?: string;
}
