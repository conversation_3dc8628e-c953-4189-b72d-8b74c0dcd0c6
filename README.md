# book-transfer-customer-account-service

The book-transfer-customer-account-service accepts one or more records containing customer metadata and optional policy info,
determines whether to create or update customer accounts, saves the data to a MongoDB collection named "customerAccounts",
and returns a response containing a list of customer account IDs matching up with each record submitted in the request.
The purpose is allow the Book Transfer teams to create/update customer accounts through a shared service instead of each team
implementing their own method for grouping policies under customer accounts.

## Example request

```json
{
    "searchCriteria": {
      "customerAccountId": "66c75f41dc6fdf4cabfc571c"
    },
    "customerMetadata": {
      "btCode": "123456",
      "customerName": "customer name",
      "address1": "address line 1",
      "city": "address line 2",
      "state": "state",
      "zip": "12345"
    },
    "customerPolicies": [
      {
        "_id": "string",
        "btCode": 123,
        "emailAddress": "string",
        "phoneNumber": "string",
        "ratingState": "string",
        "ecliqAccountNumber": "string",
        "btAgencyTransferCode": 1,
        "btAgencySubcode": 2,
        "priorPolicy" : {
            "lineOfBusiness" : "AUTOB",
            "carrier" : "21st Century Casualty Company",
            "policyNumber" : "AZG96623697"
        },
        "libertyPolicy" : {
            "policyNumber" : null,
            "effectiveDate" : "2024-06-24",
            "premium" : null,
            "producer" : null,
            "status" : "APPLICATION RATED",
            "lineOfBusiness" : "AUTOB"
        },
        "aqe": {
          "status": 2,
          "businessType": "AUTOB",
          "id": 123456,
          "nNumber": "n9999999",
          "priorExpirationDate": "2024-06-24",
          "priorPremium": 1000,
        },
        "salesforce": {
          "quoteEntryId": 1234,
          "isManualQuote": true,
          "quoterComments": "test",
          "priorExpirationDate": "2024-06-24",
          "priorPremium": 10000,
        },
        "bookAssessment": {
          "assessmentId": 234,
          "assessmentDate": "2024-12-03",
          "priorExpirationDate": "2024-06-24",
          "priorPremium": 10000,
        }
      }
    ],
    "eCliqAccounts": [
      {
        "eCliqAccountNumber": "********"
      }
    ],
    "eventSource": "ECLIQ"
}
```

## Required fields in the request

searchCriteria - how to look for an existing customer account<br>
customerMetadata - required only for new customer account creation, not updates. When required all fields must be present<br>
eventSource - name of the application calling the service<br>

## Example MongoDB document

The MongoDB document will auto generate an _id field when created.  This _id is the customer account id and is stored as an ObjectId.
Note that the btCode is stored as an integer.  You will also see a few additional date fields in the table such as:

createdDate - the date the customer account was created.<br>
lastUpdateDate - the date anything under the customerAccount was updated<br>
addressLastUpdatedDate - date the customerMetadata object was last updated<br>

#### Customer Account Document

```json
{
  "_id": {
    "$oid": "6734ba2178a4713f9d465a9d"
  },
  "ecliqAccounts": [
    {
      "ecliqAccountNumber": "********",
      "ecliqAccountCreationDate": {
        "$date": "2024-11-13T14:39:29.207Z"
      }
    }
  ],
  "createdDate": {
    "$date": "2024-11-13T14:39:29.207Z"
  },
  "lastUpdatedDate": {
    "$date": "2024-11-13T14:39:29.207Z"
  },
  "customerMetadataMap": {
    "AQE": {
      "customerMetadata": {
        "btCode": 28415,
        "customerName": "TEST",
        "address1": "Test123",
        "address2": "",
        "city": "Texas City",
        "state": "TX",
        "zip": "77510",
        "addressLastUpdatedDate": {
          "$date": "2024-11-13T14:39:29.207Z"
        }
      },
      "createdDate": {
        "$date": "2024-11-13T14:39:29.207Z"
      },
      "lastUpdatedDate": {
        "$date": "2024-11-13T14:39:29.207Z"
      }
    }
  }
}
```

#### CustomerPolicy Document

```json
{
  "_id": {
    "$oid": "6740d6d2708d7878f37e54a3"
  },
  "btCode": 28415,
  "emailAddress": null,
  "phoneNumber": null,
  "ratingState": null,
  "customerAccountId": {
    "$oid": "673f36247de57f0360c5d496"
  },
  "ecliqAccountNumber": "********",
  "priorPolicy": {
    "lineOfBusiness": null,
    "carrier": null,
    "policyNumber": null
  },
  "libertyPolicy": {
    "policyNumber": null,
    "effectiveDate": "2023-11-01",
    "premium": null,
    "producer": null,
    "status": "APPLICATION CREATED",
    "lineOfBusiness": "INMAR"
  },
  "aqe": {
    "status": null,
    "businessType": null,
    "id": null,
    "nNumber": null,
    "priorExpirationDate": null,
    "priorPremium": null,
    "createdDate": {
      "$date": "2024-11-22T19:09:06.770Z"
    },
    "lastUpdatedDate": {
      "$date": "2024-11-22T19:09:06.770Z"
    },
  },
  "salesforce": {
    "quoteEntryId": null,
    "isManualQuote": null,
    "quoterComments": null,
    "policyEffectiveDate": null,
    "priorExpirationDate": null,
    "priorPremium": null,
    "createdDate": {
      "$date": "2024-11-22T19:09:06.770Z"
    },
    "lastUpdatedDate": {
      "$date": "2024-11-22T19:09:06.770Z"
    },
  },
  "bookAssessment": {
    "assessmentId": null,
    "assessmentDate": null,
    "priorExpirationDate": null,
    "priorPremium": null,
    "createdDate": {
      "$date": "2024-11-22T19:09:06.770Z"
    },
    "lastUpdatedDate": {
      "$date": "2024-11-22T19:09:06.770Z"
    },
  },
  "createdDate": {
    "$date": "2024-11-22T19:09:06.770Z"
  },
  "lastUpdatedDate": {
    "$date": "2024-11-22T19:09:06.770Z"
  },
  "lastPolicyEvent": {
    "policyEventDate": {
      "$date": "2024-11-22T19:09:06.770Z"
    },
    "policyEventType": "ECLIQ"
  }
}
```

## Example successful response

The response will will return a 200 status code and success property when it completes processing stating "Completed processing".  The data field will contain the returned data.
The data is an array. This array should contain one object for request object processed by the lambda.
This object contains the customer account id, the searchCriteria used to find the account, and wether or not the individual create or update was successful.

```json
{
    "success": "Completed processing",
    "data": [
        {
            "statusCode": 200,
            "success": "Completed processing",
            "searchCriteria": {
                "customerAccountId": "66c75f41dc6fdf4cabfc571c"
            },
            "id": "66c75f41dc6fdf4cabfc571c"
        }
    ]
}
```

## Example partial success response with errors

When there is a partial failure and service is able to continue processing you will still receive a 200 status code, and you may see some records in the response contain an error property describing the problem that may have occurred preventing that record from being processed.

```json
{
    "success": "Completed processing",
    "data": [
        {
            "statusCode": 200,
            "success": "Completed processing",
            "searchCriteria": {
                "customerAccountId": "66c75f41dc6fdf4cabfc571c"
            },
            "id": "66c75f41dc6fdf4cabfc571c"
        },
        {
            "statusCode": 400,
            "error": "Missing required metadata field(s)",
            "searchCriteria": {
                "customerAccountId": "66c75f41dc6fdf4cabfc571c"
            }
        }
    ]
}
```

## Customer account matching order
1. Customer account id/eCLIQ account number/quote entry id
2. Prior carrier policy number and bt code
3. Customer metadata

Note: this is subject to change for the most up to date matching order go to customerAccountSearch.ts

## Customer policy matching order

### BAT
1. Assessment id or prior carrier LOB and prior carrier policy number
   
### AQE
1. Opportunity id or prior carrier LOB and prior carrier policy number

### Salesforce
1. Policy details id or prior carrier LOB and prior carrier policy number

### eCLIQ
1. eCLIQ id or eCLIQ account number and prior carrier to LM lob mapping

Note: this is subject to change for the most up to date matching order go to addCustomerPolicyHandler.ts

## Example error response

If there is a complete failure then you may receive a 500 status code and a JSON object with just an error field.

```json
{
    error: "There was an error: <error message goes here>"
}
```

## Running Locally
The application stack including the Api Gateway and lambdas can be run locally for development and debugging purposes.

__Required Installs__<br/>
The following resource must be installed and configured in order to successfully build this project locally:
* [AWS CLI](https://forge.lmig.com/wiki/display/ETSPC/Installing+and+Configuring+the+AWS+CLI)
* [AWS CDK CLI](https://docs.aws.amazon.com/cdk/latest/guide/home.html)
* [bamboo-pipeline-patterns](https://github.com/lmigtech/cloudforge-bamboo-pipeline-patterns) >= 1.19.0
<* [Docker](https://docs.docker.com/get-docker/) -- For additional details on why Docker is required, see the _Building and Running the Project Locally_ section below.

## Building and Running the Project Locally

<__Docker Requirement__<br/>
This project uses the [PythonFunction CDK Lambda construct](https://docs.aws.amazon.com/cdk/api/v2/docs/aws-lambda-python-alpha-readme.html) in order to properly package the API Authorizer function. This construct will automatically install "external" dependencies into the Lambda package during CDK synth time. The recommendation is to use this language specific CDK construct to provision any Lambda Functions that require non AWS native dependencies in order to properly function.

__Pre-development steps__<br/>
Your project should be able to synthesize without changes after performing an `npm install` on the project. If you receive errors regarding installed peer dependencies or cdk version mismatches, check out the section below titled "Aligning Locally Defined and 
Peered CDK Dependency Versions".

__Aligning Locally Defined and Peered CDK Dependency Versions__<br/>
One thing to be aware of when leveraging the `swa-cdk-core` library in your project, is that it requires certain CDK construct dependencies to be installed in order to function properly. This can lead to
mismatched CDK construct dependency versions getting installed in your projects Node environment, which could ultimately lead to CDK synth errors that prevent your stack from successfully building / deploying. 
To resolve this issue, perform the following steps after cloning down the project locally.
1. Run `npm run-script install-peerdeps` which will add all peer dependencies to your package.json file
2. Ensure that all dependencies that start with `@aws-cdk` are set to the same release version
3. Run `npm install` to install all project dependencies
   - If it fails with authentication then run `npm login` to authenticate and once logged in then run `npm install`

__Run the project locally__<br/>
1. Run `npm run local` to run the project locally.
   - If it failed to run with `docker not reachable` error on Mac then run this `sudo ln -sf "$HOME/.docker/run/docker.sock" /var/run/docker.sock` to reslove that error.

__During Development__<br/>
_Useful Commands:_

 * `npm run build`                       compile typescript to js
 * `npm run test`                        run the unit tests
 * `cdk ls`                              list all stacks in the app
 * `cdk synth`                           emits the synthesized CloudFormation template to stdout
 * `cdk synth > template.yml`            generates the cft template into a file called template.yml
 * `cdk deploy`                          deploy this stack to your default AWS account/region
 * `cdk diff`                            compare deployed stack with current state
 * `cdk docs`                            open CDK documentation
 * `npm run synth`                       alias for `cdk synth > template.yml`
 * `npm run-script install-peerdeps`      will collect all peer dependencies and add them to the package.json file
 * `npm run local`                       to run the project locally

__Customizing the local environment__<br/>
You can add a `.env.local` file to set your custom stack prefix and any other local environment variables. It is also useful to set `AWS_PROFILE=saml` in order to use the credentials created by the [apikeyretriever](https://forge.lmig.com/wiki/display/ETSPC/SAML+API+Key+Retriever+Utility).  

Below is an example `env.local` file, note that this file should not be committed and is ignored by git.  There are some examples of variables that would be set by the build and deploy pipeline.
```properties
# example .env.local
# CUSTOM VARIABLES
stack_prefix=PREFIX-
AWS_PROFILE=saml

# BUILD VARIABLES
bamboo_forge_artifact_name=brendans-test-cdk-project
bamboo_forge_organization_guid=my-lm-org
bamboo_forge_organization_key=org-name
bamboo_forge_organization_name=Org Name
bamboo_forge_support_email=<EMAIL>
bamboo_forge_troux_deployable_uuid=5D8DAFE2-C2B2-4A88-B0F2-6A56F26932EE
bamboo_secret_papi_index=unit/org/component/env/amazon-web-services-************-us-east-1
bamboo_secret_vault_token_token=vault:v1:abc123

# DEPLOY VARIABLES
bamboo_custom_aws_callerIdentity_account=************
bamboo_forge_environment_key=sandbox
bamboo_forge_deployment_guid=aaaa1111-bb22-cc33-1234-dddddd444444
bamboo_forge_runtime_instance_location_key=us-east-1
```

To deploy from the command line, you will need to:

1. Run the [apikeyretriever](https://forge.lmig.com/wiki/display/ETSPC/SAML+API+Key+Retriever+Utility) for the desired account
1. Add the `.env.local` similar to above with at least the following configured in it:
    1. `stack_prefix` - Adds a prefix to your stack name to avoid conflicts with existing stacks in AWS
    1. `AWS_PROFILE` - The profile to use when deploying the stack, this should be set using the tool from step 1.
    1. `bamboo_forge_deployment_guid` - A deployment guid from any of your environments in the pipeline. This can be found in existing deploy logs.
1. Run `npx cdk deploy <stack name>`
    1. You can run `npx cdk list` to list available stacks to deploy (and validate the impact of your stack prefix)

## Running Tests

You can run the automated tests by running the `npm test` command in the terminal.
You can also run the automated tests with additional logging by running `npm run testlocal`
that prints out the final data of the in-memory MongoDB instance.

## Using Postman to send a request against a deployed API Gateway endpoint

The following Postman settings will allow you to connect to the test environment API.  If you are not able to connect using the
shown URL, then it is possible that the URL may have changed due to the API Gateway resource having been removed and recreated in the AWS stack.
When the resource is recreated it generates a new URL.  You can find the generated URL in the Bamboo deployment logs for each environment.
The URL will appear in the outputs at the tail end of the logs and should be named "ServiceExecuteApiUrl" and may look like this "https://nbd6l2f4w0-vpce-074828fb930397a5a.execute-api.us-east-1.amazonaws.com/test/".  You must also append the API Gateway stage to the end of the URL which is "api/policy".

Method: POST<br>
URL:  https://nbd6l2f4w0-vpce-074828fb930397a5a.execute-api.us-east-1.amazonaws.com/test/api/policy<br>

Auth Type: OAuth 2.0<br>
Add authorization data to: Request Headers<br>
Token Name: <make up your own token name here><br>
Grant Type: Client Credentials<br>
Access Token URL: https://test-lmidp.libertymutual.com/as/token.oauth2?aud=https://book-transfer-customer-account-service.test.amazon-web-services-************-us-east-1<br>
Client ID: <go to Cloud Forge secrets page for book-transfer-customer-account-service and use the “Id” value under “api-gw-client” for the test environment><br>
Client Secret: <go to Cloud Forge secrets page for book-transfer-customer-account-service and use the “Secret” value under “api-gw-client” for the test environment><br>
Scope: read write<br>
Client Authentication: Send as Basic Auth header<br>

(Don’t forget to generate a new Access Token and use it once Authorization settings are configured properly.)

See `Example request` up above for a sample request body.  For the body configure Postman to use the "raw" option and set the type to "JSON".

## Datastore

This project uses a MongoDB collection named customerAccounts in bt-general-mongo.
Currently there is an index on the nested priorCarrierPolicyPolicy property within the JSON data.

## Connecting to the MongoDB collection

You may visit the Cloud Forge secrets page for book-transfer-customer-account-service to get MongoDB credentials for the environment you wish to connect to.  For example, if
you wish to connect to the test environment MongoDB the find the secret named “bt-general-mongo-test” and generate the appropriate credentials for you needs.

## Project Structure

**[/lib/service/authorizer/authorizer.py](lib/service/authorizer/authorizer.py)** - Handler used by Lambda Authorizer<br>
**[/lib/service/runtime/handler.ts](lib/service/runtime/handler.ts)** - Handler used by Lambda<br>
**[/lib/service/runtime/addCustomerPolicy.ts](lib/service/runtime/addCustomerPolicy.ts)** - Handler used by Lambda<br>
**[/lib/service/utility/\*\*](lib/service/utility)** - Utility functions<br>
**[/lib/service/infrastructure.ts](lib/service/infrastructure.ts)** - The stack definition for your lambda & all of its associated resources.<br>
**[/model/\*\*](model/)** - Interfaces for the MongoDB data model<br>
**[/bin/app.ts](bin/app.ts)** - The executable file that CDK will build your stack(s) from.<br>
**[/bin/config.ts](bin/config.ts)** - Modifiable configuration parameters for the deployed stack<br>
**[/bin/deployment.ts](bin/deployment.ts)** - The file that bundles your CDK infrastructure into a stack.<br>
**[/bamboo-specs/\*\*](bamboo-specs/)** - Definiton of your build/deploy plans for the bamboo pipeline.<br>
**[/src/main/deployment/\*\*](src/main/deployment/)** - Configurations for deployment.<br>
**[/test/service/\*\*](test/service/)** - Tests for the service.<br>
**[/test/event/\*\*](test/event/)** - Sample API Gateway Events that can be used to simulate invoking your lambda through api gateway locally<br>
**[/types/\*\*](types/)** - Interfaces.<br>

## Performance Testing
Performance Testing is enabled for addCustomerPolicy service using **Pipeline Enabled Performance Testing(PEPT)**. Apache JMeter is used to create the performance test plan and the test plan is placed in **test/performance** folder as .jmx file. To enable the testing using PEPT, a new property file **user_config property** is created in the **test/performance** folder.

Performance Testing is automatically triggered during the deployment. PEPT also provides the ability to trigger the performance test manually using the API  - https://api.us.lmig.com/pept-management/launch 
**Request Type:** POST
**Header:** x-api-key (Client id from secret apigeecreds-prd-test)
**Body:**
```json
{
    "configURL" : "https://github.com/lmigtech/book-transfer-customer-account-service/raw/main/test/performance/user_config.properties"
}
```
The status of the test execution can be checked using API - https://api.us.lmig.com/pept-management/status/{id}
**Request Type:** GET
**Header:** x-api-key (Client id from secret apigeecreds-prd-test)

Test results are emailed to **automated-tests** slack channel

The documentation on PEPT can be found in the following link: https://quality.lmig.com/docs/products/pept/introduction/what-is-pept
<br>

## Performance expectations
For performance expectations refer to the following document: https://libertymutual.atlassian.net/wiki/spaces/grm/pages/**********/Customer+Account+Service+Performance+Expectations

## Datadog Dashboard
Following is the URL of datadog dashboard to view customer account policy lambdas (Development):
https://app.datadoghq.com/dashboard/9cb-et9-v7c?fromUser=false&refresh_mode=sliding&tpl_var_Environment%5B0%5D=development&tpl_var_Service%5B0%5D=book-transfer-customer-account-service-development&from_ts=*************&to_ts=*************&live=true

Filters on the top can be changed in order to view different service or environment.

## X-Ray Traces
Following is the URL to view X-Ray traces:
https://us-east-1.console.aws.amazon.com/cloudwatch/home?region=us-east-1#xray:traces/query

There are query refiners where lambdas can be filtered down to view the desired result only.
Click on any of the trace to view the details of the trace.

The other way to open X-Ray traces is to open CloudWatch and on the left hand menu, there is a section "X-Ray Traces"
having two links to "Traces" and "Trace Maps"
