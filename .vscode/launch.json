{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
      {
        "name": "Debug Jest All",
        "type": "node",
        "request": "launch",
        "cwd": "${workspaceFolder}",
        "args": [
          "--inspect-brk",
          "${workspaceRoot}/node_modules/jest/bin/jest.js",
          "--runInBand",
          "--config",
          "${workspaceRoot}/jest.config.js"
        ],
        "console": "integratedTerminal",
      },
      {
        /**
          * Attach to SAM API Gateway request when api running with sam local start-api (npm run start command)
          Port 5800 must match the debug port for the sam local start-api command in package.json
          */
        "name": "Attach to SAM API Gateway",
        "type":"node",
        "request": "attach",
        "address": "localhost",
        "port": 5800,
        "localRoot": "${workspaceFolder}",
        "remoteRoot": "/var/task",
        "sourceMaps": true,
        "outFiles": ["${workspaceFolder}/**/*.js"],
        "resolveSourceMapLocations": ["${workspaceFolder}/**/*.js", "!**/node_modules/**"],
        "sourceMapPathOverrides": {
          "/var/task/*": "${workspaceFolder}/*"
        },
      },
      {
        "type": "node",
        "request": "launch",
        "name": "Jest Current File",
        "program": "${workspaceFolder}/node_modules/.bin/jest",
        "args": [
          "${fileBasenameNoExtension}"
        ],
        "console": "integratedTerminal",
        "internalConsoleOptions": "neverOpen",
        "disableOptimisticBPs": true,
        "cwd": "${workspaceFolder}",
        "windows": {
          "program": "${workspaceFolder}/node_modules/jest/bin/jest"
        }
      }
    ]
  }