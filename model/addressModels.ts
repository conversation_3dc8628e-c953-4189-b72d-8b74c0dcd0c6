export class Address {
  addressRequestId: string;
  address: any;
  geocode: any;
  dataQuality: any;
  extendedInformation: any;
  city: string;
  addressLine: string;
  state: string;
  postalCodeInfo: PostalCodeInfo;
  unit: any;
  houseNumber: string;
}

export interface AddressDetails {
  addressLine: string;
  city: string;
  state: string;
  postalCodeInfo: PostalCodeInfo;
  unit: Unit;
  houseNumber: string;
}

export interface Unit {
  unitNumber: string;
  unitType: string;
}

export interface PostalCodeInfo {
  postalCodeClassification: string;
  postalCode: string;
  postalCodeExtension: string;
  postalCodePlus4: string;
  postalCodeHyphenPlus4: string;
}

export interface MultipleUnparsedAddressRequest {
  unparsedAddresses: {
    addressLine: string;
    addressLine2?: string;
    addressLastLine: string;
    countryCode: string;
  }[];
}

export interface UnparsedAddressRequest {
  unparsedAddress: {
    addressLine: string;
    addressLine2?: string;
    addressLastLine: string;
    countryCode: string;
  };
}

export interface ResponseAddress {
  addresses: Address[];
  context: any;
}
