import { ObjectId } from 'mongodb';
import { EventSource, LmStatus } from '../types/enums';
import exp = require('constants');

export default interface CustomerAccountModel {
  _id?: ObjectId;
  ecliqAccounts: EcliqAccountModel[];
  quoteEntryIds?: string[];
  quoterComments?: string;
  customerMetadataMap: CustomerMetadataMap<string, CustomerMetadataModel>;
  /**  
   *  These keys are the concatenation of btcode,customername,address1,city,state,zip of customerMetadata for each source.
   *  The keys used to avoid creation of duplicate customer accounts from the lambda parallel processing
   */
  salesforceCustomerMetadataKey?: string;
  aqeCustomerMetadataKey?: string;
  batCustomerMetadataKey?: string;
  ecliqCustomerMetadataKey?: string;
  perfTestCustomerMetadataKey?: string;
  lastUpdatedDate: Date;
  createdDate: Date;
}

export interface CustomerMetadata {
  btCode: number;
  customerName: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  zip: string;
  addressCleanseApplied?: String;
  addressLastUpdatedDate: Date;
}

export interface EcliqAccountModel {
  ecliqAccountNumber: string;
  ecliqAccountCreationDate: Date;
}

export interface CustomerMetadataMap<key, CustomerMetadataModel> {
  [key: string]: CustomerMetadataModel;
}

export interface CustomerMetadataModel {
  customerMetadata: CustomerMetadata;
  lastUpdatedDate: Date;
  createdDate: Date;
}

export interface CustomerPolicyModel {
  _id?: ObjectId;
  btCode?: number;
  customerAccountId: ObjectId;
  emailAddress?: string;
  phoneNumber?: string;
  ratingState?: string;
  ecliqAccountNumber?: string;
  priorPolicy?: PriorPolicyModel;
  libertyPolicy?: LibertyPolicyModel;
  btAgencyTransferCode?: number;
  btAgencySubcode?: number;
  aqe?: AqeModel;
  salesforce?: SalesforceModel;
  bookAssessment?: BookAssessmentModel;
  transactionId?: string;
  // This flag is used to skip any updates from other sources
  isUpdatedByEcliq?: boolean;
  lastUpdatedDate: Date;
  createdDate: Date;
  /**
   * The last event that happened to the policy
   * This is used to filter out Salesforce events from being 
   * re-processed by customer policies -> Quote Entry in http sink connector
   */
  lastPolicyEvent?: PolicyEventModel;
}

export interface CommonDataModel {
  priorExpirationDate?: Date;
  priorPremium?: number;
  lastUpdatedDate?: Date;
  createdDate?: Date;
}

export interface BookAssessmentModel extends CommonDataModel {
  assessmentId?: number;
  assessmentDate?: Date;
  finalAppetiteDecision?: string;
}

export interface CustomerPolicyWithAccountDataModel extends CustomerPolicyModel {
  customerAccounts?: CustomerAccountModel[]
}

/**
 * A single event for a customer policy
 * a customer policy will have multiple events
 * TODO: save policyEvents into its own collection
 */
export interface PolicyEventModel {
  policyEventDate: Date;
  policyEventType: EventSource;
}

export interface PriorPolicyModel {
  lineOfBusiness?: string;
  carrier?: string;
  policyNumber?: string;
  lastUpdatedSource?: EventSource;
}

export interface LibertyPolicyModel extends CommonDataModel {
  effectiveDate?: Date;
  lineOfBusiness?: string;
  policyNumber?: string;
  premium?: number;
  producer?: string;
  status?: LmStatus;
  lineType?: string;
  ecliqId?:string;
}

export interface AqeModel extends CommonDataModel {
  status?: string;
  businessType?: string;
  opportunityId?: number;
  nNumber?: string;
  // TODO: figure out how we want to store opportunityId, originalXML, dataXML
}

export interface SalesforceModel extends CommonDataModel {
  policyDetailsId?: string;
  quoteEntryId?: string;
  isManualQuote?: boolean;
  policyEffectiveDate?: Date;
}
