import { EventSource, LineType } from "../types/enums";

export class LineOfBusiness {
  static readonly PPKGE = new LineOfBusiness('PKG', LineType.PL, {
    [EventSource.AQE]: ['PPKGE'],
    [EventSource.ECLIQ]: [],
    [EventSource.BAT]: [],
    [EventSource.SALESFORCE]: [],
    [EventSource.PERFTEST]: [],
  });
  // PL
  static readonly AUTOP = new LineOfBusiness('AUTO', LineType.PL, {
    [EventSource.AQE]: ['AUTOP', 'AUTO'],
    [EventSource.ECLIQ]: [],
    [EventSource.BAT]: [],
    [EventSource.SALESFORCE]: [],
    [EventSource.PERFTEST]: [],
  });
  static readonly HOME = new LineOfBusiness('HOME', LineType.PL, {
    [EventSource.AQE]: ['HOME'],
    [EventSource.ECLIQ]: [],
    [EventSource.BAT]: [],
    [EventSource.SALESFORCE]: [],
    [EventSource.PERFTEST]: [],
  });
  static readonly BOAT = new LineOfBusiness('BOAT', LineType.PL, {
    [EventSource.AQE]: ['BOAT'],
    [EventSource.ECLIQ]: [],
    [EventSource.BAT]: [],
    [EventSource.SALESFORCE]: [],
    [EventSource.PERFTEST]: [],
  });
  static readonly DFIRE = new LineOfBusiness('DFIRE', LineType.PL, {
    [EventSource.AQE]: ['DFIRE', 'FIRE'],
    [EventSource.ECLIQ]: [],
    [EventSource.BAT]: [],
    [EventSource.SALESFORCE]: [],
    [EventSource.PERFTEST]: [],
  });
  static readonly UMBRP = new LineOfBusiness('UMBRP', LineType.PL, {
    [EventSource.AQE]: ['UMBRP', 'UMBRELLA'],
    [EventSource.ECLIQ]: [],
    [EventSource.BAT]: [],
    [EventSource.SALESFORCE]: [],
    [EventSource.PERFTEST]: [],
  });
  static readonly MTR = new LineOfBusiness('MTR', LineType.PL, {
    [EventSource.AQE]: ['MTR'],
    [EventSource.ECLIQ]: [],
    [EventSource.BAT]: [],
    [EventSource.SALESFORCE]: [],
    [EventSource.PERFTEST]: [],
  });
  static readonly INMRP = new LineOfBusiness('INMRP', LineType.PL, {
    [EventSource.AQE]: ['INLANDMARINE', 'INMRP'],
    [EventSource.ECLIQ]: [],
    [EventSource.BAT]: [],
    [EventSource.SALESFORCE]: [],
    [EventSource.PERFTEST]: [],
  });
   // SC
  static readonly WORK = new LineOfBusiness('WORK', LineType.SC, {
    [EventSource.AQE]: ['WORK'],
    [EventSource.ECLIQ]: ['WKCMP', 'WORK'],
    [EventSource.BAT]: ['WORK', 'WORKERS COMPENSATION', 'WORKERS COMP', 'WORKER\'S COMP', 'WC', 'WC-S', 'CWCO', 'WORKER\'S COMPENSATION', 'WORKERS COMP A/R'],
    [EventSource.SALESFORCE]: ['WORKERS COMP'],
    [EventSource.PERFTEST]: ['WORK'],
  });
  static readonly AUTOB = new LineOfBusiness('AUTOB', LineType.SC, {
    [EventSource.AQE]: ['AUTOB'],
    [EventSource.ECLIQ]: ['COMMAUTO', 'AUTOB'],
    [EventSource.BAT]: ['AUTOB', 'BUSINESS AUTO', 'AUTOMOBILE', 'COMMERCIAL AUTO', 'BAUT', 'COMMERCIAL AUTOMOBILE', 'COMM AUTO', 
                        'CAUT', 'AUTO (COMMERCIAL)', 'BA', 'BAP', 'CAUT/CAUT', 'COMA', 'FARM/CAUT'],
    [EventSource.SALESFORCE]: ['AUTO'],
    [EventSource.PERFTEST]: ['AUTOB'],
  });
  static readonly BOND = new LineOfBusiness('BOND', LineType.SC, {
    [EventSource.AQE]: [],
    [EventSource.ECLIQ]: [],
    [EventSource.BAT]: ['BOND', 'SURETY', 'BONDS MISCELLANEOUS', 'BONDS/CONTRACTOR', 'BONDS/NOTARY', 'COMMERCIAL BOND'],
    [EventSource.SALESFORCE]: [],
    [EventSource.PERFTEST]: [],
  });
  static readonly BOP = new LineOfBusiness('BOP', LineType.SC, {
    [EventSource.AQE]: ['BOP'],
    [EventSource.ECLIQ]: ['BOP'],
    [EventSource.BAT]: ['BOP', 'BUSINESS OWNERS', 'BUSINESS OWNERS POLICY (BOP)', 'BBOP', 'BUSINESS OWNERS POLICY', 
                        'BOP + E&O', 'BOP LIABILITY', 'BOP PROPERTY', 'BOP/GLIA', 'BUSINESSOWNERS POLICY'],
    [EventSource.SALESFORCE]: ['BOP'],
    [EventSource.PERFTEST]: ['BOP'],
  });
  static readonly CGL = new LineOfBusiness('CGL', LineType.SC, {
    [EventSource.AQE]: ['CGL'],
    [EventSource.ECLIQ]: ['CGL'],
    [EventSource.BAT]: ['CGL', 'GL', 'GENERAL LIABILITY', 'CGLI', 'GL-S', 'GENL LIABILITY', 'GENERAL LIABILITY - OCCURRENCE', 'CASUALTY',
                        'COMMERCIAL CYBER AND PRIVACY LIABILITY', 'CYBER', 'GENL', 'GLIA', 'LGL', 'LIABILITY', 'MANAGEMENT LIABILITY', 'PROFESSIONAL LIABILITY' ],
    [EventSource.SALESFORCE]: ['GENERAL LIABILITY', 'PROFESSIONAL LIABILITY'],
    [EventSource.PERFTEST]: ['CGL'],
  });
  static readonly PROP = new LineOfBusiness('PROP', LineType.SC, {
    [EventSource.AQE]: ['PROP'],
    [EventSource.ECLIQ]: ['COMMPROP', 'PROPC'],
    [EventSource.BAT]: ['PROP', 'PROPERTY', 'PROPC', 'COMMERCIAL PROPERTY', 'COMMERCIAL PRPTY', 'CP', 'CPP'],
    [EventSource.SALESFORCE]: ['PROPERTY'],
    [EventSource.PERFTEST]: ['PROP'],
  });
  static readonly UMBRC = new LineOfBusiness('UMBRC', LineType.SC, {
    [EventSource.AQE]: ['UMBRC'],
    [EventSource.ECLIQ]: ['UMBRC'],
    [EventSource.BAT]: ['UMBRC', 'EXCESS/UMBRELLA', 'EXCESS/UMB', 'CUMBR', 'COMMERCIAL UMBRELLA', 'UMBRELLA(C)', 'CUMB',
                        'AGUMB', 'CMUMB', 'COMMERCIAL UMBRELLA', 'EXCESS LIABILITY', 'UMB', 'UMBC', 'UMBRELLA', 'UMBRELLA - COMM'],
    [EventSource.SALESFORCE]: ['EXCESS/UMB'],
    [EventSource.PERFTEST]: ['UMBRC'],
  });
  static readonly CRIM = new LineOfBusiness('CRIM', LineType.SC, {
    [EventSource.AQE]: ['CRIM'],
    [EventSource.ECLIQ]: ['CRIM'],
    [EventSource.BAT]: ['CRIM', 'COMMERCIAL CRIME','CRIME'],
    [EventSource.SALESFORCE]: ['COMMERCIAL CRIME'],
    [EventSource.PERFTEST]: ['CRIM'],
  });
  static readonly CPKGE = new LineOfBusiness('CPKGE', LineType.SC, {
    [EventSource.AQE]: ['CPKGE'],
    [EventSource.ECLIQ]: ['CPKGE'],
    [EventSource.BAT]: ['CPKGE', 'CPKG', 'CPSP', 'PKG', 'PACKAGE', 'COMMERCIAL PACKAGE', 'COMPREHENSIVE BUSINESS PACKAGE (CBP)', 'PCKG', 'PACKAGE (C)', 'COMMERCIAL PKG',
                        'CMPKG', 'COMM PKG', 'COMMERCIAL PCKG', 'CP/GL/PKG', 'PCKG LIAB & PROPERTY', 'PCKG/CRIM', 'PCKG/EQUI', 'PCKG/GENL'],
    [EventSource.SALESFORCE]: ['PACKAGE'],
    [EventSource.PERFTEST]: ['CPKGE'],
  });

  static readonly FARM = new LineOfBusiness('FARM', LineType.SC, {
    [EventSource.AQE]: ['CFRM'],
    [EventSource.ECLIQ]: [],
    [EventSource.BAT]: ['AG AUTO', 'AG LIABILITY', 'AG PACKAGE', 'AGPKG', 'AGRI', 'AGRICULTURE', 'FARM', 'FARM/GLIA'],
    [EventSource.SALESFORCE]: ['FARM'],
    [EventSource.PERFTEST]: ['FARM'],
  });
  
  static readonly INMRC = new LineOfBusiness('INMRC', LineType.SC, {
    [EventSource.AQE]: ['INMRC'],
    [EventSource.ECLIQ]: ['INMRC', 'INMAR'],
    [EventSource.BAT]: ['INMRC', 'INLAND MARINE', 'INMAR', 'INLAND MARINE - COMMERCIAL', 'INLAND MARINE (C)', 'IM', 'MARINE', 
                        'CONTRACTORS EQUIP FLOATER', 'EQFL', 'FARM/EQFL', 'IEQF'],
    [EventSource.SALESFORCE]: ['INLAND MARINE', 'INMAR', 'INMRC', 'IM', 'MARINE'],
    [EventSource.PERFTEST]: ['INMRC'],
  });

  private constructor(
    public readonly displayName: string,
    public readonly lineType: string,
    public readonly mappings: {
      [key in EventSource]: string[];
    },
  ) {
    if (!(EventSource.AQE in mappings)) {
      throw new Error(`${displayName} missing OPPORTUNITY mapping`);
    }
    if (!(EventSource.ECLIQ in mappings)) {
      throw new Error(`${displayName} missing ECLIQ_EVENT mapping`);
    }
    if (!(EventSource.BAT in mappings)) {
      throw new Error(`${displayName} missing BAT mapping`);
    }
    if (!(EventSource.SALESFORCE in mappings)) {
      throw new Error(`${displayName} missing SALESFORCE mapping`);
    }
  }

  toString(): string {
    return this.displayName;
  }

  static fromString(value: string, eventSource: EventSource): string {
    if (!value) {
      return value;
    }

    const lob = LineOfBusiness.values().find((lob) => lob.mappings[eventSource].includes(value.toUpperCase()));
    return lob?.displayName || value;
  }

  static values(): LineOfBusiness[] {
    return [
      LineOfBusiness.PPKGE,
      LineOfBusiness.AUTOP,
      LineOfBusiness.HOME,
      LineOfBusiness.BOAT,
      LineOfBusiness.DFIRE,
      LineOfBusiness.UMBRP,
      LineOfBusiness.MTR,
      LineOfBusiness.INMRP,
      LineOfBusiness.WORK,
      LineOfBusiness.AUTOB,
      LineOfBusiness.BOP,
      LineOfBusiness.CGL,
      LineOfBusiness.PROP,
      LineOfBusiness.UMBRC,
      LineOfBusiness.CRIM,
      LineOfBusiness.CPKGE,
      LineOfBusiness.FARM,
      LineOfBusiness.INMRC,
      LineOfBusiness.BOND,
    ];
  }
}
