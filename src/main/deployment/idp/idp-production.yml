---
idp:
  - provider: azure
    applications:
      - name: book-transfer-customer-account-service-azure-client
        client: true
        email_address: Quo<PERSON><EMAIL>
  - type: oauth
    resources:
      # server configuration
      - type: api
        name: book-transfer-customer-account-service
        external_facing: false
        allowed_scopes:
          - read
          - write
        allowed_clients:
          # self
          - artifact_guid: e2a85e6d-a71d-4f88-a0cb-e5ab31cb75b4
            description: Book Transfer Customer Account Service
            environment_keys:
              - production
            scopes:
              - read
              - write
          - artifact_guid: cace2773-8856-4c7f-a93f-4bcf30214526
            description: Appetite Service
            environment_keys:
              - production
            scopes:
              - read
              - write
          - artifact_guid: 90dc83f8-b021-456a-817b-a13262194f08
            description: bt-sf-ecliq-service
            environment_keys:
              - production
            scopes:
              - read
              - write
          - artifact_guid: 7091643b-436b-4cb8-823e-fcf3adcacece
            description: Opportunity Service
            environment_keys:
              - production
            scopes:
              - read
          - artifact_guid: 05c5c6c5-b4f4-4cf9-976a-c1bbdc5af7a4
            description: quoting-bl-adapter
            environment_keys:
              - production
            scopes:
              - read
          - artifact_guid: 569eea21-29df-4db6-a4d7-0e4d2b81ef3e
            description: bt-salesforce-service
            environment_keys:
              - production
            scopes:
              - read
              - write
          - artifact_guid: 387928df-cb52-4e69-bb85-319bdcccacd4
            description: bt-opp-to-quote-entry-service
            environment_keys:
              - production
            scopes:
              - read              
      # client configuration
      - type: client
        name: api-gw-client
